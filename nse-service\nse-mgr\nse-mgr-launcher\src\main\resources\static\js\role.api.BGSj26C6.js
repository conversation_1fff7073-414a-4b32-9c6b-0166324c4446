import{aw as e}from"./index.Ckm1SagX.js";const t="/api/role",o={getPage:o=>e({url:`${t}/page`,method:"get",params:o}),getOptions:()=>e({url:`${t}/options`,method:"get"}),getRoleMenuIds:o=>e({url:`${t}/${o}/menuIds`,method:"get"}),updateRoleMenus:(o,a)=>e({url:`${t}/${o}/menus`,method:"put",data:a}),getFormData:o=>e({url:`${t}/${o}`,method:"get"}),create:o=>e({url:`${t}`,method:"post",data:o}),update:(o,a)=>e({url:`${t}/${o}`,method:"put",data:a}),deleteByIds:o=>e({url:`${t}/${o}`,method:"delete"})};export{o as R};
