/**
 * 授权文件列表分页查询对象
 */
export interface LicenseFilePageQuery extends PageQuery {
  /** 销售订单编号 */
  salesOrderNo: string;
  /** 销售项目名称 */
  salesProjectName: string;
  /** 最终客户名称 */
  finalCustomerName: string;
  /** 服务器机器码 */
  machineCode: string;
  /** 授权类型 */
  licenseType: string;
  /** 申请人 */
  applicant: string;
}

/** 授权绑定申请表单类型 */
export interface ApplyForm {
  /** 授权码 */
  licenseCode: string;
  /** 销售订单编号 */
  salesOrderNo: string;
  /** 销售项目名称 */
  salesProjectName: string;
  /** 最终客户名称 */
  finalCustomerName: string;
  /** 项目销售 */
  sellerName: string;
  /** 服务器机器码 */
  machineCode: string;
  /** 服务器注销码 */
  machineCancelCode?: string;
}

/** 申请成功信息类型 */
export interface ApplySuccessInfo {
  id: string;
  salesOrderNo?: string;
  salesProjectName?: string;
  finalCustomerName?: string;
  sellerName?: string;
  validType?: string;
  terminalLicenseCount?: string;
  licenseType?: string;
  licenseProductInfo?: string;
  licenseCodeInfo?: string;
  machineCode?: string;
  applicant?: string;
  applyDate?: string;
}
