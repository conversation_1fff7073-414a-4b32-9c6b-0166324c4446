10:19:17.147 [main] ERROR c.r.n.m.l.v.CompositeLicenseValidator - [validate,110] - [System|系统] - License验证过程中发生异常: LIC-NSE-License-01011947483058352230400
java.io.FileNotFoundException: proc\uptime (系统找不到指定的路径。)
	at java.base/java.io.FileInputStream.open0(Native Method)
	at java.base/java.io.FileInputStream.open(FileInputStream.java:213)
	at java.base/java.io.FileInputStream.<init>(FileInputStream.java:152)
	at java.base/java.io.FileInputStream.<init>(FileInputStream.java:106)
	at java.base/java.io.FileReader.<init>(FileReader.java:60)
	at com.ruijie.nse.mgr.license.validator.CompositeLicenseValidator.getUptimeSeconds(CompositeLicenseValidator.java:181)
	at com.ruijie.nse.mgr.license.validator.CompositeLicenseValidator.refreshSystemBootTime(CompositeLicenseValidator.java:162)
	at com.ruijie.nse.mgr.license.validator.CompositeLicenseValidator.validate(CompositeLicenseValidator.java:54)
	at com.ruijie.nse.mgr.license.service.LicenseV1Service.validateCurrentLicense(LicenseV1Service.java:99)
	at com.ruijie.nse.mgr.license.service.LicenseV1Service.validateCurrentLicense(LicenseV1Service.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:723)
	at com.ruijie.nse.mgr.license.service.LicenseV1Service$$SpringCGLIB$$0.validateCurrentLicense(<generated>)
	at com.ruijie.nse.mgr.license.config.LicenseVerifyRunner.run(LicenseVerifyRunner.java:40)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:788)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:787)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.ruijie.nse.mgr.launcher.NseMgrApplication.main(NseMgrApplication.java:24)
11:25:54.123 [main] ERROR o.f.c.i.c.DbMigrate - [error,49] - [System|系统] - Migration of schema "mgr" to version "1 - Initial schema" failed! Changes successfully rolled back.
11:25:54.307 [main] ERROR o.s.b.w.e.t.TomcatStarter - [onStartup,60] - [System|系统] - Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

11:25:54.652 [main] ERROR o.s.b.SpringApplication - [reportFailure,857] - [System|系统] - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:621)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.ruijie.nse.mgr.launcher.NseMgrApplication.main(NseMgrApplication.java:28)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:147)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:107)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:516)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:222)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:174)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:169)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:154)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:87)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:266)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:240)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4426)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:772)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:203)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:870)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:437)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:128)
	... 13 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 56 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 70 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 84 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 97 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 119 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1445)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 132 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1436)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 146 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:312)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1527)
	... 157 common frames omitted
Caused by: org.flywaydb.core.internal.exception.FlywayMigrateException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:399)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$applyMigrations$1(DbMigrate.java:283)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.core.internal.command.DbMigrate.applyMigrations(DbMigrate.java:282)
	at org.flywaydb.core.internal.command.DbMigrate.migrateGroup(DbMigrate.java:255)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$migrateAll$0(DbMigrate.java:153)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:77)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.lambda$execute$0(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.database.postgresql.PostgreSQLConnection.lock(PostgreSQLConnection.java:105)
	at org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory.lock(JdbcTableSchemaHistory.java:149)
	at org.flywaydb.core.internal.command.DbMigrate.migrateAll(DbMigrate.java:153)
	at org.flywaydb.core.internal.command.DbMigrate.migrate(DbMigrate.java:104)
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:230)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:213)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:176)
	at org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer.afterPropertiesSet(FlywayMigrationInitializer.java:66)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	... 169 common frames omitted
Caused by: org.flywaydb.core.internal.sqlscript.FlywaySqlScriptException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P07
Error Code : 0
Message    : ERROR: relation "crs_course" already exists
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 16
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.handleException(DefaultSqlScriptExecutor.java:256)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:217)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.execute(DefaultSqlScriptExecutor.java:137)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.executeOnce(SqlMigrationExecutor.java:75)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.lambda$execute$0(SqlMigrationExecutor.java:66)
	at org.flywaydb.core.internal.database.DefaultExecutionStrategy.execute(DefaultExecutionStrategy.java:31)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.execute(SqlMigrationExecutor.java:65)
	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:391)
	... 188 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: relation "crs_course" already exists
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:517)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:434)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:356)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:341)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:317)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:312)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flywaydb.core.internal.jdbc.JdbcTemplate.executeStatement(JdbcTemplate.java:210)
	at org.flywaydb.core.internal.sqlscript.ParsedSqlStatement.execute(ParsedSqlStatement.java:88)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:212)
	... 194 common frames omitted
11:28:33.881 [main] ERROR o.f.c.i.c.DbMigrate - [error,49] - [System|系统] - Migration of schema "mgr" to version "1 - Initial schema" failed! Changes successfully rolled back.
11:28:34.077 [main] ERROR o.s.b.w.e.t.TomcatStarter - [onStartup,60] - [System|系统] - Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

11:28:34.153 [main] ERROR o.s.b.SpringApplication - [reportFailure,857] - [System|系统] - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:621)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.ruijie.nse.mgr.launcher.NseMgrApplication.main(NseMgrApplication.java:28)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:147)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:107)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:516)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:222)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:174)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:169)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:154)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:87)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:266)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:240)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4426)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:772)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:203)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:870)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:437)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:128)
	... 13 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 56 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 70 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 84 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 97 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 119 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1445)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 132 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1436)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 146 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:312)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1527)
	... 157 common frames omitted
Caused by: org.flywaydb.core.internal.exception.FlywayMigrateException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:399)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$applyMigrations$1(DbMigrate.java:283)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.core.internal.command.DbMigrate.applyMigrations(DbMigrate.java:282)
	at org.flywaydb.core.internal.command.DbMigrate.migrateGroup(DbMigrate.java:255)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$migrateAll$0(DbMigrate.java:153)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:77)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.lambda$execute$0(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.database.postgresql.PostgreSQLConnection.lock(PostgreSQLConnection.java:105)
	at org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory.lock(JdbcTableSchemaHistory.java:149)
	at org.flywaydb.core.internal.command.DbMigrate.migrateAll(DbMigrate.java:153)
	at org.flywaydb.core.internal.command.DbMigrate.migrate(DbMigrate.java:104)
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:230)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:213)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:176)
	at org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer.afterPropertiesSet(FlywayMigrationInitializer.java:66)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	... 169 common frames omitted
Caused by: org.flywaydb.core.internal.sqlscript.FlywaySqlScriptException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.handleException(DefaultSqlScriptExecutor.java:256)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:217)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.execute(DefaultSqlScriptExecutor.java:137)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.executeOnce(SqlMigrationExecutor.java:75)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.lambda$execute$0(SqlMigrationExecutor.java:66)
	at org.flywaydb.core.internal.database.DefaultExecutionStrategy.execute(DefaultExecutionStrategy.java:31)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.execute(SqlMigrationExecutor.java:65)
	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:391)
	... 188 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "crs_course_repo_pk"
  详细：Key (id)=(1952270524732747778) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:517)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:434)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:356)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:341)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:317)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:312)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flywaydb.core.internal.jdbc.JdbcTemplate.executeStatement(JdbcTemplate.java:210)
	at org.flywaydb.core.internal.sqlscript.ParsedSqlStatement.execute(ParsedSqlStatement.java:88)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:212)
	... 194 common frames omitted
11:41:19.214 [main] ERROR o.f.c.i.c.DbMigrate - [error,49] - [System|系统] - Migration of schema "mgr" to version "1 - Initial schema" failed! Changes successfully rolled back.
11:41:19.398 [main] ERROR o.s.b.w.e.t.TomcatStarter - [onStartup,60] - [System|系统] - Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

11:41:19.469 [main] ERROR o.s.b.SpringApplication - [reportFailure,857] - [System|系统] - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:621)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.ruijie.nse.mgr.launcher.NseMgrApplication.main(NseMgrApplication.java:28)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:147)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:107)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:516)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:222)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:174)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:169)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:154)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:87)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:266)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:240)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4426)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:772)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:203)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:870)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:437)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:128)
	... 13 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 56 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 70 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 84 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 97 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 119 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1445)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 132 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1436)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 146 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:312)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1527)
	... 157 common frames omitted
Caused by: org.flywaydb.core.internal.exception.FlywayMigrateException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:399)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$applyMigrations$1(DbMigrate.java:283)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.core.internal.command.DbMigrate.applyMigrations(DbMigrate.java:282)
	at org.flywaydb.core.internal.command.DbMigrate.migrateGroup(DbMigrate.java:255)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$migrateAll$0(DbMigrate.java:153)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:77)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.lambda$execute$0(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.database.postgresql.PostgreSQLConnection.lock(PostgreSQLConnection.java:105)
	at org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory.lock(JdbcTableSchemaHistory.java:149)
	at org.flywaydb.core.internal.command.DbMigrate.migrateAll(DbMigrate.java:153)
	at org.flywaydb.core.internal.command.DbMigrate.migrate(DbMigrate.java:104)
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:230)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:213)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:176)
	at org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer.afterPropertiesSet(FlywayMigrationInitializer.java:66)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	... 169 common frames omitted
Caused by: org.flywaydb.core.internal.sqlscript.FlywaySqlScriptException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42601
Error Code : 0
Message    : ERROR: syntax error at or near "IGNORE"
  位置：8
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 664
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.handleException(DefaultSqlScriptExecutor.java:256)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:217)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.execute(DefaultSqlScriptExecutor.java:137)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.executeOnce(SqlMigrationExecutor.java:75)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.lambda$execute$0(SqlMigrationExecutor.java:66)
	at org.flywaydb.core.internal.database.DefaultExecutionStrategy.execute(DefaultExecutionStrategy.java:31)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.execute(SqlMigrationExecutor.java:65)
	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:391)
	... 188 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: syntax error at or near "IGNORE"
  位置：8
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:517)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:434)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:356)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:341)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:317)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:312)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flywaydb.core.internal.jdbc.JdbcTemplate.executeStatement(JdbcTemplate.java:210)
	at org.flywaydb.core.internal.sqlscript.ParsedSqlStatement.execute(ParsedSqlStatement.java:88)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:212)
	... 194 common frames omitted
11:48:21.408 [main] ERROR o.f.c.i.c.DbMigrate - [error,49] - [System|系统] - Migration of schema "mgr" to version "1 - Initial schema" failed! Changes successfully rolled back.
11:48:21.586 [main] ERROR o.s.b.w.e.t.TomcatStarter - [onStartup,60] - [System|系统] - Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

11:48:21.668 [main] ERROR o.s.b.SpringApplication - [reportFailure,857] - [System|系统] - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:621)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.ruijie.nse.mgr.launcher.NseMgrApplication.main(NseMgrApplication.java:28)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:147)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:107)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:516)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:222)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:174)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:169)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:154)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:87)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:266)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:240)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4426)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:772)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:203)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:870)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:437)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:128)
	... 13 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 56 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 70 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 84 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 97 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 119 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1445)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 132 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1436)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 146 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:312)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1527)
	... 157 common frames omitted
Caused by: org.flywaydb.core.internal.exception.FlywayMigrateException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:399)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$applyMigrations$1(DbMigrate.java:283)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.core.internal.command.DbMigrate.applyMigrations(DbMigrate.java:282)
	at org.flywaydb.core.internal.command.DbMigrate.migrateGroup(DbMigrate.java:255)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$migrateAll$0(DbMigrate.java:153)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:77)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.lambda$execute$0(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.database.postgresql.PostgreSQLConnection.lock(PostgreSQLConnection.java:105)
	at org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory.lock(JdbcTableSchemaHistory.java:149)
	at org.flywaydb.core.internal.command.DbMigrate.migrateAll(DbMigrate.java:153)
	at org.flywaydb.core.internal.command.DbMigrate.migrate(DbMigrate.java:104)
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:230)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:213)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:176)
	at org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer.afterPropertiesSet(FlywayMigrationInitializer.java:66)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	... 169 common frames omitted
Caused by: org.flywaydb.core.internal.sqlscript.FlywaySqlScriptException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42703
Error Code : 0
Message    : ERROR: column "id" does not exist
  位置：2017
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.handleException(DefaultSqlScriptExecutor.java:256)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:217)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.execute(DefaultSqlScriptExecutor.java:137)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.executeOnce(SqlMigrationExecutor.java:75)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.lambda$execute$0(SqlMigrationExecutor.java:66)
	at org.flywaydb.core.internal.database.DefaultExecutionStrategy.execute(DefaultExecutionStrategy.java:31)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.execute(SqlMigrationExecutor.java:65)
	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:391)
	... 188 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: column "id" does not exist
  位置：2017
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:517)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:434)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:356)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:341)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:317)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:312)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flywaydb.core.internal.jdbc.JdbcTemplate.executeStatement(JdbcTemplate.java:210)
	at org.flywaydb.core.internal.sqlscript.ParsedSqlStatement.execute(ParsedSqlStatement.java:88)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:212)
	... 194 common frames omitted
11:49:05.714 [main] ERROR o.f.c.i.c.DbMigrate - [error,49] - [System|系统] - Migration of schema "mgr" to version "1 - Initial schema" failed! Changes successfully rolled back.
11:49:05.909 [main] ERROR o.s.b.w.e.t.TomcatStarter - [onStartup,60] - [System|系统] - Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

11:49:05.989 [main] ERROR o.s.b.SpringApplication - [reportFailure,857] - [System|系统] - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:621)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.ruijie.nse.mgr.launcher.NseMgrApplication.main(NseMgrApplication.java:28)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:147)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:107)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:516)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:222)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:174)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:169)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:154)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:87)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:266)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:240)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4426)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:772)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:203)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:870)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:437)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:128)
	... 13 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 56 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 70 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 84 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 97 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 119 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1445)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 132 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1436)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 146 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:312)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1527)
	... 157 common frames omitted
Caused by: org.flywaydb.core.internal.exception.FlywayMigrateException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:399)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$applyMigrations$1(DbMigrate.java:283)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.core.internal.command.DbMigrate.applyMigrations(DbMigrate.java:282)
	at org.flywaydb.core.internal.command.DbMigrate.migrateGroup(DbMigrate.java:255)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$migrateAll$0(DbMigrate.java:153)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:77)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.lambda$execute$0(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.database.postgresql.PostgreSQLConnection.lock(PostgreSQLConnection.java:105)
	at org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory.lock(JdbcTableSchemaHistory.java:149)
	at org.flywaydb.core.internal.command.DbMigrate.migrateAll(DbMigrate.java:153)
	at org.flywaydb.core.internal.command.DbMigrate.migrate(DbMigrate.java:104)
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:230)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:213)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:176)
	at org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer.afterPropertiesSet(FlywayMigrationInitializer.java:66)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	... 169 common frames omitted
Caused by: org.flywaydb.core.internal.sqlscript.FlywaySqlScriptException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 23505
Error Code : 0
Message    : ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 786
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.handleException(DefaultSqlScriptExecutor.java:256)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:217)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.execute(DefaultSqlScriptExecutor.java:137)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.executeOnce(SqlMigrationExecutor.java:75)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.lambda$execute$0(SqlMigrationExecutor.java:66)
	at org.flywaydb.core.internal.database.DefaultExecutionStrategy.execute(DefaultExecutionStrategy.java:31)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.execute(SqlMigrationExecutor.java:65)
	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:391)
	... 188 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "sys_role_menu_unique"
  详细：Key (role_id, menu_id)=(2, 1948294624590172161) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:517)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:434)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:356)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:341)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:317)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:312)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flywaydb.core.internal.jdbc.JdbcTemplate.executeStatement(JdbcTemplate.java:210)
	at org.flywaydb.core.internal.sqlscript.ParsedSqlStatement.execute(ParsedSqlStatement.java:88)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:212)
	... 194 common frames omitted
13:36:08.492 [main] ERROR o.f.c.i.c.DbMigrate - [error,49] - [System|系统] - Migration of schema "mgr" to version "1 - Initial schema" failed! Changes successfully rolled back.
13:36:08.673 [main] ERROR o.s.b.w.e.t.TomcatStarter - [onStartup,60] - [System|系统] - Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

13:36:08.752 [main] ERROR o.s.b.SpringApplication - [reportFailure,857] - [System|系统] - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:621)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.ruijie.nse.mgr.launcher.NseMgrApplication.main(NseMgrApplication.java:28)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:147)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:107)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:516)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:222)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseValidationFilter' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\web\filter\LicenseValidationFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:174)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:169)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:154)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:87)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:266)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:240)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4426)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:772)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:203)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:870)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:437)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:128)
	... 13 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseV1Service' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\LicenseV1Service.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 56 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'compositeLicenseValidator' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\validator\CompositeLicenseValidator.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 70 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventPublishService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\classes\com\ruijie\nse\mgr\common\service\EventPublishService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 84 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'integrationConfig' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\config\IntegrationConfig.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 97 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'eventVerifyRecordsService' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes\com\ruijie\nse\mgr\license\service\EventVerifyRecordsService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 119 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1445)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 132 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'licenseActivationInfoDao' defined in file [C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\classes\com\ruijie\nse\mgr\repository\mapper\LicenseActivationInfoDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1436)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 146 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:312)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1631)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1527)
	... 157 common frames omitted
Caused by: org.flywaydb.core.internal.exception.FlywayMigrateException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:399)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$applyMigrations$1(DbMigrate.java:283)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.core.internal.command.DbMigrate.applyMigrations(DbMigrate.java:282)
	at org.flywaydb.core.internal.command.DbMigrate.migrateGroup(DbMigrate.java:255)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$migrateAll$0(DbMigrate.java:153)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:77)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.lambda$execute$0(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.database.postgresql.PostgreSQLAdvisoryLockTemplate.execute(PostgreSQLAdvisoryLockTemplate.java:60)
	at org.flywaydb.database.postgresql.PostgreSQLConnection.lock(PostgreSQLConnection.java:105)
	at org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory.lock(JdbcTableSchemaHistory.java:149)
	at org.flywaydb.core.internal.command.DbMigrate.migrateAll(DbMigrate.java:153)
	at org.flywaydb.core.internal.command.DbMigrate.migrate(DbMigrate.java:104)
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:230)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:213)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:176)
	at org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer.afterPropertiesSet(FlywayMigrationInitializer.java:66)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	... 169 common frames omitted
Caused by: org.flywaydb.core.internal.sqlscript.FlywaySqlScriptException: Script V1__Initial_schema.sql failed
------------------------------------
SQL State  : 42P10
Error Code : 0
Message    : ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
Location   : db/migration/V1__Initial_schema.sql (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes\db\migration\V1__Initial_schema.sql)
Line       : 876
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.handleException(DefaultSqlScriptExecutor.java:256)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:217)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.execute(DefaultSqlScriptExecutor.java:137)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.executeOnce(SqlMigrationExecutor.java:75)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.lambda$execute$0(SqlMigrationExecutor.java:66)
	at org.flywaydb.core.internal.database.DefaultExecutionStrategy.execute(DefaultExecutionStrategy.java:31)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.execute(SqlMigrationExecutor.java:65)
	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:391)
	... 188 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:517)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:434)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:356)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:341)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:317)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:312)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flywaydb.core.internal.jdbc.JdbcTemplate.executeStatement(JdbcTemplate.java:210)
	at org.flywaydb.core.internal.sqlscript.ParsedSqlStatement.execute(ParsedSqlStatement.java:88)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:212)
	... 194 common frames omitted
13:50:54.196 [http-nio-8090-exec-1] ERROR c.r.n.m.l.s.LicenseV1Service - [importLicense,181] - [1945376569312702465|admin] - 导入License失败
com.ruijie.nse.mgr.license.exception.LicenseException$LicenseValidationException: 该License已经使用过了，请勿重复导入
	at com.ruijie.nse.mgr.license.service.LicenseV1Service.importLicense(LicenseV1Service.java:156)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:723)
	at com.ruijie.nse.mgr.license.service.LicenseV1Service$$SpringCGLIB$$0.importLicense(<generated>)
	at com.ruijie.nse.mgr.license.controller.LicenseV1Controller.update(LicenseV1Controller.java:69)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:257)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:925)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:593)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.common.web.filter.ThreadLocalCleanFilter.doFilter(ThreadLocalCleanFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.mgr.common.interceptors.RequestWrapperFilter.doFilter(RequestWrapperFilter.java:33)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.common.web.filter.LogMDCFilter.doFilter(LogMDCFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.ruijie.nse.mgr.license.web.filter.LicenseValidationFilter.doFilterInternal(LicenseValidationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.ruijie.nse.mgr.sys.filter.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:122)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
13:50:54.199 [http-nio-8090-exec-1] ERROR c.r.n.m.l.e.LicenseExceptionHandler - [handleLicenseValidationException,48] - [1945376569312702465|admin] - License验证异常: 500 - 导入License失败: 该License已经使用过了，请勿重复导入
14:06:50.127 [http-nio-8090-exec-1] ERROR c.r.n.m.p.a.ServerReachableAspect - [validateServerReachability,81] - [1945376569312702465|admin] - 校验用户 1945376569312702465 的 NSE 服务可达性时发生异常
java.lang.NullPointerException: 该作业项目模版不存在，请重新确认
	at java.base/java.util.Objects.requireNonNull(Objects.java:259)
	at com.ruijie.nse.mgr.course.service.ExperimentService.copyLessonExp(ExperimentService.java:379)
	at com.ruijie.nse.mgr.course.service.ExperimentService.createExperiment(ExperimentService.java:126)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:723)
	at com.ruijie.nse.mgr.course.service.ExperimentService$$SpringCGLIB$$0.createExperiment(<generated>)
	at com.ruijie.nse.mgr.course.controller.ExperimentController.createExperiment(ExperimentController.java:64)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ruijie.nse.mgr.py3server.aspect.ServerReachableAspect.validateServerReachability(ServerReachableAspect.java:60)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:638)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:628)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.proceed(AuthorizationManagerBeforeMethodInterceptor.java:268)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.attemptAuthorization(AuthorizationManagerBeforeMethodInterceptor.java:263)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.invoke(AuthorizationManagerBeforeMethodInterceptor.java:196)
	at org.springframework.security.config.annotation.method.configuration.DeferringMethodInterceptor.invoke(DeferringMethodInterceptor.java:44)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at com.ruijie.nse.mgr.course.controller.ExperimentController$$SpringCGLIB$$0.createExperiment(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:257)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.common.web.filter.ThreadLocalCleanFilter.doFilter(ThreadLocalCleanFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.mgr.common.interceptors.RequestWrapperFilter.doFilter(RequestWrapperFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.common.web.filter.LogMDCFilter.doFilter(LogMDCFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.ruijie.nse.mgr.license.web.filter.LicenseValidationFilter.doFilterInternal(LicenseValidationFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.ruijie.nse.mgr.sys.filter.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:122)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
14:06:50.130 [http-nio-8090-exec-1] ERROR c.r.n.c.e.GlobalExceptionHandler - [handleRuntimeException,126] - [1945376569312702465|admin] - 运行时异常: [500]：该作业项目模版不存在，请重新确认
com.ruijie.nse.common.exception.InvalidAccessException: [500]：该作业项目模版不存在，请重新确认
	at com.ruijie.nse.mgr.py3server.aspect.ServerReachableAspect.validateServerReachability(ServerReachableAspect.java:82)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:638)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:628)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.proceed(AuthorizationManagerBeforeMethodInterceptor.java:268)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.attemptAuthorization(AuthorizationManagerBeforeMethodInterceptor.java:263)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.invoke(AuthorizationManagerBeforeMethodInterceptor.java:196)
	at org.springframework.security.config.annotation.method.configuration.DeferringMethodInterceptor.invoke(DeferringMethodInterceptor.java:44)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at com.ruijie.nse.mgr.course.controller.ExperimentController$$SpringCGLIB$$0.createExperiment(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:257)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.common.web.filter.ThreadLocalCleanFilter.doFilter(ThreadLocalCleanFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.mgr.common.interceptors.RequestWrapperFilter.doFilter(RequestWrapperFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.common.web.filter.LogMDCFilter.doFilter(LogMDCFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.ruijie.nse.mgr.license.web.filter.LicenseValidationFilter.doFilterInternal(LicenseValidationFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.ruijie.nse.mgr.sys.filter.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:122)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
14:06:55.266 [http-nio-8090-exec-2] ERROR c.r.n.m.p.a.ServerReachableAspect - [validateServerReachability,81] - [1945376569312702465|admin] - 校验用户 1945376569312702465 的 NSE 服务可达性时发生异常
java.lang.NullPointerException: 该作业项目模版不存在，请重新确认
	at java.base/java.util.Objects.requireNonNull(Objects.java:259)
	at com.ruijie.nse.mgr.course.service.ExperimentService.copyLessonExp(ExperimentService.java:379)
	at com.ruijie.nse.mgr.course.service.ExperimentService.createExperiment(ExperimentService.java:126)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:723)
	at com.ruijie.nse.mgr.course.service.ExperimentService$$SpringCGLIB$$0.createExperiment(<generated>)
	at com.ruijie.nse.mgr.course.controller.ExperimentController.createExperiment(ExperimentController.java:64)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ruijie.nse.mgr.py3server.aspect.ServerReachableAspect.validateServerReachability(ServerReachableAspect.java:60)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:638)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:628)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.proceed(AuthorizationManagerBeforeMethodInterceptor.java:268)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.attemptAuthorization(AuthorizationManagerBeforeMethodInterceptor.java:263)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.invoke(AuthorizationManagerBeforeMethodInterceptor.java:196)
	at org.springframework.security.config.annotation.method.configuration.DeferringMethodInterceptor.invoke(DeferringMethodInterceptor.java:44)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at com.ruijie.nse.mgr.course.controller.ExperimentController$$SpringCGLIB$$0.createExperiment(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:257)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.common.web.filter.ThreadLocalCleanFilter.doFilter(ThreadLocalCleanFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.mgr.common.interceptors.RequestWrapperFilter.doFilter(RequestWrapperFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.common.web.filter.LogMDCFilter.doFilter(LogMDCFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.ruijie.nse.mgr.license.web.filter.LicenseValidationFilter.doFilterInternal(LicenseValidationFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.ruijie.nse.mgr.sys.filter.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:122)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
14:06:55.267 [http-nio-8090-exec-2] ERROR c.r.n.c.e.GlobalExceptionHandler - [handleRuntimeException,126] - [1945376569312702465|admin] - 运行时异常: [500]：该作业项目模版不存在，请重新确认
com.ruijie.nse.common.exception.InvalidAccessException: [500]：该作业项目模版不存在，请重新确认
	at com.ruijie.nse.mgr.py3server.aspect.ServerReachableAspect.validateServerReachability(ServerReachableAspect.java:82)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:638)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:628)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.proceed(AuthorizationManagerBeforeMethodInterceptor.java:268)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.attemptAuthorization(AuthorizationManagerBeforeMethodInterceptor.java:263)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.invoke(AuthorizationManagerBeforeMethodInterceptor.java:196)
	at org.springframework.security.config.annotation.method.configuration.DeferringMethodInterceptor.invoke(DeferringMethodInterceptor.java:44)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at com.ruijie.nse.mgr.course.controller.ExperimentController$$SpringCGLIB$$0.createExperiment(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:257)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.common.web.filter.ThreadLocalCleanFilter.doFilter(ThreadLocalCleanFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.mgr.common.interceptors.RequestWrapperFilter.doFilter(RequestWrapperFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.ruijie.nse.common.web.filter.LogMDCFilter.doFilter(LogMDCFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.ruijie.nse.mgr.license.web.filter.LicenseValidationFilter.doFilterInternal(LicenseValidationFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.ruijie.nse.mgr.sys.filter.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:122)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
14:08:57.177 [scheduling-1] ERROR c.r.n.m.l.v.CompositeLicenseValidator - [validate,110] - [System|系统] - License验证过程中发生异常: LIC-NSE-License-05506EPUUuybApjLNUDlvotvy
java.io.FileNotFoundException: proc\uptime (系统找不到指定的路径。)
	at java.base/java.io.FileInputStream.open0(Native Method)
	at java.base/java.io.FileInputStream.open(FileInputStream.java:213)
	at java.base/java.io.FileInputStream.<init>(FileInputStream.java:152)
	at java.base/java.io.FileInputStream.<init>(FileInputStream.java:106)
	at java.base/java.io.FileReader.<init>(FileReader.java:60)
	at com.ruijie.nse.mgr.license.validator.CompositeLicenseValidator.getUptimeSeconds(CompositeLicenseValidator.java:181)
	at com.ruijie.nse.mgr.license.validator.CompositeLicenseValidator.refreshSystemBootTime(CompositeLicenseValidator.java:162)
	at com.ruijie.nse.mgr.license.validator.CompositeLicenseValidator.validate(CompositeLicenseValidator.java:54)
	at com.ruijie.nse.mgr.license.service.LicenseV1Service.validateCurrentLicense(LicenseV1Service.java:99)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:723)
	at com.ruijie.nse.mgr.license.service.LicenseV1Service$$SpringCGLIB$$0.validateCurrentLicense(<generated>)
	at com.ruijie.nse.mgr.license.config.LicenseSchedulerConfig.scheduledLicenseValidation(LicenseSchedulerConfig.java:45)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
14:18:37.140 [Python3Server-Shutdown-Hook] ERROR c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,76] - [System|系统] - 终止 NSE Server进程失败: PID=10568
java.lang.IllegalStateException: destroy of current process not allowed
	at java.base/java.lang.ProcessHandleImpl.destroyProcess(ProcessHandleImpl.java:369)
	at java.base/java.lang.ProcessHandleImpl.destroy(ProcessHandleImpl.java:387)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at com.ruijie.nse.mgr.py3server.launcher.context.Python3ServerContext.releaseAllHosts(Python3ServerContext.java:68)
	at java.base/java.lang.Thread.run(Thread.java:1583)
14:18:37.191 [SpringApplicationShutdownHook] ERROR c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,76] - [System|系统] - 终止 NSE Server进程失败: PID=10568
java.lang.IllegalStateException: destroy of current process not allowed
	at java.base/java.lang.ProcessHandleImpl.destroyProcess(ProcessHandleImpl.java:369)
	at java.base/java.lang.ProcessHandleImpl.destroy(ProcessHandleImpl.java:387)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at com.ruijie.nse.mgr.py3server.launcher.context.Python3ServerContext.releaseAllHosts(Python3ServerContext.java:68)
	at com.ruijie.nse.mgr.py3server.launcher.manager.GlobalShutdownHookManager.destroy(GlobalShutdownHookManager.java:52)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
