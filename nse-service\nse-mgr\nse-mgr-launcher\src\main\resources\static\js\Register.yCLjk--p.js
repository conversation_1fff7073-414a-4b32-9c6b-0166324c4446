import{d as e,aB as a,o as r,r as o,c as s,db as l,ap as i,g as t,f as d,C as n,m as p,F as m,i as u,w as c,E as g,a0 as f,dc as _,e as x,aC as h,a1 as j,aP as w}from"./index.Ckm1SagX.js";/* empty css             */import{a as v,E as y}from"./form-item.CUMILu98.js";/* empty css               *//* empty css             */import{E as b}from"./checkbox.CyAsOZKA.js";/* empty css                */import{E as C}from"./popper.DpZVcW1M.js";/* empty css              */import{E as V}from"./index.4JfkAhur.js";import{E as k}from"./index.CMOQuMWt.js";import{E}from"./index.CbYeWxT8.js";import{E as q}from"./index.CqmGTqol.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./castArray.Chmjnshw.js";import"./aria.C1IWO_Rd.js";import"./_baseClone.ByRc02qR.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./index.BLy3nyPI.js";import"./event.BwRzfsZt.js";import"./isEqual.CZKKciWh.js";import"./index.BRUQ9gWw.js";import"./index.C0OsJ5su.js";import"./index.Cn1QDWeG.js";import"./focus-trap.Bd_uzvDY.js";import"./index.DJHzyRe5.js";import"./index.Byj-i824.js";const K={"text-center":"","m-0":"","mb-20px":""},M={flex:""},P=["src"],U={class:"flex-y-center w-full gap-10px"},A={"flex-center":"","gap-10px":""},L=e({__name:"Register",emits:["update:modelValue"],setup(e,{emit:L}){const{t:B}=a(),R=L,z=()=>R("update:modelValue","login");r(()=>J());const F=o(),Y=o(!1),D=o(!1),G=o(),H=o(!1),I=o({username:"admin",password:"123456",confirmPassword:"",captchaKey:"",captchaCode:"",rememberMe:!1}),O=s(()=>({username:[{required:!0,trigger:"blur",message:B("login.message.username.required")}],password:[{required:!0,trigger:"blur",message:B("login.message.password.required")},{min:6,message:B("login.message.password.min"),trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:B("login.message.password.required")},{min:6,message:B("login.message.password.min"),trigger:"blur"},{validator:(e,a)=>a===I.value.password,trigger:"blur",message:B("login.message.password.inconformity")}],captchaCode:[{required:!0,trigger:"blur",message:B("login.message.captchaCode.required")}]})),S=o(!1);function J(){S.value=!0,l.getCaptcha().then(e=>{I.value.captchaKey=e.captchaKey,G.value=e.captchaBase64}).finally(()=>S.value=!1)}function N(e){e instanceof KeyboardEvent&&(D.value=e.getModifierState("CapsLock"))}const Q=async()=>{var e;await(null==(e=F.value)?void 0:e.validate()),w.warning("开发中 ...")};return(e,a)=>{const r=i("User"),o=g,s=V,l=v,w=C,L=i("Loading"),R=b,T=k,W=E,X=y,Z=q;return d(),t("div",null,[n("h3",K,m(u(B)("login.reg")),1),p(X,{ref_key:"formRef",ref:F,model:u(I),rules:u(O),size:"large"},{default:c(()=>[p(l,{prop:"username"},{default:c(()=>[p(s,{modelValue:u(I).username,"onUpdate:modelValue":a[0]||(a[0]=e=>u(I).username=e),modelModifiers:{trim:!0},placeholder:u(B)("login.username")},{prefix:c(()=>[p(o,null,{default:c(()=>[p(r)]),_:1})]),_:1},8,["modelValue","placeholder"])]),_:1}),p(w,{visible:u(D),content:u(B)("login.capsLock"),placement:"right"},{default:c(()=>[p(l,{prop:"password"},{default:c(()=>[p(s,{modelValue:u(I).password,"onUpdate:modelValue":a[1]||(a[1]=e=>u(I).password=e),modelModifiers:{trim:!0},placeholder:u(B)("login.password"),type:"password","show-password":"",onKeyup:[N,f(Q,["enter"])]},{prefix:c(()=>[p(o,null,{default:c(()=>[p(u(_))]),_:1})]),_:1},8,["modelValue","placeholder"])]),_:1})]),_:1},8,["visible","content"]),p(w,{visible:u(D),content:u(B)("login.capsLock"),placement:"right"},{default:c(()=>[p(l,{prop:"confirmPassword"},{default:c(()=>[p(s,{modelValue:u(I).confirmPassword,"onUpdate:modelValue":a[2]||(a[2]=e=>u(I).confirmPassword=e),modelModifiers:{trim:!0},placeholder:u(B)("login.message.password.confirm"),type:"password","show-password":"",onKeyup:[N,f(Q,["enter"])]},{prefix:c(()=>[p(o,null,{default:c(()=>[p(u(_))]),_:1})]),_:1},8,["modelValue","placeholder"])]),_:1})]),_:1},8,["visible","content"]),p(l,{prop:"captchaCode"},{default:c(()=>[n("div",M,[p(s,{modelValue:u(I).captchaCode,"onUpdate:modelValue":a[3]||(a[3]=e=>u(I).captchaCode=e),modelModifiers:{trim:!0},placeholder:u(B)("login.captchaCode"),onKeyup:f(Q,["enter"])},{prefix:c(()=>a[5]||(a[5]=[n("div",{class:"i-svg:captcha"},null,-1)])),_:1},8,["modelValue","placeholder"]),n("div",{"cursor-pointer":"",h:"[40px]",w:"[120px]","flex-center":"","ml-10px":"",onClick:J},[u(S)?(d(),x(o,{key:0,class:"is-loading"},{default:c(()=>[p(L)]),_:1})):(d(),t("img",{key:1,"object-cover":"","border-rd-4px":"","p-1px":"",shadow:"[0_0_0_1px_var(--el-border-color)_inset]",src:u(G),alt:"code"},null,8,P))])])]),_:1}),p(l,null,{default:c(()=>[n("div",U,[p(R,{modelValue:u(H),"onUpdate:modelValue":a[4]||(a[4]=e=>h(H)?H.value=e:null)},{default:c(()=>[j(m(u(B)("login.agree")),1)]),_:1},8,["modelValue"]),p(T,{type:"primary",underline:"never"},{default:c(()=>[j(m(u(B)("login.userAgreement")),1)]),_:1})])]),_:1}),p(l,null,{default:c(()=>[p(W,{loading:u(Y),type:"success",class:"w-full",onClick:Q},{default:c(()=>[j(m(u(B)("login.register")),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model","rules"]),n("div",A,[p(Z,{size:"default"},{default:c(()=>[j(m(u(B)("login.haveAccount")),1)]),_:1}),p(T,{type:"primary",underline:"never",onClick:z},{default:c(()=>[j(m(u(B)("login.login")),1)]),_:1})])])}}});export{L as default};
