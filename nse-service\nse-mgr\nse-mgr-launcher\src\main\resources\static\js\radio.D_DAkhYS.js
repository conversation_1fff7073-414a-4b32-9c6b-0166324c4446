import{L as e,aV as a,ba as l,t as o,O as s,r as i,A as d,c as r,ag as n,_ as u,d as t,b as m,g as p,f as v,C as c,X as b,bN as f,i as y,j as g,n as V,aC as k,l as B,a1 as h,F as C,a2 as S,k as x,o as R,y as _,V as G,bs as j,I as E,G as I,q as z}from"./index.Ckm1SagX.js";import{C as F,U}from"./event.BwRzfsZt.js";import{a as w,b as K,u as N,c as L}from"./use-form-common-props.BSYTvb6G.js";import{u as $}from"./index.BRUQ9gWw.js";import{u as q}from"./index.BLy3nyPI.js";import{u as A}from"./index.Dh_vcBr5.js";import{d as O}from"./aria.C1IWO_Rd.js";const X=o({modelValue:{type:[String,Number,Boolean],default:void 0},size:s,disabled:Boolean,label:{type:[String,Number,Boolean],default:void 0},value:{type:[String,Number,Boolean],default:void 0},name:{type:String,default:void 0}}),Y=o({...X,border:Boolean}),D={[U]:o=>e(o)||a(o)||l(o),[F]:o=>e(o)||a(o)||l(o)},H=Symbol("radioGroupKey"),J=(e,a)=>{const l=i(),o=d(H,void 0),s=r(()=>!!o),u=r(()=>n(e.value)?e.label:e.value),t=r({get:()=>s.value?o.modelValue:e.modelValue,set(i){s.value?o.changeEvent(i):a&&a(U,i),l.value.checked=e.modelValue===u.value}}),m=w(r(()=>null==o?void 0:o.size)),p=K(r(()=>null==o?void 0:o.disabled)),v=i(!1),c=r(()=>p.value||s.value&&t.value!==u.value?-1:0);return $({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-radio",ref:"https://element-plus.org/en-US/component/radio.html"},r(()=>s.value&&n(e.value))),{radioRef:l,isGroup:s,radioGroup:o,focus:v,size:m,disabled:p,tabIndex:c,modelValue:t,actualValue:u}},M=t({name:"ElRadio"});var P=u(t({...M,props:Y,emits:D,setup(e,{emit:a}){const l=e,o=m("radio"),{radioRef:s,radioGroup:i,focus:d,size:r,disabled:n,modelValue:u,actualValue:t}=J(l,a);function x(){S(()=>a(F,u.value))}return(e,a)=>{var l;return v(),p("label",{class:V([y(o).b(),y(o).is("disabled",y(n)),y(o).is("focus",y(d)),y(o).is("bordered",e.border),y(o).is("checked",y(u)===y(t)),y(o).m(y(r))])},[c("span",{class:V([y(o).e("input"),y(o).is("disabled",y(n)),y(o).is("checked",y(u)===y(t))])},[b(c("input",{ref_key:"radioRef",ref:s,"onUpdate:modelValue":e=>k(u)?u.value=e:null,class:V(y(o).e("original")),value:y(t),name:e.name||(null==(l=y(i))?void 0:l.name),disabled:y(n),checked:y(u)===y(t),type:"radio",onFocus:e=>d.value=!0,onBlur:e=>d.value=!1,onChange:x,onClick:g(()=>{},["stop"])},null,42,["onUpdate:modelValue","value","name","disabled","checked","onFocus","onBlur","onClick"]),[[f,y(u)]]),c("span",{class:V(y(o).e("inner"))},null,2)],2),c("span",{class:V(y(o).e("label")),onKeydown:g(()=>{},["stop"])},[B(e.$slots,"default",{},()=>[h(C(e.label),1)])],42,["onKeydown"])],2)}}}),[["__file","radio.vue"]]);const Q=o({...X}),T=t({name:"ElRadioButton"});var W=u(t({...T,props:Q,setup(e){const a=e,l=m("radio"),{radioRef:o,focus:s,size:i,disabled:d,modelValue:n,radioGroup:u,actualValue:t}=J(a),S=r(()=>({backgroundColor:(null==u?void 0:u.fill)||"",borderColor:(null==u?void 0:u.fill)||"",boxShadow:(null==u?void 0:u.fill)?`-1px 0 0 0 ${u.fill}`:"",color:(null==u?void 0:u.textColor)||""}));return(e,a)=>{var r;return v(),p("label",{class:V([y(l).b("button"),y(l).is("active",y(n)===y(t)),y(l).is("disabled",y(d)),y(l).is("focus",y(s)),y(l).bm("button",y(i))])},[b(c("input",{ref_key:"radioRef",ref:o,"onUpdate:modelValue":e=>k(n)?n.value=e:null,class:V(y(l).be("button","original-radio")),value:y(t),type:"radio",name:e.name||(null==(r=y(u))?void 0:r.name),disabled:y(d),onFocus:e=>s.value=!0,onBlur:e=>s.value=!1,onClick:g(()=>{},["stop"])},null,42,["onUpdate:modelValue","value","name","disabled","onFocus","onBlur","onClick"]),[[f,y(n)]]),c("span",{class:V(y(l).be("button","inner")),style:x(y(n)===y(t)?y(S):{}),onKeydown:g(()=>{},["stop"])},[B(e.$slots,"default",{},()=>[h(C(e.label),1)])],46,["onKeydown"])],2)}}}),[["__file","radio-button.vue"]]);const Z=o({id:{type:String,default:void 0},size:s,disabled:Boolean,modelValue:{type:[String,Number,Boolean],default:void 0},fill:{type:String,default:""},textColor:{type:String,default:""},name:{type:String,default:void 0},validateEvent:{type:Boolean,default:!0},...q(["ariaLabel"])}),ee=D,ae=t({name:"ElRadioGroup"});var le=u(t({...ae,props:Z,emits:ee,setup(e,{emit:a}){const l=e,o=m("radio"),s=A(),d=i(),{formItem:n}=N(),{inputId:u,isLabeledByFormItem:t}=L(l,{formItemContext:n});R(()=>{const e=d.value.querySelectorAll("[type=radio]"),a=e[0];!Array.from(e).some(e=>e.checked)&&a&&(a.tabIndex=0)});const c=r(()=>l.name||s.value);return _(H,G({...j(l),changeEvent:e=>{a(U,e),S(()=>a(F,e))},name:c})),E(()=>l.modelValue,()=>{l.validateEvent&&(null==n||n.validate("change").catch(e=>O()))}),(e,a)=>(v(),p("div",{id:y(u),ref_key:"radioGroupRef",ref:d,class:V(y(o).b("group")),role:"radiogroup","aria-label":y(t)?void 0:e.ariaLabel||"radio-group","aria-labelledby":y(t)?y(n).labelId:void 0},[B(e.$slots,"default")],10,["id","aria-label","aria-labelledby"]))}}),[["__file","radio-group.vue"]]);const oe=z(P,{RadioButton:W,RadioGroup:le}),se=I(le);I(W);export{oe as E,se as a};
