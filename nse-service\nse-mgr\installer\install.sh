#!/bin/bash
#source "$(dirname "$0")/00__config.sh"

SCRIPTS_ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
bash "$SCRIPTS_ROOT_DIR/Ubuntu_24_04/scripts/00__config.sh" || exit $?
bash "$SCRIPTS_ROOT_DIR/Ubuntu_24_04/scripts/01__env_check.sh" || exit $?
bash "$SCRIPTS_ROOT_DIR/Ubuntu_24_04/scripts/10__jdk_installer.sh" || exit $?
bash "$SCRIPTS_ROOT_DIR/Ubuntu_24_04/scripts/11__python3_check.sh" || exit $?
#bash "$SCRIPTS_ROOT_DIR/Ubuntu_24_04/scripts/12__postgresql_installer.sh" || exit $?
bash "$SCRIPTS_ROOT_DIR/Ubuntu_24_04/scripts/13__postgresql_installer.sh" || exit $?
bash "$SCRIPTS_ROOT_DIR/Ubuntu_24_04/scripts/20__nse_service.sh" || exit $?

echo "🎉 所有脚本执行完成！"