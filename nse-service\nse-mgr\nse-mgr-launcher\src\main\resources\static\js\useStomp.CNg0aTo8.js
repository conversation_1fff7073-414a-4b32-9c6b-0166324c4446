import{r as e,I as t,aR as s}from"./index.Ckm1SagX.js";const n="\n",i="\0";class o{get body(){return!this._body&&this.isBinaryBody&&(this._body=(new TextDecoder).decode(this._binaryBody)),this._body||""}get binaryBody(){return this._binaryBody||this.isBinaryBody||(this._binaryBody=(new TextEncoder).encode(this._body)),this._binaryBody}constructor(e){const{command:t,headers:s,body:n,binaryBody:i,escapeHeaderValues:o,skipContentLengthHeader:r}=e;this.command=t,this.headers=Object.assign({},s||{}),i?(this._binaryBody=i,this.isBinaryBody=!0):(this._body=n||"",this.isBinaryBody=!1),this.escapeHeaderValues=o||!1,this.skipContentLengthHeader=r||!1}static fromRawFrame(e,t){const s={},n=e=>e.replace(/^\s+|\s+$/g,"");for(const i of e.headers.reverse()){i.indexOf(":");const r=n(i[0]);let c=n(i[1]);t&&"CONNECT"!==e.command&&"CONNECTED"!==e.command&&(c=o.hdrValueUnEscape(c)),s[r]=c}return new o({command:e.command,headers:s,binaryBody:e.binaryBody,escapeHeaderValues:t})}toString(){return this.serializeCmdAndHeaders()}serialize(){const e=this.serializeCmdAndHeaders();return this.isBinaryBody?o.toUnit8Array(e,this._binaryBody).buffer:e+this._body+i}serializeCmdAndHeaders(){const e=[this.command];this.skipContentLengthHeader&&delete this.headers["content-length"];for(const t of Object.keys(this.headers||{})){const s=this.headers[t];this.escapeHeaderValues&&"CONNECT"!==this.command&&"CONNECTED"!==this.command?e.push(`${t}:${o.hdrValueEscape(`${s}`)}`):e.push(`${t}:${s}`)}return(this.isBinaryBody||!this.isBodyEmpty()&&!this.skipContentLengthHeader)&&e.push(`content-length:${this.bodyLength()}`),e.join(n)+n+n}isBodyEmpty(){return 0===this.bodyLength()}bodyLength(){const e=this.binaryBody;return e?e.length:0}static sizeOfUTF8(e){return e?(new TextEncoder).encode(e).length:0}static toUnit8Array(e,t){const s=(new TextEncoder).encode(e),n=new Uint8Array([0]),i=new Uint8Array(s.length+t.length+n.length);return i.set(s),i.set(t,s.length),i.set(n,s.length+t.length),i}static marshall(e){return new o(e).serialize()}static hdrValueEscape(e){return e.replace(/\\/g,"\\\\").replace(/\r/g,"\\r").replace(/\n/g,"\\n").replace(/:/g,"\\c")}static hdrValueUnEscape(e){return e.replace(/\\r/g,"\r").replace(/\\n/g,"\n").replace(/\\c/g,":").replace(/\\\\/g,"\\")}}class r{constructor(e,t){this.onFrame=e,this.onIncomingPing=t,this._encoder=new TextEncoder,this._decoder=new TextDecoder,this._token=[],this._initState()}parseChunk(e,t=!1){let s;if(s="string"==typeof e?this._encoder.encode(e):new Uint8Array(e),t&&0!==s[s.length-1]){const e=new Uint8Array(s.length+1);e.set(s,0),e[s.length]=0,s=e}for(let n=0;n<s.length;n++){const e=s[n];this._onByte(e)}}_collectFrame(e){0!==e&&13!==e&&(10!==e?(this._onByte=this._collectCommand,this._reinjectByte(e)):this.onIncomingPing())}_collectCommand(e){if(13!==e)return 10===e?(this._results.command=this._consumeTokenAsUTF8(),void(this._onByte=this._collectHeaders)):void this._consumeByte(e)}_collectHeaders(e){13!==e&&(10!==e?(this._onByte=this._collectHeaderKey,this._reinjectByte(e)):this._setupCollectBody())}_reinjectByte(e){this._onByte(e)}_collectHeaderKey(e){if(58===e)return this._headerKey=this._consumeTokenAsUTF8(),void(this._onByte=this._collectHeaderValue);this._consumeByte(e)}_collectHeaderValue(e){if(13!==e)return 10===e?(this._results.headers.push([this._headerKey,this._consumeTokenAsUTF8()]),this._headerKey=void 0,void(this._onByte=this._collectHeaders)):void this._consumeByte(e)}_setupCollectBody(){const e=this._results.headers.filter(e=>"content-length"===e[0])[0];e?(this._bodyBytesRemaining=parseInt(e[1],10),this._onByte=this._collectBodyFixedSize):this._onByte=this._collectBodyNullTerminated}_collectBodyNullTerminated(e){0!==e?this._consumeByte(e):this._retrievedBody()}_collectBodyFixedSize(e){0!==this._bodyBytesRemaining--?this._consumeByte(e):this._retrievedBody()}_retrievedBody(){this._results.binaryBody=this._consumeTokenAsRaw();try{this.onFrame(this._results)}catch(e){}this._initState()}_consumeByte(e){this._token.push(e)}_consumeTokenAsUTF8(){return this._decoder.decode(this._consumeTokenAsRaw())}_consumeTokenAsRaw(){const e=new Uint8Array(this._token);return this._token=[],e}_initState(){this._results={command:void 0,headers:[],binaryBody:void 0},this._token=[],this._headerKey=void 0,this._onByte=this._collectFrame}}var c,a,h,d,l,u,m,_;(a=c||(c={}))[a.CONNECTING=0]="CONNECTING",a[a.OPEN=1]="OPEN",a[a.CLOSING=2]="CLOSING",a[a.CLOSED=3]="CLOSED",(d=h||(h={}))[d.ACTIVE=0]="ACTIVE",d[d.DEACTIVATING=1]="DEACTIVATING",d[d.INACTIVE=2]="INACTIVE",(u=l||(l={}))[u.LINEAR=0]="LINEAR",u[u.EXPONENTIAL=1]="EXPONENTIAL",(_=m||(m={})).Interval="interval",_.Worker="worker";class b{constructor(e,t=m.Interval,s){this._interval=e,this._strategy=t,this._debug=s,this._workerScript=`\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `}start(e){this.stop(),this.shouldUseWorker()?this.runWorker(e):this.runInterval(e)}stop(){this.disposeWorker(),this.disposeInterval()}shouldUseWorker(){return"undefined"!=typeof Worker&&this._strategy===m.Worker}runWorker(e){this._debug("Using runWorker for outgoing pings"),this._worker||(this._worker=new Worker(URL.createObjectURL(new Blob([this._workerScript],{type:"text/javascript"}))),this._worker.onmessage=t=>e(t.data))}runInterval(e){if(this._debug("Using runInterval for outgoing pings"),!this._timer){const t=Date.now();this._timer=setInterval(()=>{e(Date.now()-t)},this._interval)}}disposeWorker(){this._worker&&(this._worker.terminate(),delete this._worker,this._debug("Outgoing ping disposeWorker"))}disposeInterval(){this._timer&&(clearInterval(this._timer),delete this._timer,this._debug("Outgoing ping disposeInterval"))}}class g{constructor(e){this.versions=e}supportedVersions(){return this.versions.join(",")}protocolVersions(){return this.versions.map(e=>`v${e.replace(".","")}.stomp`)}}g.V1_0="1.0",g.V1_1="1.1",g.V1_2="1.2",g.default=new g([g.V1_2,g.V1_1,g.V1_0]);class p{get connectedVersion(){return this._connectedVersion}get connected(){return this._connected}constructor(e,t,s){this._client=e,this._webSocket=t,this._connected=!1,this._serverFrameHandlers={CONNECTED:e=>{this.debug(`connected to server ${e.headers.server}`),this._connected=!0,this._connectedVersion=e.headers.version,this._connectedVersion===g.V1_2&&(this._escapeHeaderValues=!0),this._setupHeartbeat(e.headers),this.onConnect(e)},MESSAGE:e=>{const t=e.headers.subscription,s=this._subscriptions[t]||this.onUnhandledMessage,n=e,i=this,o=this._connectedVersion===g.V1_2?n.headers.ack:n.headers["message-id"];n.ack=(e={})=>i.ack(o,t,e),n.nack=(e={})=>i.nack(o,t,e),s(n)},RECEIPT:e=>{const t=this._receiptWatchers[e.headers["receipt-id"]];t?(t(e),delete this._receiptWatchers[e.headers["receipt-id"]]):this.onUnhandledReceipt(e)},ERROR:e=>{this.onStompError(e)}},this._counter=0,this._subscriptions={},this._receiptWatchers={},this._partialData="",this._escapeHeaderValues=!1,this._lastServerActivityTS=Date.now(),this.debug=s.debug,this.stompVersions=s.stompVersions,this.connectHeaders=s.connectHeaders,this.disconnectHeaders=s.disconnectHeaders,this.heartbeatIncoming=s.heartbeatIncoming,this.heartbeatOutgoing=s.heartbeatOutgoing,this.splitLargeFrames=s.splitLargeFrames,this.maxWebSocketChunkSize=s.maxWebSocketChunkSize,this.forceBinaryWSFrames=s.forceBinaryWSFrames,this.logRawCommunication=s.logRawCommunication,this.appendMissingNULLonIncoming=s.appendMissingNULLonIncoming,this.discardWebsocketOnCommFailure=s.discardWebsocketOnCommFailure,this.onConnect=s.onConnect,this.onDisconnect=s.onDisconnect,this.onStompError=s.onStompError,this.onWebSocketClose=s.onWebSocketClose,this.onWebSocketError=s.onWebSocketError,this.onUnhandledMessage=s.onUnhandledMessage,this.onUnhandledReceipt=s.onUnhandledReceipt,this.onUnhandledFrame=s.onUnhandledFrame}start(){const e=new r(e=>{const t=o.fromRawFrame(e,this._escapeHeaderValues);this.logRawCommunication||this.debug(`<<< ${t}`);(this._serverFrameHandlers[t.command]||this.onUnhandledFrame)(t)},()=>{this.debug("<<< PONG")});this._webSocket.onmessage=t=>{if(this.debug("Received data"),this._lastServerActivityTS=Date.now(),this.logRawCommunication){const e=t.data instanceof ArrayBuffer?(new TextDecoder).decode(t.data):t.data;this.debug(`<<< ${e}`)}e.parseChunk(t.data,this.appendMissingNULLonIncoming)},this._webSocket.onclose=e=>{this.debug(`Connection closed to ${this._webSocket.url}`),this._cleanUp(),this.onWebSocketClose(e)},this._webSocket.onerror=e=>{this.onWebSocketError(e)},this._webSocket.onopen=()=>{const e=Object.assign({},this.connectHeaders);this.debug("Web Socket Opened..."),e["accept-version"]=this.stompVersions.supportedVersions(),e["heart-beat"]=[this.heartbeatOutgoing,this.heartbeatIncoming].join(","),this._transmit({command:"CONNECT",headers:e})}}_setupHeartbeat(e){if(e.version!==g.V1_1&&e.version!==g.V1_2)return;if(!e["heart-beat"])return;const[t,s]=e["heart-beat"].split(",").map(e=>parseInt(e,10));if(0!==this.heartbeatOutgoing&&0!==s){const e=Math.max(this.heartbeatOutgoing,s);this.debug(`send PING every ${e}ms`),this._pinger=new b(e,this._client.heartbeatStrategy,this.debug),this._pinger.start(()=>{this._webSocket.readyState===c.OPEN&&(this._webSocket.send(n),this.debug(">>> PING"))})}if(0!==this.heartbeatIncoming&&0!==t){const e=Math.max(this.heartbeatIncoming,t);this.debug(`check PONG every ${e}ms`),this._ponger=setInterval(()=>{const t=Date.now()-this._lastServerActivityTS;t>2*e&&(this.debug(`did not receive server activity for the last ${t}ms`),this._closeOrDiscardWebsocket())},e)}}_closeOrDiscardWebsocket(){this.discardWebsocketOnCommFailure?(this.debug("Discarding websocket, the underlying socket may linger for a while"),this.discardWebsocket()):(this.debug("Issuing close on the websocket"),this._closeWebsocket())}forceDisconnect(){this._webSocket&&(this._webSocket.readyState!==c.CONNECTING&&this._webSocket.readyState!==c.OPEN||this._closeOrDiscardWebsocket())}_closeWebsocket(){this._webSocket.onmessage=()=>{},this._webSocket.close()}discardWebsocket(){var e,t;"function"!=typeof this._webSocket.terminate&&(e=this._webSocket,t=e=>this.debug(e),e.terminate=function(){const s=()=>{};this.onerror=s,this.onmessage=s,this.onopen=s;const n=new Date,i=Math.random().toString().substring(2,8),o=this.onclose;this.onclose=e=>{const s=(new Date).getTime()-n.getTime();t(`Discarded socket (#${i})  closed after ${s}ms, with code/reason: ${e.code}/${e.reason}`)},this.close(),null==o||o.call(e,{code:4001,reason:`Quick discarding socket (#${i}) without waiting for the shutdown sequence.`,wasClean:!1})}),this._webSocket.terminate()}_transmit(e){const{command:t,headers:s,body:n,binaryBody:i,skipContentLengthHeader:r}=e,c=new o({command:t,headers:s,body:n,binaryBody:i,escapeHeaderValues:this._escapeHeaderValues,skipContentLengthHeader:r});let a=c.serialize();if(this.logRawCommunication?this.debug(`>>> ${a}`):this.debug(`>>> ${c}`),this.forceBinaryWSFrames&&"string"==typeof a&&(a=(new TextEncoder).encode(a)),"string"==typeof a&&this.splitLargeFrames){let e=a;for(;e.length>0;){const t=e.substring(0,this.maxWebSocketChunkSize);e=e.substring(this.maxWebSocketChunkSize),this._webSocket.send(t),this.debug(`chunk sent = ${t.length}, remaining = ${e.length}`)}}else this._webSocket.send(a)}dispose(){if(this.connected)try{const e=Object.assign({},this.disconnectHeaders);e.receipt||(e.receipt="close-"+this._counter++),this.watchForReceipt(e.receipt,e=>{this._closeWebsocket(),this._cleanUp(),this.onDisconnect(e)}),this._transmit({command:"DISCONNECT",headers:e})}catch(e){this.debug(`Ignoring error during disconnect ${e}`)}else this._webSocket.readyState!==c.CONNECTING&&this._webSocket.readyState!==c.OPEN||this._closeWebsocket()}_cleanUp(){this._connected=!1,this._pinger&&(this._pinger.stop(),this._pinger=void 0),this._ponger&&(clearInterval(this._ponger),this._ponger=void 0)}publish(e){const{destination:t,headers:s,body:n,binaryBody:i,skipContentLengthHeader:o}=e,r=Object.assign({destination:t},s);this._transmit({command:"SEND",headers:r,body:n,binaryBody:i,skipContentLengthHeader:o})}watchForReceipt(e,t){this._receiptWatchers[e]=t}subscribe(e,t,s={}){(s=Object.assign({},s)).id||(s.id="sub-"+this._counter++),s.destination=e,this._subscriptions[s.id]=t,this._transmit({command:"SUBSCRIBE",headers:s});const n=this;return{id:s.id,unsubscribe:e=>n.unsubscribe(s.id,e)}}unsubscribe(e,t={}){t=Object.assign({},t),delete this._subscriptions[e],t.id=e,this._transmit({command:"UNSUBSCRIBE",headers:t})}begin(e){const t=e||"tx-"+this._counter++;this._transmit({command:"BEGIN",headers:{transaction:t}});const s=this;return{id:t,commit(){s.commit(t)},abort(){s.abort(t)}}}commit(e){this._transmit({command:"COMMIT",headers:{transaction:e}})}abort(e){this._transmit({command:"ABORT",headers:{transaction:e}})}ack(e,t,s={}){s=Object.assign({},s),this._connectedVersion===g.V1_2?s.id=e:s["message-id"]=e,s.subscription=t,this._transmit({command:"ACK",headers:s})}nack(e,t,s={}){return s=Object.assign({},s),this._connectedVersion===g.V1_2?s.id=e:s["message-id"]=e,s.subscription=t,this._transmit({command:"NACK",headers:s})}}class y{get webSocket(){var e;return null==(e=this._stompHandler)?void 0:e._webSocket}get disconnectHeaders(){return this._disconnectHeaders}set disconnectHeaders(e){this._disconnectHeaders=e,this._stompHandler&&(this._stompHandler.disconnectHeaders=this._disconnectHeaders)}get connected(){return!!this._stompHandler&&this._stompHandler.connected}get connectedVersion(){return this._stompHandler?this._stompHandler.connectedVersion:void 0}get active(){return this.state===h.ACTIVE}_changeState(e){this.state=e,this.onChangeState(e)}constructor(e={}){this.stompVersions=g.default,this.connectionTimeout=0,this.reconnectDelay=5e3,this._nextReconnectDelay=0,this.maxReconnectDelay=9e5,this.reconnectTimeMode=l.LINEAR,this.heartbeatIncoming=1e4,this.heartbeatOutgoing=1e4,this.heartbeatStrategy=m.Interval,this.splitLargeFrames=!1,this.maxWebSocketChunkSize=8192,this.forceBinaryWSFrames=!1,this.appendMissingNULLonIncoming=!1,this.discardWebsocketOnCommFailure=!1,this.state=h.INACTIVE;const t=()=>{};this.debug=t,this.beforeConnect=t,this.onConnect=t,this.onDisconnect=t,this.onUnhandledMessage=t,this.onUnhandledReceipt=t,this.onUnhandledFrame=t,this.onStompError=t,this.onWebSocketClose=t,this.onWebSocketError=t,this.logRawCommunication=!1,this.onChangeState=t,this.connectHeaders={},this._disconnectHeaders={},this.configure(e)}configure(e){Object.assign(this,e),this.maxReconnectDelay>0&&this.maxReconnectDelay<this.reconnectDelay&&(this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`),this.maxReconnectDelay=this.reconnectDelay)}activate(){const e=()=>{this.active?this.debug("Already ACTIVE, ignoring request to activate"):(this._changeState(h.ACTIVE),this._nextReconnectDelay=this.reconnectDelay,this._connect())};this.state===h.DEACTIVATING?(this.debug("Waiting for deactivation to finish before activating"),this.deactivate().then(()=>{e()})):e()}async _connect(){if(await this.beforeConnect(this),this._stompHandler)return void this.debug("There is already a stompHandler, skipping the call to connect");if(!this.active)return void this.debug("Client has been marked inactive, will not attempt to connect");this.connectionTimeout>0&&(this._connectionWatcher&&clearTimeout(this._connectionWatcher),this._connectionWatcher=setTimeout(()=>{this.connected||(this.debug(`Connection not established in ${this.connectionTimeout}ms, closing socket`),this.forceDisconnect())},this.connectionTimeout)),this.debug("Opening Web Socket...");const e=this._createWebSocket();this._stompHandler=new p(this,e,{debug:this.debug,stompVersions:this.stompVersions,connectHeaders:this.connectHeaders,disconnectHeaders:this._disconnectHeaders,heartbeatIncoming:this.heartbeatIncoming,heartbeatOutgoing:this.heartbeatOutgoing,heartbeatStrategy:this.heartbeatStrategy,splitLargeFrames:this.splitLargeFrames,maxWebSocketChunkSize:this.maxWebSocketChunkSize,forceBinaryWSFrames:this.forceBinaryWSFrames,logRawCommunication:this.logRawCommunication,appendMissingNULLonIncoming:this.appendMissingNULLonIncoming,discardWebsocketOnCommFailure:this.discardWebsocketOnCommFailure,onConnect:e=>{if(this._connectionWatcher&&(clearTimeout(this._connectionWatcher),this._connectionWatcher=void 0),!this.active)return this.debug("STOMP got connected while deactivate was issued, will disconnect now"),void this._disposeStompHandler();this.onConnect(e)},onDisconnect:e=>{this.onDisconnect(e)},onStompError:e=>{this.onStompError(e)},onWebSocketClose:e=>{this._stompHandler=void 0,this.state===h.DEACTIVATING&&this._changeState(h.INACTIVE),this.onWebSocketClose(e),this.active&&this._schedule_reconnect()},onWebSocketError:e=>{this.onWebSocketError(e)},onUnhandledMessage:e=>{this.onUnhandledMessage(e)},onUnhandledReceipt:e=>{this.onUnhandledReceipt(e)},onUnhandledFrame:e=>{this.onUnhandledFrame(e)}}),this._stompHandler.start()}_createWebSocket(){let e;if(this.webSocketFactory)e=this.webSocketFactory();else{if(!this.brokerURL)throw new Error("Either brokerURL or webSocketFactory must be provided");e=new WebSocket(this.brokerURL,this.stompVersions.protocolVersions())}return e.binaryType="arraybuffer",e}_schedule_reconnect(){this._nextReconnectDelay>0&&(this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`),this._reconnector=setTimeout(()=>{this.reconnectTimeMode===l.EXPONENTIAL&&(this._nextReconnectDelay=2*this._nextReconnectDelay,0!==this.maxReconnectDelay&&(this._nextReconnectDelay=Math.min(this._nextReconnectDelay,this.maxReconnectDelay))),this._connect()},this._nextReconnectDelay))}async deactivate(e={}){var t;const s=e.force||!1,n=this.active;let i;if(this.state===h.INACTIVE)return this.debug("Already INACTIVE, nothing more to do"),Promise.resolve();if(this._changeState(h.DEACTIVATING),this._nextReconnectDelay=0,this._reconnector&&(clearTimeout(this._reconnector),this._reconnector=void 0),!this._stompHandler||this.webSocket.readyState===c.CLOSED)return this._changeState(h.INACTIVE),Promise.resolve();{const e=this._stompHandler.onWebSocketClose;i=new Promise((t,s)=>{this._stompHandler.onWebSocketClose=s=>{e(s),t()}})}return s?null==(t=this._stompHandler)||t.discardWebsocket():n&&this._disposeStompHandler(),i}forceDisconnect(){this._stompHandler&&this._stompHandler.forceDisconnect()}_disposeStompHandler(){this._stompHandler&&this._stompHandler.dispose()}publish(e){this._checkConnection(),this._stompHandler.publish(e)}_checkConnection(){if(!this.connected)throw new TypeError("There is no underlying STOMP connection")}watchForReceipt(e,t){this._checkConnection(),this._stompHandler.watchForReceipt(e,t)}subscribe(e,t,s={}){return this._checkConnection(),this._stompHandler.subscribe(e,t,s)}unsubscribe(e,t={}){this._checkConnection(),this._stompHandler.unsubscribe(e,t)}begin(e){return this._checkConnection(),this._stompHandler.begin(e)}commit(e){this._checkConnection(),this._stompHandler.commit(e)}abort(e){this._checkConnection(),this._stompHandler.abort(e)}ack(e,t,s={}){this._checkConnection(),this._stompHandler.ack(e,t,s)}nack(e,t,s={}){this._checkConnection(),this._stompHandler.nack(e,t,s)}}function k(n={}){const i=e(n.brokerURL??""),o=n.reconnectDelay??15e3,r=n.connectionTimeout??1e4,c=n.useExponentialBackoff??!1,a=n.maxReconnectAttempts??3,h=n.maxReconnectDelay??6e4,d=e(!1),l=e(0);let u=null,m=null;const _=new Map,b=e(null);let g=!1,p=!1;const k=()=>{if(b.value&&(b.value.active||b.value.connected))return;if(!i.value)return;const e=s.getAccessToken();if(e){if(b.value){try{b.value.deactivate()}catch(t){}b.value=null}b.value=new y({brokerURL:i.value,connectHeaders:{Authorization:`Bearer ${e}`},debug:n.debug?console.log:()=>{},reconnectDelay:0,heartbeatIncoming:4e3,heartbeatOutgoing:4e3}),b.value.onConnect=()=>{d.value=!0,g=!1,l.value=0,clearTimeout(m),clearTimeout(u)},b.value.onDisconnect=()=>{d.value=!1,g=!1,!p&&l.value<a&&v()},b.value.onWebSocketClose=e=>{d.value=!1,g=!1,p||(1e3===(null==e?void 0:e.code)||1006===(null==e?void 0:e.code)||1008===(null==e?void 0:e.code))&&l.value<a&&v()},b.value.onStompError=e=>{var t,s,n,i;g=!1,((null==(s=null==(t=e.headers)?void 0:t.message)?void 0:s.includes("Unauthorized"))||(null==(n=e.body)?void 0:n.includes("Unauthorized"))||(null==(i=e.body)?void 0:i.includes("Token")))&&(p=!0)}}},v=()=>{if(g||p)return;if(l.value>=a)return;l.value++;const e=c?Math.min(o*Math.pow(2,l.value-1),h):o;u&&clearTimeout(u),u=setTimeout(()=>{d.value||p||g||S()},e)};t(i,(e,t)=>{e!==t&&(b.value&&b.value.connected&&b.value.deactivate(),i.value=e,k())}),k();const S=()=>{if(p=!1,i.value&&!g&&(b.value||k(),b.value))if(b.value.connected)d.value=!0;else{g=!0,clearTimeout(m),m=setTimeout(()=>{!d.value&&g&&(g=!1,!p&&l.value<a&&v())},r);try{b.value.activate()}catch(e){g=!1}}};return{isConnected:d,connect:S,subscribe:(e,t)=>{if(!b.value||!b.value.connected)return"";try{const s=b.value.subscribe(e,t),n=s.id;return _.set(n,s),n}catch(s){return""}},unsubscribe:e=>{const t=_.get(e);t&&(t.unsubscribe(),_.delete(e))},disconnect:()=>{p=!0,u&&(clearTimeout(u),u=null),m&&(clearTimeout(m),m=null);for(const[t,s]of _.entries())try{s.unsubscribe()}catch(e){}if(_.clear(),b.value){try{(b.value.connected||b.value.active)&&b.value.deactivate()}catch(e){}b.value=null}d.value=!1,g=!1,l.value=0}}}export{k as u};
