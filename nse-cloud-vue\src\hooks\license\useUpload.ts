import { ref } from "vue";
import { UploadFile, UploadFiles } from "element-plus";
import { LicenseApplyForm } from "@/api/license-manage/license-code.api";

export default function (formData: LicenseApplyForm) {
  function handleChange(file: UploadFile, fileList: UploadFiles) {
    if (fileList.length > 1) {
      fileList.splice(-1, 1);
      ElMessage.warning("只允许上传一个文件！");
      return;
    }
    const isLt2M = file.size! / 1024 / 1024 < 50;
    if (!isLt2M) {
      fileList.splice(-1, 1);
      ElMessage.warning("文件大小不能超过50M");
      return;
    }
    formData.documentationList = fileList;
  }

  function beforeRemove(file: UploadFile, fileList: UploadFiles) {
    return ElMessageBox.confirm(`确定移除 ${file.name}？`).then(
      () => {
        return true;
      },
      () => {
        return false;
      }
    );
  }

  function handleRemove(file: UploadFile, fileList: UploadFiles) {
    formData.documentationList = fileList;
  }

  return { beforeRemove, handleRemove, handleChange };
}
