package com.ruijie.nse.cloud.license.controller;

import com.ruijie.nse.cloud.common.annotation.AuditLog;
import com.ruijie.nse.cloud.common.annotation.enums.LogModuleEnum;
import com.ruijie.nse.cloud.license.pojo.dto.LicenseFileDto;
import com.ruijie.nse.cloud.license.pojo.dto.LicenseFileQueryDto;
import com.ruijie.nse.cloud.license.pojo.vo.LicenseFileVo;
import com.ruijie.nse.cloud.license.service.LicenseFileBuilderService;
import com.ruijie.nse.cloud.license.service.LicenseFileService;
import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.dto.R;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/license-file")
public class LicenseFileController {
    @Autowired
    private LicenseFileService licenseFileService;

    @Autowired
    private LicenseFileBuilderService licenseFileBuilderService;

    /**
     * 分页查询授权文件记录列表
     *
     * @param queryDto  查询参数
     * @param pageInput 分页参数
     * @return PageOutput<List < LicenseFileVo>>
     */
    @GetMapping("/listPageQuery")
    public R<PageOutput<LicenseFileVo>> listPageQuery(LicenseFileQueryDto queryDto, PageInput pageInput) {
        return R.success(licenseFileService.listPageQuery(queryDto, pageInput));
    }

    /**
     * 授权绑定申请
     *
     * @return LicenseFileVo
     */
    @AuditLog(module = LogModuleEnum.LICENSE, title = "授权绑定申请")
    @PostMapping(value = "/apply")
    public R<LicenseFileVo> apply(@RequestBody LicenseFileDto licenseFileDto) {
        return R.success(licenseFileService.apply(licenseFileDto));
    }

    /**
     * 【授权绑定申请】通过授权码获取机器码
     *
     * @param licenseCode 授权码
     * @return String
     */
    @GetMapping("/getMachineCode")
    public R<String> getMachineCode(@RequestParam("licenseCode") String licenseCode) {
        return R.success(licenseFileService.getMachineCode(licenseCode));
    }

    /**
     * 下载授权文件
     *
     * @param id       主键id
     * @param response 响应体
     */
    @AuditLog(module = LogModuleEnum.LICENSE, title = "下载LICENSE文件")
    @GetMapping("/downloadLicenseFile")
    public void downloadLicenseFile(String id, HttpServletResponse response) throws IOException {
        Path path = licenseFileBuilderService.buildLicenseFile(id);
        // 下载文件
        String fileName = URLEncoder.encode("授权文件.lic", StandardCharsets.UTF_8);
        response.setHeader("content-disposition", "attachment; filename*=UTF-8''" + fileName);
        response.setContentType("application/octet-stream");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            IOUtils.copy(Files.newInputStream(path), outputStream);
        }
    }

    /**
     * 注销码是否正确
     *
     * @param licenseCode 授权码
     * @param cancelCode 注销码
     * @return boolean
     */
    @GetMapping("/isCancelCodeCorrect")
    public R<Boolean> isCancelCodeCorrect(@RequestParam("licenseCode") String licenseCode,
                                          @RequestParam("cancelCode") String cancelCode) {
        return R.success(licenseFileService.isCancelCodeCorrect(licenseCode,cancelCode));
    }

}
