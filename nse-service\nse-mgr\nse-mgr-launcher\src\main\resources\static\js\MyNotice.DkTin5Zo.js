import{d as e,r as t,V as a,o as l,g as i,C as o,m as r,w as s,i as p,aC as n,a0 as m,a1 as d,ap as u,X as c,e as _,h as j,E as f,F as b,f as v}from"./index.Ckm1SagX.js";/* empty css                */import{E as h}from"./dialog.TtqHlFhB.js";import"./overlay.CXfNA60T.js";import{E as g}from"./card.BfhlXze7.js";import{_ as y}from"./index.r55iLQne.js";import{a as w,E as x}from"./table-column.DQa6-hu-.js";import"./checkbox.CyAsOZKA.js";/* empty css                */import"./popper.DpZVcW1M.js";import"./scrollbar.6rbryiG1.js";/* empty css            */import{_ as V}from"./DictLabel.vue_vue_type_script_setup_true_lang.BOw0aI_B.js";import{E as k,a as U}from"./form-item.CUMILu98.js";/* empty css               *//* empty css              */import{N}from"./notice.api.DecNPkys.js";import{E}from"./index.4JfkAhur.js";import{E as C}from"./index.CbYeWxT8.js";import{v as T}from"./directive.C7vihscI.js";import{E as M}from"./index.BPj3iklg.js";import{_ as z}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.BjYFza3j.js";import"./vnode.BkZiIFpS.js";import"./aria.C1IWO_Rd.js";import"./scroll.XdyICIdv.js";import"./focus-trap.Bd_uzvDY.js";import"./index.C0OsJ5su.js";import"./refs.biN0GvkM.js";import"./index.BRUQ9gWw.js";import"./event.BwRzfsZt.js";import"./index.Dh_vcBr5.js";import"./pagination.TL6aFrlm.js";import"./select.DHkh6uhw.js";import"./token.DWNpOE8r.js";import"./strings.By8NVWWL.js";import"./castArray.Chmjnshw.js";import"./isEqual.CZKKciWh.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Byj-i824.js";import"./debounce.YgIwzEIs.js";import"./_baseIteratee.PZHdcgYb.js";import"./index.BLy3nyPI.js";import"./index.B0geSHq7.js";import"./_initCloneObject.BsGr3vVr.js";import"./isPlainObject.Ct3iyI-U.js";import"./index.Cn1QDWeG.js";import"./_baseClone.ByRc02qR.js";import"./index.DJHzyRe5.js";const R={class:"app-container"},S={class:"search-container"},A={key:0,class:"notice-detail__wrapper"},F={class:"notice-detail__meta"},L={class:"ml-2"},P={class:"notice-detail__content"},q=["innerHTML"],D=z(e({name:"MyNotice",inheritAttrs:!1,__name:"MyNotice",setup(e){const z=t(),D=t([]),H=t(!1),I=t(0),K=a({pageNumber:1,pageSize:10}),O=t(!1),X=t(null);function B(){H.value=!0,N.getMyNoticePage(K).then(e=>{D.value=e.rows,I.value=e.total}).finally(()=>{H.value=!1})}return l(()=>{B()}),(e,t)=>{var a;const l=E,G=U,J=u("Search"),W=C,Y=u("Refresh"),Z=k,$=w,Q=V,ee=M,te=x,ae=y,le=g,ie=u("User"),oe=f,re=u("Timer"),se=h,pe=T;return v(),i("div",R,[o("div",S,[r(Z,{ref_key:"queryFormRef",ref:z,model:p(K),inline:!0},{default:s(()=>[r(G,{label:"通知标题",prop:"title"},{default:s(()=>[r(l,{modelValue:p(K).title,"onUpdate:modelValue":t[0]||(t[0]=e=>p(K).title=e),placeholder:"关键字",clearable:"",onKeyup:t[1]||(t[1]=m(e=>B(),["enter"]))},null,8,["modelValue"])]),_:1}),r(G,{class:"search-buttons"},{default:s(()=>[r(W,{type:"primary",onClick:t[2]||(t[2]=e=>B())},{icon:s(()=>[r(J)]),default:s(()=>[t[9]||(t[9]=d(" 搜索 "))]),_:1,__:[9]}),r(W,{onClick:t[3]||(t[3]=e=>(z.value.resetFields(),K.pageNumber=1,void B()))},{icon:s(()=>[r(Y)]),default:s(()=>[t[10]||(t[10]=d(" 重置 "))]),_:1,__:[10]})]),_:1})]),_:1},8,["model"])]),r(le,{shadow:"hover",class:"data-table"},{default:s(()=>[c((v(),_(te,{ref:"dataTableRef",data:p(D),"highlight-current-row":"",class:"data-table__content"},{default:s(()=>[r($,{type:"index",label:"序号",width:"60"}),r($,{label:"通知标题",prop:"title","min-width":"200"}),r($,{align:"center",label:"通知类型",width:"150"},{default:s(e=>[r(Q,{modelValue:e.row.type,"onUpdate:modelValue":t=>e.row.type=t,code:"notice_type"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),r($,{align:"center",label:"发布人",prop:"publisherName",width:"100"}),r($,{align:"center",label:"通知等级",width:"100"},{default:s(e=>[r(Q,{modelValue:e.row.level,"onUpdate:modelValue":t=>e.row.level=t,code:"notice_level"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),r($,{key:"releaseTime",align:"center",label:"发布时间",prop:"publishTime",width:"150"}),r($,{align:"center",label:"发布人",prop:"publisherName",width:"150"}),r($,{align:"center",label:"状态",width:"100"},{default:s(e=>[1==e.row.isRead?(v(),_(ee,{key:0,type:"success"},{default:s(()=>t[11]||(t[11]=[d("已读")])),_:1,__:[11]})):(v(),_(ee,{key:1,type:"info"},{default:s(()=>t[12]||(t[12]=[d("未读")])),_:1,__:[12]}))]),_:1}),r($,{align:"center",fixed:"right",label:"操作",width:"80"},{default:s(e=>[r(W,{type:"primary",size:"small",link:"",onClick:t=>{return a=e.row.id,void N.getDetail(a).then(e=>{O.value=!0,X.value=e});var a}},{default:s(()=>t[13]||(t[13]=[d(" 查看 ")])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[pe,p(H)]]),p(I)>0?(v(),_(ae,{key:0,total:p(I),"onUpdate:total":t[4]||(t[4]=e=>n(I)?I.value=e:null),page:p(K).pageNumber,"onUpdate:page":t[5]||(t[5]=e=>p(K).pageNumber=e),limit:p(K).pageSize,"onUpdate:limit":t[6]||(t[6]=e=>p(K).pageSize=e),onPagination:t[7]||(t[7]=e=>B())},null,8,["total","page","limit"])):j("",!0)]),_:1}),r(se,{modelValue:p(O),"onUpdate:modelValue":t[8]||(t[8]=e=>n(O)?O.value=e:null),title:(null==(a=p(X))?void 0:a.title)??"通知详情",width:"800px","custom-class":"notice-detail"},{default:s(()=>[p(X)?(v(),i("div",A,[o("div",F,[o("span",null,[r(oe,null,{default:s(()=>[r(ie)]),_:1}),d(" "+b(p(X).publisherName),1)]),o("span",L,[r(oe,null,{default:s(()=>[r(re)]),_:1}),d(" "+b(p(X).publishTime),1)])]),o("div",P,[o("div",{innerHTML:p(X).content},null,8,q)])])):j("",!0)]),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-84848100"]]);export{D as default};
