<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
        <el-form-item label="课程名称" prop="courseName">
          <el-input
            v-model="queryParams.courseName"
            placeholder="请输入"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="班级名称" prop="className">
          <el-input
            v-model="queryParams.className"
            placeholder="请输入"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="当前状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="全部"
            clearable
            style="width: 200px"
          >
            <el-option label="上课中" value="上课中" />
            <el-option label="未上课" value="未上课" />
          </el-select>
        </el-form-item>

        <!-- 展开后显示的查询条件 -->
        <template v-if="isExpand">
          <el-form-item label="人数" prop="studentCntRange">
            <el-input
              v-model.number="queryParams.studentCntMin"
              placeholder="请输入"
              clearable
              style="width: 150px"
            />
            <span style="margin: 0 10px">-</span>
            <el-input
              v-model.number="queryParams.studentCntMax"
              placeholder="请输入"
              clearable
              style="width: 150px"
            />
          </el-form-item>

          <el-form-item label="实验个数" prop="expCntRange">
            <el-input
              v-model.number="queryParams.expCntMin"
              placeholder="请输入"
              clearable
              style="width: 150px"
            />
            <span style="margin: 0 10px">-</span>
            <el-input
              v-model.number="queryParams.expCntMax"
              placeholder="请输入"
              clearable
              style="width: 150px"
            />
          </el-form-item>

          <el-form-item label="创建人" prop="createdBy">
            <el-input
              v-model="queryParams.createdBy"
              placeholder="请输入"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <el-form-item label="创建时间" prop="createdDate">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 380px"
            />
          </el-form-item>
        </template>
      </el-form>
      <div class="search-buttons">
        <el-button type="primary" icon="search" @click="handleQuery">查询</el-button>
        <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
        <el-link class="ml-3" type="primary" underline="never" @click="isExpand = !isExpand">
          {{ isExpand ? "收起" : "展开" }}
          <component :is="isExpand ? ArrowUp : ArrowDown" class="w-4 h-4 ml-2" />
        </el-link>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card shadow="hover" class="data-table">
      <div class="data-table__toolbar">
        <div class="data-table__toolbar--actions">
          <el-button
            v-hasPerm="['teaching:add']"
            type="primary"
            icon="CirclePlus"
            @click="handleCreateTeaching"
          >
            新建课程
          </el-button>
          <el-button
            v-hasPerm="['teaching:delete']"
            type="danger"
            plain
            icon="delete"
            :disabled="selectIds.length === 0"
            @click="handleDeleteTeaching()"
          >
            批量删除课程
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="pageData"
        border
        stripe
        highlight-current-row
        class="data-table__content"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" :selectable="(row: TeachingVO) => row.status !== '上课中'" />
        <el-table-column type="index" label="序号" width="70" />
        <el-table-column label="课程名称" prop="courseName" min-width="200">
          <template #default="scope">
            <el-link type="primary" :underline="false" @click="viewExpData(scope.row)">
              {{ scope.row.courseName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="班级" prop="className" width="180" />
        <el-table-column label="当前状态" prop="status" width="100">
          <template #default="scope">
            <span :style="{ color: scope.row.status === '上课中' ? 'red' : '' }">
              {{ scope.row.status }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="人数" prop="studentCnt" width="100" />
        <el-table-column label="实验个数" prop="expCnt" width="100" />
        <el-table-column label="创建人" prop="createdBy" width="120" />
        <el-table-column label="创建时间" prop="createdDate" width="180" />
        <el-table-column label="操作" fixed="right" width="300">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === '未上课'"
              v-hasPerm="['teaching:start']"
              type="primary"
              link
              size="small"
              @click="handleStartCourse(scope.row)"
            >
              上课
            </el-button>
            <el-button
              v-if="scope.row.status === '上课中'"
              v-hasPerm="['teaching:end']"
              type="primary"
              link
              size="small"
              @click="handleEndCourse(scope.row.id)"
            >
              下课
            </el-button>
            <el-button
              v-hasPerm="['teaching:student']"
              type="primary"
              link
              size="small"
              @click="handleStudentManagement(scope.row.id)"
            >
              学生管理
            </el-button>
            <el-button
              v-hasPerm="['teaching:export']"
              type="primary"
              link
              size="small"
              @click="handleExportData(scope.row.id)"
            >
              导出数据
            </el-button>
            <el-button
              v-hasPerm="['teaching:delete']"
              type="primary"
              link
              size="small"
              @click="handleDeleteTeaching(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-if="total > 0"
        v-model:current-page="queryParams.pageNumber"
        v-model:page-size="queryParams.pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 30, 50, 100]"
        class="mt-4"
        @current-change="fetchData"
        @size-change="fetchData"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance } from "element-plus";
import { useRouter } from "vue-router";
import { ArrowUp, ArrowDown } from "@element-plus/icons-vue";
import TeachingAPI from "@/api/teaching/teaching.api";
import type { TeachingPageQuery, TeachingVO } from "@/api/course/types/teaching.types";
import Base64 from "@/utils/base64";

defineOptions({
  name: "Teaching",
  inheritAttrs: false,
});
const router = useRouter();

// 展开/收起状态
const isExpand = ref(false);

// 查询参数
const queryParams = reactive<TeachingPageQuery>({
  pageNumber: 1,
  pageSize: 10,
  courseName: "",
  className: "",
  status: "",
  studentCntMin: undefined,
  studentCntMax: undefined,
  expCntMin: undefined,
  expCntMax: undefined,
  createdBy: "",
  startTime: "",
  endTime: "",
});

// 时间范围
const dateRange = ref<string[]>([]);

// 表格数据
const loading = ref(false);
const pageData = ref<TeachingVO[]>([]);
const total = ref(0);
const selectIds = ref<string[]>([]);

// 表单引用
const queryFormRef = ref<FormInstance>();

// 获取数据
const fetchData = () => {
  loading.value = true;

  // 处理时间范围
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startTime = dateRange.value[0];
    queryParams.endTime = dateRange.value[1];
  } else {
    queryParams.startTime = undefined;
    queryParams.endTime = undefined;
  }

  TeachingAPI.getCoursePage(queryParams).then((data) => {
    pageData.value = data.rows;
    total.value = data.total;
    loading.value = false;
  });
};

// 搜索
const handleQuery = () => {
  queryParams.pageNumber = 1;
  fetchData();
};

// 重置搜索
const handleResetQuery = () => {
  queryFormRef.value?.resetFields();
  dateRange.value = [];
  queryParams.studentCntMin = undefined;
  queryParams.studentCntMax = undefined;
  queryParams.expCntMin = undefined;
  queryParams.expCntMax = undefined;
  queryParams.pageNumber = 1;
  fetchData();
};

// 表格选择变化
const handleSelectionChange = (selection: TeachingVO[]) => {
  selectIds.value = selection.map((item) => item.id);
};

// 新建课程
const handleCreateTeaching = () => {
  router.push("/teaching/create");
};

// 上课操作
const handleStartCourse = (row: TeachingVO) => {
  TeachingAPI.startCourseCheck(row.id).then(res => {
    if (!res) {
      ElMessage.error("无法上课，请稍后重试");
      return;
    }
    if (res.message) {
      ElMessageBox.confirm(res.message, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        if (res.canStart) {
          doStartCourse(row.id)
        }
      }).catch(() => {
      });
      return;
    }
    doStartCourse(row.id)
  })
};

const doStartCourse = (id: string) => {
  TeachingAPI.startCourse(id).then(() => {
    ElMessage.success("已上课");
    fetchData();
  })
}

// 下课操作
const handleEndCourse = (id: string) => {
  ElMessageBox.confirm(`请确认是否结束上课，结束上课后，该班级学生账号将变成非上课状态，每日允许登录系统，使用2小时！`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    TeachingAPI.endCourse(id).then(() => {
      ElMessage.success("已下课");
      fetchData();
    })
  }).catch(() => {
  });
};

// 学生管理
const handleStudentManagement = (id: string) => {
  router.push({
    path: "/teaching/student",
    query: {
      id,
    },
  });
};

// 导出数据
const handleExportData = (id: string) => {
  const baseUrl = import.meta.env.VITE_APP_API_URL || '';
  window.open(`${baseUrl}/api/course/export/${id}`);
};

// 实验数据
const handleExpData = (id: string) => {
  router.push({
    path: "/teaching/experiment-data",
    query: {
      id,
    },
  });
};

// 删除课程
const handleDeleteTeaching = (row?: TeachingVO) => {
  if (row && row.status === '上课中') {
    ElMessage.warning("正在上课的课程不允许删除");
    return;
  }
  const ids = row ? [row.id] : selectIds.value;
  if (ids.length === 0) {
    ElMessage.warning("请选择要删除的数据");
    return;
  }

  ElMessageBox.confirm(
    `此操作将永久该实验课程，并删除该课程下所有学生的实验数据，请确认是否继续？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    TeachingAPI.deleteCourseBatch(ids)
      .then(() => {
        ElMessage.success("删除成功");
        fetchData();
      })
      .catch(() => {
        ElMessage.error("删除失败");
      });
  });
};

// 点击课程名称查看实验数据
const viewExpData = (row: TeachingVO) => {
  router.push({
    path: "/teaching/experiment-data",
    query: {
      info: Base64.encode(
        JSON.stringify({
          courseId: row.id,
          courseName: row.courseName,
          expCnt: row.expCnt,
          createdBy: row.createdBy
        })
      )
    },
  });
};

// 页面加载时获取数据
onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.search-container {
  display: flex;
  justify-content: space-between;
  padding: 18px 16px 0;
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;

  .search-buttons {
    min-width: 250px;
    margin-left: auto;
  }
}

.data-table {
  .data-table__toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    &--actions,
    &--tools {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  .data-table__content {
    margin-bottom: 16px;
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 12px;
  }

  .search-container {
    padding: 16px;

    :deep(.el-form--inline) {
      .el-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .data-table__toolbar {
    flex-direction: column;
    align-items: stretch;

    &--actions,
    &--tools {
      justify-content: center;
    }
  }
}
</style>
