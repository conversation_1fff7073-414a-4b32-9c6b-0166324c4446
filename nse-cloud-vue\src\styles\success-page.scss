/* 成功页面通用样式 */
.success-page {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.success-header {
  text-align: center;
  margin-bottom: 32px;
}

.success-result {
  padding: 0;
  background: transparent;
}

.success-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--el-color-success-light-9) 0%, var(--el-color-success-light-7) 100%);
  margin-bottom: 16px;
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
  }
}

.info-card {
  margin-bottom: 32px;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.info-section {
  margin-bottom: 24px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 16px 0;
  padding: 8px 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  border-left: 4px solid var(--el-color-primary);
  border-radius: 4px;
}

.info-label {
  font-weight: 600;
  color: var(--el-text-color-regular);
  background-color: var(--el-fill-color-light);
}

.info-value {
  font-weight: 500;
}

.code-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  background-color: var(--el-fill-color-light);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
}

.action-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 0;
}

.download-btn {
  min-width: 160px;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(64, 128, 255, 0.3);
  transition: all 0.3s ease;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 128, 255, 0.4);
}

.back-btn {
  min-width: 120px;
  height: 44px;
  font-size: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  transform: translateY(-1px);
}

.mr-2 {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .success-page {
    padding: 16px;
  }
  
  .el-descriptions {
    --el-descriptions-item-bordered-label-background: var(--el-fill-color-light);
  }
  
  .basic-info,
  .license-info,
  .apply-info {
    --el-descriptions-table-border: 1px solid var(--el-border-color-lighter);
  }
  
  .basic-info .el-descriptions__body,
  .license-info .el-descriptions__body,
  .apply-info .el-descriptions__body {
    display: block;
  }
  
  .basic-info .el-descriptions__body .el-descriptions__row,
  .license-info .el-descriptions__body .el-descriptions__row,
  .apply-info .el-descriptions__body .el-descriptions__row {
    display: block;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .basic-info .el-descriptions__body .el-descriptions__row:last-child,
  .license-info .el-descriptions__body .el-descriptions__row:last-child,
  .apply-info .el-descriptions__body .el-descriptions__row:last-child {
    border-bottom: none;
  }
  
  .basic-info .el-descriptions__cell,
  .license-info .el-descriptions__cell,
  .apply-info .el-descriptions__cell {
    display: block;
    width: 100% !important;
    padding: 12px 16px;
    border-right: none !important;
  }
  
  .basic-info .el-descriptions__cell.is-bordered-label,
  .license-info .el-descriptions__cell.is-bordered-label,
  .apply-info .el-descriptions__cell.is-bordered-label {
    border-bottom: 1px solid var(--el-border-color-lighter);
    background-color: var(--el-fill-color-light);
    font-weight: 600;
  }
  
  .action-footer {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
  
  .download-btn,
  .back-btn {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .success-page {
    padding: 12px;
  }
  
  .section-title {
    font-size: 14px;
    padding: 6px 10px;
  }
  
  .card-title {
    font-size: 16px;
  }
  
  .success-icon {
    width: 60px;
    height: 60px;
  }
  
  .success-icon .el-icon {
    font-size: 48px !important;
  }
}
