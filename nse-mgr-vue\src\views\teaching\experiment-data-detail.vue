<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryForm" :inline="true" label-width="130px">
        <el-form-item label="学号" prop="account">
          <el-input v-model="queryForm.account" placeholder="请输入学号" clearable />
        </el-form-item>
        <el-form-item label="姓名" prop="studentName">
          <el-input v-model="queryForm.studentName" placeholder="请输入姓名" clearable />
        </el-form-item>
        <el-form-item label="班级" prop="officialName">
          <el-input v-model="queryForm.officialName" placeholder="请输入班级" clearable />
        </el-form-item>
        <template v-if="isExpand">
          <el-form-item label="提交状态" prop="submitStatus">
            <el-select v-model="queryForm.submitStatus" placeholder="请选择提交状态" clearable>
              <el-option label="已提交" value="已提交"></el-option>
              <el-option label="未提交" value="未提交"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="分数" prop="scores">
            <el-input-number
              v-model="queryForm.scores[0]"
              placeholder="最小值"
              :controls="false"
              :precision="0"
              :min="0"
              :max="100"
            ></el-input-number>
            <span class="line">&nbsp;-&nbsp;</span>
            <el-input-number
              v-model="queryForm.scores[1]"
              placeholder="最大值"
              :controls="false"
              :precision="0"
              :min="0"
              :max="100"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="提交时间" prop="submitDates">
            <el-date-picker
              v-model="queryForm.submitDates"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
        </template>
      </el-form>
      <div class="search-buttons">
        <el-button type="primary" icon="search" @click="pageQuery()">查询</el-button>
        <el-button icon="refresh" @click="resetQuery()">重置</el-button>
        <el-link class="ml-3" type="primary" underline="never" @click="isExpand = !isExpand">
          {{ isExpand ? "收起" : "展开" }}
          <component :is="isExpand ? ArrowUp : ArrowDown" class="w-4 h-4 ml-2" />
        </el-link>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card shadow="never" class="data-table">
      <div>
        <ul class="detail-desc">
          <li>
            实验名称：
            <span>{{ queryString.expName }}</span>
          </li>
          <li>
            实验总人数：
            <span>{{ myTotal }}</span>
          </li>
          <li>
            实验提交人数：
            <span>{{ queryString.expSubmitCount }}</span>
          </li>
        </ul>
      </div>
      <div class="data-table__toolbar">
        <div class="data-table__toolbar--actions">
          <!-- v-hasPerm="['expData:assignHomework']" -->
          <el-button type="primary" icon="CirclePlus" @click="handleAssignHomework">布置实验作业</el-button>
          <!-- v-hasPerm="['expData:batchDelete']" -->
          <el-button
type="danger" plain icon="delete" 
              :disabled="myIds.length === 0" @click="handleBatchDelete">批量删除</el-button>
        </div>
      </div>

      <el-table
ref="tableRef" v-loading="myLoading" :data="myPageData" 
          border
          stripe highlight-current-row class="data-table__content"
          @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column type="index" label="序号" width="70" />
        <el-table-column label="学号" prop="account" min-width="120" show-overflow-tooltip />
        <el-table-column label="姓名" prop="studentName" min-width="200" show-overflow-tooltip />
        <el-table-column label="班级" prop="officialName" min-width="200" show-overflow-tooltip />
        <el-table-column
          label="提交状态"
          prop="submitStatus"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tag v-if="row.submitStatus === '已提交'" type="success">{{ row.submitStatus }}</el-tag>
            <el-tag v-else type="warning">{{ row.submitStatus }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="分数" prop="score" min-width="100" show-overflow-tooltip >
          <template #default="{ row }">
            <span>{{ row.score || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="提交时间" prop="submitDate" min-width="200" show-overflow-tooltip />
        <el-table-column label="实验报告" prop="expResultFilename" min-width="200" show-overflow-tooltip >
          <template #default="{ row }">
            <el-link type="primary" @click="previewExpResult({ id: row.lessonId, expResultFilename: row.expResultFilename })">
              {{ row.expResultFilename }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="240">
          <template #default="scope">
            <!-- v-hasPerm="['expDataDetail:viewExp']" -->
            <el-button 
              v-loading.fullscreen.lock="screenLoading" 
              type="primary" link 
              size="small" :icon="View" 
              @click="handleAssignedExperiment(scope.row)">查看实验</el-button>
            <!-- v-hasPerm="['expDataDetail:changeScore']" -->
            <el-button type="primary" link size="small" :icon="Star" @click="studentName = scope.row.studentName; submitScoreForm.lessonId = scope.row.lessonId; scoreDialogVisible = true">{{ scope.row.score != null ? '修改评分' : '评分' }}</el-button>
            <!-- v-hasPerm="['expDataDetail:delete']" -->
            <el-button type="danger" link size="small" :icon="Delete" @click="handleDeleteExperiment(scope.row.lessonId)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

        <pagination
          v-if="myTotal > 0"
          v-model:total="myTotal"
          v-model:page="queryForm.pageNumber"
          v-model:limit="queryForm.pageSize"
          @pagination="fetchTableData"
        />
    </el-card>

    <!-- 布置作业对话框 -->
    <CoursePkgSelector
      v-model:visible="assignDialogVisible"
      title="布置实验作业"
      @confirm="handleAssignConfirm"
      @cancel="handleAssignCancel"
    />

    <!-- 预览实验结果（作业）弹窗 -->
    <PreviewExpResult
      v-if="previewDialog.visible"
      v-model="previewDialog.visible"
      :exp-file="previewDialog.blob"
      @stop-preview="stopPreview"
    />

    <!-- 提交评分 -->
    <el-dialog v-model="scoreDialogVisible" class="score-dialog" title="评分" width="360">
      <el-form :model="submitScoreForm">
        <div style="margin-bottom: 20px;">请对学生【{{ studentName }}】实验数据进行评分：</div>
        <el-form-item>
          <el-input-number v-model="submitScoreForm.score" :min="1" :max="100" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="scoreDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleScore">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ArrowUp, ArrowDown, Star, Delete, View } from "@element-plus/icons-vue";
import { useRoute, useRouter } from "vue-router";
import Base64 from "@/utils/base64";
import { ExperimentDataDetailQuery, ExperimentDataDetailVO } from "@/api/teaching/types/experiment-data.types";
import ExperimentDataAPI from "@/api/teaching/experiment-data.api";
import PreviewExpResult from "../experiment/components/PreviewExpResult.vue";
import usePreview from "@/hooks/experiment/usePreview";
import { ExperimentPreviewForm, ExperimentScoreForm, ExperimentUrlDto } from "@/api/experiment/types/experiment.types";
import ExperimentAPI from "@/api/experiment/experiment.api";
import { useExperimentStore } from "@/store/modules/experiment.store";
import { deleteLessonApi, submitScoreApi } from "@/api/lesson/lesson.api";
import TeachingAPI from "@/api/teaching/teaching.api";

const queryFormRef = ref();
const tableRef = ref();
const isExpand = ref(false);
const route = useRoute();
const router = useRouter();
const queryForm = reactive<ExperimentDataDetailQuery>({
  pageNumber: 1,
  pageSize: 10,
  lessonBatchNo: "",
  account: "",
  studentName: "",
  officialName: "",
  submitStatus: "",
  scores: [],
  submitDates: [],
});

const myIds = ref<string[]>([]);
// 行复选框选中
const handleSelectionChange = (selection: ExperimentDataDetailVO[]) => {
  myIds.value = selection.map((item: ExperimentDataDetailVO) => item.lessonId);
};

const submitScoreForm = reactive<ExperimentScoreForm>({
  lessonId: '',
  score: 0,
})
const studentName = ref<string>();
const myLoading = ref(false);
const myPageData = ref<ExperimentDataDetailVO[]>([]);
const myTotal = ref(0);
const scoreDialogVisible = ref<boolean>(false)

const queryString = Base64.decode(route.query.info);
// 拉取数据
const fetchTableData = async () => {
  queryForm.lessonBatchNo = queryString.lessonBatchNo
  const data = await ExperimentDataAPI.listDetailPageQuery(queryForm);
  myPageData.value = data.rows;
  myTotal.value = data.total;
}

// 查询
function pageQuery() {
  queryForm.pageNumber = 1;
  fetchTableData();
}

// 重置查询
function resetQuery() {
  queryFormRef.value?.resetFields();
  pageQuery();
}

// 预览实验结果（作业）相关
const { previewDialog, previewExpResult, stopPreview } = usePreview();

/**
 * 查看实验
 * @param data 
 */
const screenLoading = ref(false)
const handleAssignedExperiment = async (data: ExperimentDataDetailVO) => {
  try {
    screenLoading.value = true;

    const requestData: ExperimentPreviewForm = {
      lessonId: data.lessonId,
      experimentId: '',
    }
    const experimentUrlInfo: ExperimentUrlDto = await ExperimentAPI.previpreviewExperimentApi(requestData);

    // 创建实验会话并获取会话ID
    const experimentStore = useExperimentStore();
    const sessionId = experimentStore.createSession({
      signUrl: experimentUrlInfo.signUrl,
      title: "实验环境",
      hostId: experimentUrlInfo.hostId,
      serverIp: experimentUrlInfo.serverIp,
      serverPort: experimentUrlInfo.serverPort?.toString(),
      userId: experimentUrlInfo.userId,
    });
    
    // 使用会话ID导航到实验容器页面
    const routeData = router.resolve({
      path: '/expinfo',
      query: {
        sessionId,
      }
    });
    window.open(routeData.href, '_blank');
  } finally {
    screenLoading.value = false;
  }

}

/**
 * 提交评分
 * @param row 
 */
const handleScore = async () => {
  await submitScoreApi(submitScoreForm);
  scoreDialogVisible.value = false;
  fetchTableData();
  ElMessage.success("评分成功");
}



// 删除实验
const handleDeleteExperiment = async (lessonId: string) => {
  try {
    await ElMessageBox.confirm("此操作将永久删除该学生的实验数据, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    screenLoading.value = true;
    const ids = [lessonId];
    await deleteLessonApi(ids);
    ElMessage.success("删除成功");
    fetchTableData();
  } finally {
    screenLoading.value = false;
  }
};

// 批量删除实验
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm("此操作将永久删除该实验数据, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    if (!myIds.value) {
      ElMessage.error("请选择要删除的实验");
      return;
    }
    screenLoading.value = true;
    await deleteLessonApi(myIds.value);
    ElMessage.success("删除成功");
    fetchTableData();
  } finally {
    screenLoading.value = false;
  }
};
// 布置作业相关
const assignDialogVisible = ref(false);

// 处理布置作业
const handleAssignHomework = () => {
  assignDialogVisible.value = true;
};

// 处理布置作业确认
const handleAssignConfirm = async (repo: any) => {
  await TeachingAPI.assignHomework(queryString.courseId, repo.repoId);
  ElMessage.success('布置作业成功');
  fetchTableData();
  assignDialogVisible.value = false;
};

// 处理布置作业取消
const handleAssignCancel = () => {
  assignDialogVisible.value = false;
};
onMounted(() => fetchTableData())
</script>

<style scoped lang="scss">
.search-container {
  display: flex;
  justify-content: space-between;
  padding: 18px 16px 0;
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;

  .search-buttons {
    min-width: 250px;
  }
  .line {
    color: #606266;
  }
  .el-input {
    width: 200px;
  }

  .el-select {
    width: 200px;
  }
}

.data-table {
  .detail-desc {
    display: flex;
    margin-bottom: 20px;
    > li {
      width: auto;
      margin-right: 50px;
      font-size: 14px;
      > span {
        color: #606266;
      }
    }
  }
}

.score-dialog :deep(.el-input-number) .el-input__wrapper {
  width: 100% !important;
}
</style>
