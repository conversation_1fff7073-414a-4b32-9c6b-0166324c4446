import{d as e,aF as a,aD as l,am as t,c as s,r,V as o,o as i,aQ as n,g as d,f as u,C as p,m,w as c,h as v,a0 as h,Q as j,a1 as f,e as _,F as b,D as g,i as y,b0 as w,Y as x,X as V,Z as C,aP as k}from"./index.Ckm1SagX.js";/* empty css                */import{E as N}from"./dialog.TtqHlFhB.js";import"./overlay.CXfNA60T.js";import{E as T}from"./card.BfhlXze7.js";import{_ as E}from"./index.r55iLQne.js";import{E as U,a as F}from"./table-column.DQa6-hu-.js";import"./checkbox.CyAsOZKA.js";/* empty css                */import"./popper.DpZVcW1M.js";import"./scrollbar.6rbryiG1.js";/* empty css            *//* empty css             *//* empty css               */import{a as S,E as B}from"./form-item.CUMILu98.js";import{E as D}from"./date-picker.CB50TImD.js";/* empty css              */import P from"./transfer.BSmAeCb0.js";import{T as Y}from"./teaching.api.BuM237ec.js";import{A as z}from"./account.api.C-wwvVop.js";import{E as I}from"./index.4JfkAhur.js";import{E as K}from"./index.CbYeWxT8.js";import{E as q}from"./index.CMOQuMWt.js";import{v as A}from"./directive.C7vihscI.js";import{E as H}from"./index.DCgMX_7R.js";import{E as M}from"./index.KtapGdwl.js";import{_ as O}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.BjYFza3j.js";import"./vnode.BkZiIFpS.js";import"./aria.C1IWO_Rd.js";import"./scroll.XdyICIdv.js";import"./focus-trap.Bd_uzvDY.js";import"./index.C0OsJ5su.js";import"./refs.biN0GvkM.js";import"./index.BRUQ9gWw.js";import"./event.BwRzfsZt.js";import"./index.Dh_vcBr5.js";import"./pagination.TL6aFrlm.js";import"./select.DHkh6uhw.js";import"./index.BPj3iklg.js";import"./use-form-common-props.BSYTvb6G.js";import"./token.DWNpOE8r.js";import"./strings.By8NVWWL.js";import"./castArray.Chmjnshw.js";import"./isEqual.CZKKciWh.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./index.Byj-i824.js";import"./debounce.YgIwzEIs.js";import"./_baseIteratee.PZHdcgYb.js";import"./index.BLy3nyPI.js";import"./index.B0geSHq7.js";import"./_initCloneObject.BsGr3vVr.js";import"./isPlainObject.Ct3iyI-U.js";import"./index.Cn1QDWeG.js";import"./_baseClone.ByRc02qR.js";import"./index.COAWgEf6.js";import"./index.DJHzyRe5.js";import"./tree.BMRqnaD4.js";import"./index.Cg5eTZHL.js";import"./index.CqmGTqol.js";/* empty css             */const R={class:"app-container"},Q={class:"search-container"},X={class:"search-buttons"},Z={class:"preview-row"},G={class:"preview-value"},J={class:"preview-value"},L={class:"preview-value"},W={class:"data-table__toolbar"},$={class:"data-table__toolbar--actions"},ee={style:{color:"#4080FF"}},ae={style:{color:"#4080FF"}},le={class:"dialog-footer"},te=O(e({__name:"student-manage",setup(e){const O=a();l();const te=t(),se=s(()=>te.query.id),re=r(),oe=r(!1),ie=o({pageNumber:1,pageSize:10,courseId:se.value,account:"",name:"",className:"",createdBy:"",startTime:"",endTime:""}),ne=r(),de=r(),ue=r(),pe=r(0),me=r([]),ce=r(!1),ve=r([]);r([]);const he=r(0),je=r([]),fe=r(),_e=r(!1),be=r([]),ge=r([]),ye=()=>{ce.value=!0,me.value&&2===me.value.length?(ie.startTime=me.value[0],ie.endTime=me.value[1]):(ie.startTime="",ie.endTime=""),Y.getCourseStudentPage(ie).then(e=>{ve.value=e.rows,he.value=e.total,ne.value=e.courseName,de.value=e.className,ue.value=e.teacherName,pe.value=e.studentCnt,ce.value=!1})},we=()=>{ie.pageNumber=1,ye()},xe=()=>{var e;null==(e=fe.value)||e.resetFields(),me.value=[],ie.pageNumber=1,ye()},Ve=e=>{je.value=e.map(e=>e.id)},Ce=()=>{z.getTree(!1).then(e=>{Y.getCourseStudent(se.value).then(a=>{ge.value=(e||[]).filter(e=>e.children&&e.children.length).map(e=>({...e,children:e.children.filter(e=>!a.includes(e.value))})).filter(e=>e.children&&e.children.length),be.value=[],_e.value=!0})})},ke=()=>{if(!be.value||0===be.value.length)return void k.warning("请选择学生");const e=H.service({lock:!0,text:"正在添加学生...",background:"rgba(0, 0, 0, 0.7)"});Y.addCourseStudent({id:se.value,userIds:be.value}).then(()=>{k.success("添加学生成功"),_e.value=!1,ye();O.delCachedView({name:"Teaching"})}).finally(()=>{e.close()})},Ne=e=>{const a=e?[e]:je.value;0!==a.length?M.confirm("此操作将永久删除该课程下所有学生的实验数据，请确认是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Y.deleteCourseStudent(a).then(()=>{k.success("删除成功"),ye();O.delCachedView({name:"Teaching"})})}):k.warning("请选择要删除的学生")};return i(()=>{ye(),Y.getCourseDetail(se.value).then(e=>{re.value=e})}),(e,a)=>{const l=I,t=S,s=D,r=B,o=K,i=q,k=F,Y=U,z=E,H=T,M=N,O=n("hasPerm"),te=A;return u(),d("div",R,[p("div",Q,[m(r,{ref_key:"queryFormRef",ref:fe,model:ie,inline:!0,"label-width":"auto"},{default:c(()=>[m(t,{label:"学号",prop:"account"},{default:c(()=>[m(l,{modelValue:ie.account,"onUpdate:modelValue":a[0]||(a[0]=e=>ie.account=e),placeholder:"请输入",clearable:"",style:{width:"200px"},onKeyup:h(we,["enter"])},null,8,["modelValue"])]),_:1}),m(t,{label:"姓名",prop:"name"},{default:c(()=>[m(l,{modelValue:ie.name,"onUpdate:modelValue":a[1]||(a[1]=e=>ie.name=e),placeholder:"请输入",clearable:"",style:{width:"200px"},onKeyup:h(we,["enter"])},null,8,["modelValue"])]),_:1}),m(t,{label:"班级名称",prop:"className"},{default:c(()=>[m(l,{modelValue:ie.className,"onUpdate:modelValue":a[2]||(a[2]=e=>ie.className=e),placeholder:"请输入",clearable:"",style:{width:"200px"},onKeyup:h(we,["enter"])},null,8,["modelValue"])]),_:1}),oe.value?(u(),d(j,{key:0},[m(t,{label:"添加人",prop:"createdBy"},{default:c(()=>[m(l,{modelValue:ie.createdBy,"onUpdate:modelValue":a[3]||(a[3]=e=>ie.createdBy=e),placeholder:"请输入",clearable:"",style:{width:"200px"},onKeyup:h(we,["enter"])},null,8,["modelValue"])]),_:1}),m(t,{label:"添加时间",prop:"createdDate"},{default:c(()=>[m(s,{modelValue:me.value,"onUpdate:modelValue":a[4]||(a[4]=e=>me.value=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"380px"}},null,8,["modelValue"])]),_:1})],64)):v("",!0)]),_:1},8,["model"]),p("div",X,[m(o,{type:"primary",icon:"search",onClick:we},{default:c(()=>a[13]||(a[13]=[f("查询")])),_:1,__:[13]}),m(o,{icon:"refresh",onClick:xe},{default:c(()=>a[14]||(a[14]=[f("重置")])),_:1,__:[14]}),m(i,{class:"ml-3",type:"primary",underline:"never",onClick:a[5]||(a[5]=e=>oe.value=!oe.value)},{default:c(()=>[f(b(oe.value?"收起":"展开")+" ",1),(u(),_(g(oe.value?y(w):y(x)),{class:"w-4 h-4 ml-2"}))]),_:1})])]),m(H,{shadow:"hover",class:"data-table"},{default:c(()=>{var e;return[V(p("div",Z,[a[15]||(a[15]=p("span",{class:"preview-title"},"课程：",-1)),p("span",G,b(ne.value)+"-"+b(de.value),1),a[16]||(a[16]=p("span",{class:"preview-title"},"任课老师：",-1)),p("span",J,b(ue.value),1),a[17]||(a[17]=p("span",{class:"preview-title"},"总人数：",-1)),p("span",L,b(pe.value)+"人",1)],512),[[C,ne.value]]),p("div",W,[p("div",$,[V((u(),_(o,{type:"primary",icon:"CirclePlus",onClick:Ce,disabled:"上课中"===(null==(e=re.value)?void 0:e.status)},{default:c(()=>a[18]||(a[18]=[f(" 新增学生 ")])),_:1,__:[18]},8,["disabled"])),[[O,["teaching:student:add"]]]),V((u(),_(o,{type:"danger",plain:"",icon:"delete",disabled:0===je.value.length,onClick:a[6]||(a[6]=e=>Ne())},{default:c(()=>a[19]||(a[19]=[f(" 批量删除学生 ")])),_:1,__:[19]},8,["disabled"])),[[O,["teaching:student:delete"]]])])]),V((u(),_(Y,{data:ve.value,border:"",stripe:"","highlight-current-row":"",class:"data-table__content",onSelectionChange:Ve},{default:c(()=>[m(k,{type:"selection",width:"50",selectable:e=>{var a;return"上课中"!==(null==(a=re.value)?void 0:a.status)}},null,8,["selectable"]),m(k,{type:"index",label:"序号",width:"70"}),m(k,{label:"学号",width:"200"},{default:c(e=>[p("span",ee,b(e.row.account),1)]),_:1}),m(k,{label:"角色",prop:"role",width:"150"},{default:c(e=>a[20]||(a[20]=[p("span",null," 学生 ",-1)])),_:1}),m(k,{label:"姓名",width:"150"},{default:c(e=>[p("span",ae,b(e.row.name),1)]),_:1}),m(k,{label:"班级",prop:"className",width:"150"}),m(k,{label:"添加人",prop:"createdBy",width:"150"}),m(k,{label:"添加时间",prop:"createdDate",width:"180"}),m(k,{label:"操作",fixed:"right",width:"150"},{default:c(e=>{var l;return[V((u(),_(o,{type:"danger",link:"",icon:"delete",size:"small",disabled:"上课中"===(null==(l=re.value)?void 0:l.status),onClick:a=>Ne(e.row.id)},{default:c(()=>a[21]||(a[21]=[f(" 删除 ")])),_:2,__:[21]},1032,["disabled","onClick"])),[[O,["teaching:student:delete"]]])]}),_:1})]),_:1},8,["data"])),[[te,ce.value]]),he.value>0?(u(),_(z,{key:0,total:he.value,"onUpdate:total":a[7]||(a[7]=e=>he.value=e),page:ie.pageNumber,"onUpdate:page":a[8]||(a[8]=e=>ie.pageNumber=e),limit:ie.pageSize,"onUpdate:limit":a[9]||(a[9]=e=>ie.pageSize=e),onPagination:ye},null,8,["total","page","limit"])):v("",!0)]}),_:1}),m(M,{modelValue:_e.value,"onUpdate:modelValue":a[12]||(a[12]=e=>_e.value=e),title:"新增学生",width:"800px"},{footer:c(()=>[p("div",le,[m(o,{onClick:a[11]||(a[11]=e=>_e.value=!1)},{default:c(()=>a[22]||(a[22]=[f("取消")])),_:1,__:[22]}),m(o,{type:"primary",onClick:ke},{default:c(()=>a[23]||(a[23]=[f("确定")])),_:1,__:[23]})])]),default:c(()=>[m(P,{modelValue:be.value,"onUpdate:modelValue":a[10]||(a[10]=e=>be.value=e),data:ge.value},null,8,["modelValue","data"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-dca73ae2"]]);export{te as default};
