import{u as e}from"./index.Dh_vcBr5.js";import{A as o,r as a,c as l,o as d,I as i,ah as n,aY as t,B as s,bX as u,i as r}from"./index.Ckm1SagX.js";const v=Symbol("formContextKey"),m=Symbol("formItemContextKey"),I=()=>({form:o(v,void 0),formItem:o(m,void 0)}),p=(o,{formItemContext:s,disableIdGeneration:u,disableIdManagement:r})=>{u||(u=a(!1)),r||(r=a(!1));const v=a();let m;const I=l(()=>{var e;return!!(!o.label&&!o.ariaLabel&&s&&s.inputIds&&(null==(e=s.inputIds)?void 0:e.length)<=1)});return d(()=>{m=i([n(o,"id"),u],([o,a])=>{const l=null!=o?o:a?void 0:e().value;l!==v.value&&((null==s?void 0:s.removeInputId)&&(v.value&&s.removeInputId(v.value),(null==r?void 0:r.value)||a||!l||s.addInputId(l)),v.value=l)},{immediate:!0})}),t(()=>{m&&m(),(null==s?void 0:s.removeInputId)&&v.value&&s.removeInputId(v.value)}),{isLabeledByFormItem:I,inputId:v}},b=e=>{const o=s();return l(()=>{var a,l;return null==(l=null==(a=null==o?void 0:o.proxy)?void 0:a.$props)?void 0:l[e]})},f=(e,d={})=>{const i=a(void 0),n=d.prop?i:b("size"),t=d.global?i:u(),s=d.form?{size:void 0}:o(v,void 0),I=d.formItem?{size:void 0}:o(m,void 0);return l(()=>n.value||r(e)||(null==I?void 0:I.size)||(null==s?void 0:s.size)||t.value||"")},c=e=>{const a=b("disabled"),d=o(v,void 0);return l(()=>a.value||r(e)||(null==d?void 0:d.disabled)||!1)};export{f as a,c as b,p as c,v as d,m as f,I as u};
