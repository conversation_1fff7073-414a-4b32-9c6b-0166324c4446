package com.ruijie.nse.cloud.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.DataEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("lic_activation_file")
public class LicenseFile extends DataEntity {
    /**
     * 授权码
     */
    private String activationCode;
    /**
     * 授权产品信息
     */
    private String activationProductInfo;
    /**
     * 授权类型
     */
    private String activationType;
    /**
     * 终端许可数量（管理员）
     */
    private Integer permitMgrCnt;
    /**
     * 终端许可数量（普通用户）
     */
    private Integer permitUserCnt;
    /**
     * 有效期类型，永久、1年、2年、3年
     */
    private String validType;
    /**
     * 授权证书动态指纹
     */
    private String finger;
    /**
     * 销售订单编号
     */
    private String salesOrderNo;
    /**
     * 销售项目名称
     */
    private String salesProjectName;
    /**
     * 最终客户
     */
    private String finalCustomerName;
    /**
     * 项目销售人员
     */
    private String sellerName;
    /**
     * 机器码
     */
    private String machineCode;
    /**
     * 变更注销码
     */
    private String cancelCode;
    /**
     * 有效期开始日期
     */
    private Date validFrom;
    /**
     * 有效期截止日期
     */
    private Date validTo;
    /**
     * 未使用,已使用,已过期,已撤销
     */
    private String status;
    /**
     * lic文件存放地址
     */
    private String licenseUrl;
    /**
     * 注销时间
     */
    private Date cancelDate;
    /**
     * 申请人
     */
    private String applicant;
}