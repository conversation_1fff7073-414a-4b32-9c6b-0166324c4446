17:54:10.463 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
17:54:10.556 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 14592 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
17:54:10.557 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "prod"
17:54:13.686 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:54:15.222 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
17:54:15.227 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
17:54:15.227 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
17:54:15.334 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
17:54:15.572 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
17:54:15.797 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
17:54:17.598 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
17:54:18.738 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
17:54:47.308 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
17:54:47.396 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 44036 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
17:54:47.397 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
17:54:49.417 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:54:50.552 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
17:54:50.555 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
17:54:50.556 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
17:54:50.654 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
17:54:50.805 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
17:54:50.982 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
17:54:52.273 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
17:54:52.752 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6651efa4
17:54:52.755 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
17:54:52.815 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
17:54:53.189 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.155s)
17:54:53.547 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
17:54:53.610 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
17:54:55.273 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
17:54:55.337 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
17:54:55.578 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
17:54:55.903 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
17:54:56.016 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
17:54:56.260 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
17:54:56.388 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
17:54:56.391 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
17:54:56.446 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
17:54:56.449 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
17:54:56.450 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
17:54:58.233 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
17:54:58.288 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
17:54:58.929 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
17:54:59.560 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c65121
17:54:59.971 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
17:55:00.024 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 13.319 seconds (process running for 14.339)
17:55:00.134 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,60] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
17:55:00.461 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_THREAT_DETECTED, evtDetails=RuntimeDetectedService.SecurityStatus(debuggingDetected=true, jvmTampered=false, fileIntegrityViolated=false, memoryAnomalyDetected=false, networkAnomalyDetected=false, securityLevel=MEDIUM_RISK, lastCheckTime=1756893300118), remark=null, createdDate=2025-09-03T17:55:00.135421, modifiedDate=2025-09-03T17:55:00.135421, evtLevel=WARNING, evtMessage=安全事件检查)
17:55:01.221 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 17:55:01 CST 2025, errorMessage=null, details=[], extensions=null)
17:55:01.654 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 17:55:01 CST 2025, errorMessage=null, details=[], extensions=null)
17:55:02.114 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
17:55:02.115 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
17:55:02.115 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
17:55:02.115 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
17:55:02.115 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
17:55:02.116 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
17:55:02.116 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
17:55:02.116 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
17:55:02.116 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
17:55:02.116 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
17:55:02.142 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| 50             | 5                | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

18:19:40.493 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
18:19:40.602 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 40656 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
18:19:40.603 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "prod"
18:19:42.745 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
18:19:44.063 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
18:19:44.071 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
18:19:44.072 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
18:19:44.203 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
18:19:44.356 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
18:19:44.529 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
18:19:45.824 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
18:19:46.944 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
18:20:05.805 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
18:20:05.889 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 15060 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
18:20:05.891 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
18:20:07.900 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
18:20:09.023 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
18:20:09.028 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
18:20:09.028 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
18:20:09.125 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
18:20:09.279 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
18:20:09.471 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
18:20:10.750 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
18:20:11.109 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2fca282c
18:20:11.113 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
18:20:11.174 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
18:20:11.500 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.133s)
18:20:11.806 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
18:20:11.832 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
18:20:13.151 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
18:20:13.171 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
18:20:13.349 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
18:20:13.453 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
18:20:13.473 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
18:20:13.631 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
18:20:13.710 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
18:20:13.714 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
18:20:13.753 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
18:20:13.756 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
18:20:13.756 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
18:20:15.092 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
18:20:15.135 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
18:20:15.546 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
18:20:16.144 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3add81c4
18:20:16.546 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
18:20:16.584 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 11.383 seconds (process running for 12.492)
18:20:17.283 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
18:20:17.284 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
18:20:17.284 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
18:20:17.284 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
18:20:17.285 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
18:20:17.285 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
18:20:17.285 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
18:20:17.285 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
18:20:17.285 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
18:20:17.285 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
18:20:17.363 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,60] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
18:20:17.509 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_THREAT_DETECTED, evtDetails=RuntimeDetectedService.SecurityStatus(debuggingDetected=true, jvmTampered=false, fileIntegrityViolated=false, memoryAnomalyDetected=false, networkAnomalyDetected=false, securityLevel=MEDIUM_RISK, lastCheckTime=1756894817354), remark=null, createdDate=2025-09-03T18:20:17.363008300, modifiedDate=2025-09-03T18:20:17.363008300, evtLevel=WARNING, evtMessage=安全事件检查)
18:20:18.203 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:20:18 CST 2025, errorMessage=null, details=[], extensions=null)
18:20:18.211 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| 50             | 5                | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

18:20:18.695 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:20:18 CST 2025, errorMessage=null, details=[], extensions=null)
18:20:31.137 [http-nio-8090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:25:00.906 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:25:01.016 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
18:25:17.231 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:25:17 CST 2025, errorMessage=null, details=[], extensions=null)
18:30:00.225 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:30:00.280 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
18:30:16.826 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:30:16 CST 2025, errorMessage=null, details=[], extensions=null)
18:31:18.251 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
18:31:18.338 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
18:31:18.339 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,88] - [System|系统] - NSE Server进程缓存清理成功
18:31:18.422 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,88] - [System|系统] - NSE Server进程缓存清理成功
18:31:18.670 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
18:31:18.670 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
18:31:18.726 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
18:31:18.727 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
18:31:18.730 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
18:31:18.733 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
18:31:23.123 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
18:31:23.251 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 168 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
18:31:23.253 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
18:31:25.814 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
18:31:27.053 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
18:31:27.057 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
18:31:27.058 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
18:31:27.159 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
18:31:27.346 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
18:31:27.515 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
18:31:28.957 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
18:31:29.383 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7641ed02
18:31:29.386 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
18:31:29.458 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
18:31:29.844 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.160s)
18:31:30.196 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
18:31:30.232 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
18:31:31.760 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
18:31:31.785 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
18:31:32.021 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
18:31:32.180 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
18:31:32.210 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
18:31:32.407 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
18:31:32.471 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 664956ms : this is harmless.
18:31:32.500 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
18:31:32.503 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
18:31:32.582 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
18:31:32.585 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
18:31:32.586 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
18:31:34.358 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
18:31:34.401 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
18:31:34.819 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
18:31:35.635 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c65121
18:31:36.060 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
18:31:36.097 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 13.734 seconds (process running for 15.278)
18:31:36.189 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,60] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
18:31:36.383 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_THREAT_DETECTED, evtDetails=RuntimeDetectedService.SecurityStatus(debuggingDetected=true, jvmTampered=false, fileIntegrityViolated=false, memoryAnomalyDetected=false, networkAnomalyDetected=false, securityLevel=MEDIUM_RISK, lastCheckTime=1756895496177), remark=null, createdDate=2025-09-03T18:31:36.189005, modifiedDate=2025-09-03T18:31:36.189005, evtLevel=WARNING, evtMessage=安全事件检查)
18:31:37.159 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:31:37 CST 2025, errorMessage=null, details=[], extensions=null)
18:31:37.600 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:31:37 CST 2025, errorMessage=null, details=[], extensions=null)
18:31:37.801 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
18:31:37.802 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
18:31:37.802 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
18:31:37.802 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
18:31:37.802 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
18:31:37.802 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
18:31:37.802 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
18:31:37.802 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
18:31:37.802 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
18:31:37.802 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
18:31:37.810 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| 50             | 5                | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

18:31:46.491 [http-nio-8090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:33:01.605 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
18:33:01.609 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
18:33:01.698 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,88] - [System|系统] - NSE Server进程缓存清理成功
18:33:01.707 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,88] - [System|系统] - NSE Server进程缓存清理成功
18:33:01.723 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
18:33:01.724 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
18:33:01.750 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
18:33:01.751 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
18:33:01.754 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
18:33:01.756 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
18:33:05.856 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
18:33:05.950 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 11168 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
18:33:05.952 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
18:33:08.408 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
18:33:09.981 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
18:33:09.986 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
18:33:09.987 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
18:33:10.154 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
18:33:10.413 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
18:33:10.640 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
18:33:12.022 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
18:33:12.388 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4e357792
18:33:12.392 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
18:33:12.464 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
18:33:12.831 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.149s)
18:33:13.281 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
18:33:13.334 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
18:33:14.962 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
18:33:14.997 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
18:33:15.219 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
18:33:15.349 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
18:33:15.382 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
18:33:15.618 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
18:33:15.677 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 768011ms : this is harmless.
18:33:15.710 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
18:33:15.715 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
18:33:15.757 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
18:33:15.761 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
18:33:15.761 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
18:33:17.345 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/api/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
18:33:17.398 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
18:33:17.889 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
18:33:18.621 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c65121
18:33:19.176 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
18:33:19.220 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 13.996 seconds (process running for 15.081)
18:33:19.311 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,60] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
18:33:19.498 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_THREAT_DETECTED, evtDetails=RuntimeDetectedService.SecurityStatus(debuggingDetected=true, jvmTampered=false, fileIntegrityViolated=false, memoryAnomalyDetected=false, networkAnomalyDetected=false, securityLevel=MEDIUM_RISK, lastCheckTime=1756895599301), remark=null, createdDate=2025-09-03T18:33:19.311080900, modifiedDate=2025-09-03T18:33:19.311080900, evtLevel=WARNING, evtMessage=安全事件检查)
18:33:20.302 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:33:20 CST 2025, errorMessage=null, details=[], extensions=null)
18:33:20.741 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:33:20 CST 2025, errorMessage=null, details=[], extensions=null)
18:33:20.888 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
18:33:20.888 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
18:33:20.888 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
18:33:20.889 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
18:33:20.889 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
18:33:20.889 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
18:33:20.889 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
18:33:20.889 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
18:33:20.889 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
18:33:20.890 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
18:33:20.903 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| 50             | 5                | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

18:33:22.110 [http-nio-8090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:35:00.012 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:35:00.128 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
18:36:18.340 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
18:36:18.418 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
18:36:18.437 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,88] - [System|系统] - NSE Server进程缓存清理成功
18:36:18.500 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,88] - [System|系统] - NSE Server进程缓存清理成功
18:36:18.528 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
18:36:18.529 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
18:36:18.553 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
18:36:18.554 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
18:36:18.557 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
18:36:18.560 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
18:36:22.266 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
18:36:22.350 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 51028 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
18:36:22.351 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
18:36:24.378 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
18:36:25.702 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
18:36:25.705 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
18:36:25.705 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
18:36:25.823 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
18:36:25.982 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
18:36:26.174 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
18:36:27.549 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
18:36:27.969 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@642c5bb3
18:36:27.973 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
18:36:28.043 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
18:36:28.454 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.164s)
18:36:28.885 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
18:36:28.919 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
18:36:30.308 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
18:36:30.328 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
18:36:30.497 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
18:36:30.611 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
18:36:30.633 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
18:36:30.785 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
18:36:30.831 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 964814ms : this is harmless.
18:36:30.865 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
18:36:30.868 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
18:36:30.909 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
18:36:30.911 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
18:36:30.911 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
18:36:32.415 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
18:36:32.788 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
18:36:33.401 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c65121
18:36:33.839 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
18:36:33.872 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 12.156 seconds (process running for 13.118)
18:36:34.342 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
18:36:34.342 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
18:36:34.342 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
18:36:34.342 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
18:36:34.342 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
18:36:34.342 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
18:36:34.343 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
18:36:34.343 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
18:36:34.343 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
18:36:34.343 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
18:36:34.430 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,60] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
18:36:34.600 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_THREAT_DETECTED, evtDetails=RuntimeDetectedService.SecurityStatus(debuggingDetected=true, jvmTampered=false, fileIntegrityViolated=false, memoryAnomalyDetected=false, networkAnomalyDetected=false, securityLevel=MEDIUM_RISK, lastCheckTime=1756895794419), remark=null, createdDate=2025-09-03T18:36:34.430939100, modifiedDate=2025-09-03T18:36:34.430939100, evtLevel=WARNING, evtMessage=安全事件检查)
18:36:35.330 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:36:35 CST 2025, errorMessage=null, details=[], extensions=null)
18:36:35.338 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| 50             | 5                | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

18:36:35.798 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:36:35 CST 2025, errorMessage=null, details=[], extensions=null)
18:36:36.612 [http-nio-8090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:40:00.005 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:40:00.575 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
18:41:34.621 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:41:34 CST 2025, errorMessage=null, details=[], extensions=null)
18:45:00.030 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:45:00.093 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
18:46:35.066 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:46:34 CST 2025, errorMessage=null, details=[], extensions=null)
18:51:11.580 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
18:51:11.829 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 57076 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
18:51:11.832 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
18:51:14.741 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
18:51:15.977 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
18:51:15.981 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
18:51:15.981 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
18:51:16.064 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
18:51:16.236 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
18:51:16.394 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
18:51:17.632 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
18:51:18.065 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7641ed02
18:51:18.067 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
18:51:18.133 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
18:51:18.539 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.161s)
18:51:18.912 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
18:51:18.946 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
18:51:20.283 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
18:51:20.306 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
18:51:20.472 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
18:51:20.583 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
18:51:20.605 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
18:51:20.739 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
18:51:20.786 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 964814ms : this is harmless.
18:51:20.813 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
18:51:20.816 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
18:51:20.854 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
18:51:20.856 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
18:51:20.857 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
18:51:22.474 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
18:51:22.520 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
18:51:22.996 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
18:51:23.646 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c65121
18:51:24.160 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
18:51:24.194 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 13.5 seconds (process running for 15.037)
18:51:24.199 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
18:51:24.199 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
18:51:24.199 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
18:51:24.199 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
18:51:24.199 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
18:51:24.200 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
18:51:24.200 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
18:51:24.291 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,60] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
18:51:25.184 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:51:25 CST 2025, errorMessage=null, details=[], extensions=null)
18:51:25.680 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:51:25 CST 2025, errorMessage=null, details=[], extensions=null)
18:51:25.945 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
18:51:25.945 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
18:51:25.945 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
18:51:25.958 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| 50             | 5                | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

18:51:31.522 [http-nio-8090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:55:00.008 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:55:00.138 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
18:56:24.422 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:56:24 CST 2025, errorMessage=null, details=[], extensions=null)
18:56:58.327 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
18:56:58.382 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
18:56:58.436 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,88] - [System|系统] - NSE Server进程缓存清理成功
18:56:58.455 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,88] - [System|系统] - NSE Server进程缓存清理成功
18:56:58.482 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
18:56:58.483 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
18:56:58.509 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
18:56:58.510 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
18:56:58.513 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
18:56:58.515 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
18:57:01.523 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
18:57:01.605 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 54300 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
18:57:01.606 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
18:57:04.467 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
18:59:10.537 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
18:59:10.637 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 43732 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
18:59:10.639 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
18:59:13.849 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
18:59:15.329 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
18:59:15.339 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
18:59:15.339 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
18:59:15.469 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
18:59:15.646 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
18:59:15.870 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
18:59:17.491 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
18:59:17.931 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@77ec9fdb
18:59:17.935 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
18:59:18.008 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
18:59:18.390 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.164s)
18:59:18.722 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
18:59:18.752 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
18:59:20.267 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
18:59:20.297 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
18:59:20.530 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
18:59:20.674 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
18:59:20.702 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
18:59:20.904 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
18:59:20.955 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2204769ms : this is harmless.
18:59:20.980 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
18:59:20.984 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
18:59:21.042 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
18:59:21.045 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
18:59:21.045 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
18:59:22.556 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
18:59:22.603 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
18:59:22.997 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
18:59:23.622 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c65121
18:59:24.061 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
18:59:24.097 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 14.556 seconds (process running for 15.832)
18:59:24.582 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
18:59:24.583 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
18:59:24.583 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
18:59:24.663 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,60] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
18:59:25.573 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:59:25 CST 2025, errorMessage=null, details=[], extensions=null)
18:59:25.575 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
18:59:25.575 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
18:59:25.575 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
18:59:25.575 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
18:59:25.575 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
18:59:25.576 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
18:59:25.576 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
18:59:25.587 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| 50             | 5                | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

18:59:26.001 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 18:59:25 CST 2025, errorMessage=null, details=[], extensions=null)
18:59:27.172 [http-nio-8090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:00:00.059 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
19:00:00.614 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
19:04:24.326 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 19:04:24 CST 2025, errorMessage=null, details=[], extensions=null)
19:05:00.408 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
19:05:00.462 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
19:09:24.746 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 19:09:24 CST 2025, errorMessage=null, details=[], extensions=null)
19:10:00.812 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
19:10:00.865 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
19:14:25.182 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 19:14:25 CST 2025, errorMessage=null, details=[], extensions=null)
19:15:00.008 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
19:15:00.061 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
19:19:24.292 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 19:19:24 CST 2025, errorMessage=null, details=[], extensions=null)
19:20:00.001 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
19:20:00.054 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
19:24:24.295 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 19:24:24 CST 2025, errorMessage=null, details=[], extensions=null)
19:25:00.003 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
19:25:00.056 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
19:29:24.658 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Wed Sep 03 19:29:24 CST 2025, errorMessage=null, details=[], extensions=null)
19:30:00.589 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
19:30:00.716 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
