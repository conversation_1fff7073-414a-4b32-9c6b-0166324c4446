package com.ruijie.nse.common.config.security;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户认证信息
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class UserPrincipal implements UserDetails {

    /**
     * 用户ID
     */
    private String userId;
    private String userType;
    private String account;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    @Getter(AccessLevel.NONE)
    private String password;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 权限列表
     */
    private List<String> permissions;

    /**
     * 角色列表
     */
    private List<String> roles;

    /**
     * 有效期
     */
    private Date validDate;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return permissions.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
    }

    @JsonIgnore
    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    /**
     * 获取权限字符串（用于JWT）
     *
     * @return 权限字符串
     */
    public String getAuthoritiesString() {
        if (permissions == null || permissions.isEmpty()) {
            return "";
        }
        return String.join(",", permissions);
    }

    /**
     * 获取角色字符串
     *
     * @return 角色字符串
     */
    public String getRolesString() {
        if (roles == null || roles.isEmpty()) {
            return "";
        }
        return String.join(",", roles);
    }

    /**
     * 是否为超级管理员
     *
     * @return 是否为超级管理员
     */
    public boolean isSuperAdmin() {
        return roles != null && roles.contains("super_admin");
    }
}