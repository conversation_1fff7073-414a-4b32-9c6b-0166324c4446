import{d as e,aD as l,r as a,c as s,I as t,g as r,f as n,C as u,m as d,w as o,a1 as i,E as c,i as v,dl as p,dm as m,dn as f,F as h,a6 as _,cs as j,a2 as k}from"./index.Ckm1SagX.js";import{E as y}from"./tree.BMRqnaD4.js";import"./checkbox.CyAsOZKA.js";/* empty css             *//* empty css              *//* empty css                */import{E as C}from"./popper.DpZVcW1M.js";/* empty css               */import{E as x}from"./index.CbYeWxT8.js";import{E as b}from"./index.4JfkAhur.js";import{_ as V}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./token.DWNpOE8r.js";import"./index.Cg5eTZHL.js";import"./index.CqmGTqol.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./index.BLy3nyPI.js";import"./_arrayPush.Dbwejsrt.js";import"./event.BwRzfsZt.js";import"./aria.C1IWO_Rd.js";import"./isEqual.CZKKciWh.js";import"./_Uint8Array.BCiDNJWl.js";import"./index.BRUQ9gWw.js";import"./index.C0OsJ5su.js";import"./index.Cn1QDWeG.js";import"./focus-trap.Bd_uzvDY.js";import"./index.DJHzyRe5.js";import"./index.Byj-i824.js";const g={class:"student-transfer"},w={class:"transfer-section"},E={class:"transfer-header"},K={class:"header-actions"},L={class:"transfer-content"},A={class:"transfer-controls"},I={style:{"margin-top":"30px"}},S={class:"transfer-section"},z={class:"transfer-header"},R={class:"header-actions"},T={class:"transfer-content"},U=V(e({__name:"transfer",props:{modelValue:{},data:{}},emits:["update:modelValue","account-manage","reset-available","reset-selected"],setup(e,{emit:V}){const U=l(),q=e,D=V,F=a(""),M=a(""),N=a(new Set),B=a(),G=a(),P=(e,l)=>!e||Z.value.includes(l.value),Y=(e,l)=>!e||J.value.includes(l.value),Z=a([]),H=()=>{var e;if(Z.value=[],F.value){const e=F.value.toLowerCase();Q.value.forEach(l=>{const a=l.label.toLowerCase().includes(e);a&&Z.value.push(l.value),l.children&&Array.isArray(l.children)&&l.children.forEach(l=>{(a||l.label.toLowerCase().includes(e))&&Z.value.push(l.value)})})}null==(e=B.value)||e.filter(F.value)},J=a([]),O=()=>{var e;if(J.value=[],M.value){const e=M.value.toLowerCase();W.value.forEach(l=>{const a=l.label.toLowerCase().includes(e);a&&J.value.push(l.value),l.children&&Array.isArray(l.children)&&l.children.forEach(l=>{(a||l.label.toLowerCase().includes(e))&&J.value.push(l.value)})})}null==(e=G.value)||e.filter(M.value)},Q=s(()=>{const e=new Set(q.modelValue),l=a=>a.map(a=>{const s=a.children?l(a.children):void 0;if(!s||0===s.length)return{...a,disabled:e.has(a.value),children:s};const t=s.every(e=>e.disabled);return{...a,disabled:t,children:s}});return l(q.data)}),W=s(()=>{const e=new Map(q.modelValue.map(e=>[e,!0])),l=a=>a.filter(a=>{if(e.has(a.value))return!0;if(a.children){if(l(a.children).length>0)return!0}return!1}).map(e=>e.children?{...e,children:l(e.children)}:e);return l(q.data)}),X=(e,l)=>{},$=(e,l)=>{},ee=()=>{var e,l;const a=((null==(e=B.value)?void 0:e.getCheckedNodes())||[]).filter(e=>!e.children).map(e=>e.value),s=[...q.modelValue],t=[];if(a.forEach(e=>{e=String(e),s.includes(e)||(s.push(e),t.push(e))}),D("update:modelValue",s),t.length>0&&G.value){const e=G.value.getCheckedKeys(),l=t.filter(e=>!N.value.has(e)),a=[...new Set([...e,...l])];k(()=>{G.value.setCheckedKeys(a)})}null==(l=B.value)||l.setCheckedKeys([])},le=()=>{var e;const l=((null==(e=G.value)?void 0:e.getCheckedNodes())||[]).filter(e=>!e.children).map(e=>e.value);if(!l||!l.length)return;const a=q.modelValue.filter(e=>!l.includes(e));l.forEach(e=>{N.value.delete(String(e))}),D("update:modelValue",a)},ae=()=>{D("account-manage"),U.push("/account/normal-user")},se=()=>{var e,l;D("reset-available"),F.value="",null==(e=B.value)||e.filter(""),null==(l=B.value)||l.setCheckedKeys([])},te=()=>{var e,l;D("reset-selected"),M.value="",null==(e=G.value)||e.filter(""),null==(l=G.value)||l.setCheckedKeys([]),D("update:modelValue",[])};return t(()=>q.modelValue,()=>{var e,l;null==(e=B.value)||e.setCheckedKeys([]),null==(l=G.value)||l.setCheckedKeys([])},{deep:!0}),(e,l)=>{const a=c,s=x,t=C,k=b,V=y;return n(),r("div",g,[u("div",w,[u("div",E,[l[4]||(l[4]=u("span",null,"待选择学生列表",-1)),u("div",K,[d(t,{content:"若待选择学生列表无对应学生，请到账号管理添加",placement:"top"},{default:o(()=>[d(s,{size:"small",type:"primary",onClick:ae},{default:o(()=>[d(a,null,{default:o(()=>[d(v(p))]),_:1}),l[2]||(l[2]=i("账号管理 "))]),_:1,__:[2]})]),_:1}),d(s,{size:"small",onClick:se},{default:o(()=>[d(a,null,{default:o(()=>[d(v(m))]),_:1}),l[3]||(l[3]=i("重置 "))]),_:1,__:[3]})])]),u("div",L,[d(k,{modelValue:F.value,"onUpdate:modelValue":l[0]||(l[0]=e=>F.value=e),clearable:"",placeholder:"请输入",onInput:H},{suffix:o(()=>[d(a,null,{default:o(()=>[d(v(f))]),_:1})]),_:1},8,["modelValue"]),d(V,{ref_key:"availableTreeRef",ref:B,"node-key":"value","show-checkbox":"",data:Q.value,"default-expand-all":!0,"check-strictly":!1,"filter-node-method":P,class:"transfer-tree",onCheck:X},{default:o(({node:e})=>[i(h(e.label),1)]),_:1},8,["data"])])]),u("div",A,[u("div",null,[d(s,{type:"primary",onClick:ee,circle:""},{default:o(()=>[d(a,null,{default:o(()=>[d(v(_))]),_:1})]),_:1})]),u("div",I,[d(s,{onClick:le,circle:""},{default:o(()=>[d(a,null,{default:o(()=>[d(v(j))]),_:1})]),_:1})])]),u("div",S,[u("div",z,[l[6]||(l[6]=u("span",null,"已选择学生列表",-1)),u("div",R,[d(s,{size:"small",onClick:te},{default:o(()=>[d(a,null,{default:o(()=>[d(v(m))]),_:1}),l[5]||(l[5]=i("重置 "))]),_:1,__:[5]})])]),u("div",T,[d(k,{modelValue:M.value,"onUpdate:modelValue":l[1]||(l[1]=e=>M.value=e),clearable:"",placeholder:"请输入",onInput:O},{suffix:o(()=>[d(a,null,{default:o(()=>[d(v(f))]),_:1})]),_:1},8,["modelValue"]),d(V,{ref_key:"selectedTreeRef",ref:G,"node-key":"value","show-checkbox":"",data:W.value,"default-expand-all":!0,"check-strictly":!1,"filter-node-method":Y,class:"transfer-tree",onCheck:$},{default:o(({node:e})=>[i(h(e.label),1)]),_:1},8,["data"])])])])}}}),[["__scopeId","data-v-b2daef34"]]);export{U as default};
