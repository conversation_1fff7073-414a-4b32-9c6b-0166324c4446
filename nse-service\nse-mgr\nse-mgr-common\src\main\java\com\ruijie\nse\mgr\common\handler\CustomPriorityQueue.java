package com.ruijie.nse.mgr.common.handler;

import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * 自定义优先级队列
 *
 * @param <E>
 */
public class CustomPriorityQueue<E> extends AbstractQueue<E> {

    // 默认初始容量
    private static final int DEFAULT_INITIAL_CAPACITY = 10;

    // 存储元素的数组
    private transient Object[] queue;

    // 队列当前大小
    private transient int size;

    // 比较器，如果为null则使用元素的自然排序
    private final transient Comparator<? super E> comparator;

    // 重入锁，保证线程安全
    private final ReentrantLock lock = new ReentrantLock();

    /**
     * 构造函数 - 默认容量，自然排序
     */
    public CustomPriorityQueue() {
        this(DEFAULT_INITIAL_CAPACITY, null);
    }

    /**
     * 构造函数 - 默认容量，自然排序
     */
    public CustomPriorityQueue(Comparator<? super E> comparator) {
        this(DEFAULT_INITIAL_CAPACITY, comparator);
    }

    /**
     * 构造函数 - 指定比较器，默认容量
     */
    public CustomPriorityQueue(int initialCapacity) {
        this(initialCapacity, null);
    }

    /**
     * 构造函数 - 指定容量和比较器
     */
    public CustomPriorityQueue(int initialCapacity, Comparator<? super E> comparator) {
        if (initialCapacity < 1) {
            throw new IllegalArgumentException("初始容量必须大于0");
        }
        this.queue = new Object[initialCapacity];
        this.comparator = comparator;
    }

    @Override
    public boolean add(E e) {
        return offer(e);
    }

    @Override
    public boolean offer(E e) {
        if (e == null) {
            throw new NullPointerException();
        }

        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            int i = size;
            // 检查是否需要扩容
            if (i >= queue.length) {
                grow(i + 1);
            }

            // 插入新元素并维护排序
            if (i == 0) {
                // 如果是第一个元素，直接插入
                queue[0] = e;
            } else {
                // 插入元素并进行排序
                queue[i] = e;
                sortWithComparator(i, e);
            }

            size++;
            return true;
        } finally {
            lock.unlock();
        }
    }


    /**
     * 根据比较器对数组中的元素进行排序
     */
    private void sortWithComparator(int k, E x) {
        if (comparator != null) {
            // 使用自定义比较器进行排序
            insertionSortWithComparator();
        }
    }

    /**
     * 使用比较器进行插入排序
     */
    private void insertionSortWithComparator() {
        for (int i = 1; i < size + 1; i++) {
            E key = (E) queue[i];
            int j = i - 1;

            // 将大于key的元素向后移动
            while (j >= 0 && comparator.compare((E) queue[j], key) > 0) {
                queue[j + 1] = queue[j];
                j--;
            }
            queue[j + 1] = key;
        }
    }

    @Override
    public E poll() {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            // 检查队列是否为空
            if (size == 0) {
                return null;
            }

            // 获取第一个元素
            E result = (E) queue[0];

            // 将剩余元素往前移动一位
            for (int i = 0; i < size - 1; i++) {
                queue[i] = queue[i + 1];
            }

            // 清空最后一个位置并减少size
            queue[size - 1] = null;
            size--;

            return result;
        } finally {
            lock.unlock();
        }
    }

    public E pollLast() {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            // 检查队列是否为空
            if (size == 0) {
                return null;
            }

            // 获取第一个元素
            E result = (E) queue[size - 1];
            // 清空最后一个位置并减少size
            queue[size - 1] = null;
            size--;

            return result;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public E peek() {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            return (E) queue[0];
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取指定索引处的元素
     * @param index
     */
    public E get(int index) {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            if (size > index) {
                return (E) queue[index];
            }
            return null;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 扩容方法
     */
    private void grow(int minCapacity) {
        int oldCapacity = queue.length;
        // 如果容量小于64，则扩容50%，否则扩容100%
        int newCapacity = oldCapacity +
                ((oldCapacity < 64) ? (oldCapacity + 1) : (oldCapacity >> 1));

        // 检查是否超过最大数组大小
        if (newCapacity - Integer.MAX_VALUE - 8 > 0) {
            newCapacity = (minCapacity > Integer.MAX_VALUE - 8) ?
                    Integer.MAX_VALUE :
                    Integer.MAX_VALUE - 8;
        }
        queue = Arrays.copyOf(queue, newCapacity);
    }

    @Override
    public boolean removeAll(Collection<?> c) {
        Objects.requireNonNull(c);
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            boolean modified = false;
            for (Object o : c) {
                if (remove(o)) {
                    modified = true;
                }
            }
            return modified;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public boolean remove(Object o) {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            int i = indexOf(o);
            if (i == -1)
                return false;
            removeAt(i);
            return true;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public boolean removeIf(Predicate<? super E> filter) {
        Objects.requireNonNull(filter);
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            boolean removed = false;
            // 从后往前遍历，避免删除元素时索引变化的问题
            for (int i = size - 1; i >= 0; i--) {
                if (filter.test((E) queue[i])) {
                    removeAt(i);
                    removed = true;
                }
            }
            return removed;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取指定元素的索引，如果元素不存在则返回-1
     * @param o
     */
    private int indexOf(Object o) {
        if (o != null) {
            final Object[] es = queue;
            for (int i = 0, n = size; i < n; i++)
                if (o.equals(es[i]))
                    return i;
        }
        return -1;
    }


    /**
     * 根据指定条件获取指定元素的索引，如果元素不存在则返回-1
     * @param predicate
     */
    public int indexOf(Predicate<E> predicate) {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            final Object[] es = queue;
            for (int i = 0, n = size; i < n; i++)
                if (predicate.test((E)es[i]))
                    return i;
            return -1;
        } finally {
            lock.unlock();
        }
    }

    void removeEq(Object o) {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            final Object[] es = queue;
            for (int i = 0, n = size; i < n; i++) {
                if (o == es[i]) {
                    removeAt(i);
                    break;
                }
            }
        } finally {
            lock.unlock();
        }
    }

    public void removeAt(int i) {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            // 检查索引是否有效
            if (i < 0 || i >= size) {
                throw new IndexOutOfBoundsException("索引: " + i + ", 大小: " + size);
            }

            // 将要删除位置的元素置为null，帮助垃圾回收
            queue[i] = null;

            // 将索引i之后的所有元素向前移动一位
            for (int j = i; j < size - 1; j++) {
                queue[j] = queue[j + 1];
            }

            // 清空最后一个位置
            queue[size - 1] = null;

            // 减少size
            size--;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public <T> T[] toArray(T[] a) {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            int n = size;
            if (a.length < n)
                // Make a new array of a's runtime type, but my contents:
                return (T[]) Arrays.copyOf(queue, size, a.getClass());
            System.arraycopy(queue, 0, a, 0, n);
            if (a.length > n)
                a[n] = null;
            return a;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Object[] toArray() {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            return Arrays.copyOf(queue, size);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 遍历队列
     * @param action The action to be performed for each element
     */
    @Override
    public void forEach(Consumer<? super E> action) {
        Objects.requireNonNull(action);
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            final Object[] es = queue;
            for (int i = 0, n = size; i < n; i++)
                action.accept((E) es[i]);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Iterator<E> iterator() {
        return new Itr(toArray());
    }

    /**
     * Snapshot iterator that works off copy of underlying q array.
     */
    final class Itr implements Iterator<E> {
        final Object[] array; // Array of all elements
        int cursor;           // index of next element to return
        int lastRet = -1;     // index of last element, or -1 if no such

        Itr(Object[] array) {
            this.array = array;
        }

        public boolean hasNext() {
            return cursor < array.length;
        }

        public E next() {
            if (cursor >= array.length)
                throw new NoSuchElementException();
            return (E)array[lastRet = cursor++];
        }

        public void remove() {
            if (lastRet < 0)
                throw new IllegalStateException();
            removeEq(array[lastRet]);
            lastRet = -1;
        }

        public void forEachRemaining(Consumer<? super E> action) {
            Objects.requireNonNull(action);
            final Object[] es = array;
            int i;
            if ((i = cursor) < es.length) {
                lastRet = -1;
                cursor = es.length;
                for (; i < es.length; i++)
                    action.accept((E) es[i]);
                lastRet = es.length - 1;
            }
        }
    }


    @Override
    public int size() {
        lock.lock();
        try {
            return size;
        } finally {
            lock.unlock();
        }
    }

}
