import{t as e,_ as t,d as n,r as o,c as r,l as a,y as i,b as s,A as l,a8 as u,g as p,f,k as c,n as d,i as v,z as g,b7 as m,X as h,bz as b,ac as y,Q as w,bA as x,b4 as O,m as R,o as A,I as E,bB as C,e as S,h as T,w as j,W as k,bC as B,M,H as P,s as F,bD as L,bE as D,aV as I,q as _,K as H,bF as W,B as $,b6 as q,ba as z,ab as U,ah as N,au as V,bG as K,bH as Z,T as X,Z as Y,bI as G,bJ as J,bK as Q,F as ee}from"./index.Ckm1SagX.js";import{u as te}from"./index.BLy3nyPI.js";import{t as ne,E as oe}from"./index.C0OsJ5su.js";import{u as re,a as ae}from"./index.Cn1QDWeG.js";import{b as ie}from"./aria.C1IWO_Rd.js";import{a as se,u as le}from"./index.Dh_vcBr5.js";import{E as ue,t as pe}from"./focus-trap.Bd_uzvDY.js";import{f as fe}from"./use-form-common-props.BSYTvb6G.js";const ce=Symbol("popper"),de=Symbol("popperContent"),ve=["dialog","grid","group","listbox","menu","navigation","tooltip","tree"],ge=e({role:{type:String,values:ve,default:"tooltip"}}),me=n({name:"ElPopper",inheritAttrs:!1});var he=t(n({...me,props:ge,setup(e,{expose:t}){const n=e,s={triggerRef:o(),popperInstanceRef:o(),contentRef:o(),referenceRef:o(),role:r(()=>n.role)};return t(s),i(ce,s),(e,t)=>a(e.$slots,"default")}}),[["__file","popper.vue"]]);const be=n({name:"ElPopperArrow",inheritAttrs:!1});var ye=t(n({...be,setup(e,{expose:t}){const n=s("popper"),{arrowRef:o,arrowStyle:r}=l(de,void 0);return u(()=>{o.value=void 0}),t({arrowRef:o}),(e,t)=>(f(),p("span",{ref_key:"arrowRef",ref:o,class:d(v(n).e("arrow")),style:c(v(r)),"data-popper-arrow":""},null,6))}}),[["__file","arrow.vue"]]);const we=e({virtualRef:{type:g(Object)},virtualTriggering:Boolean,onMouseenter:{type:g(Function)},onMouseleave:{type:g(Function)},onClick:{type:g(Function)},onKeydown:{type:g(Function)},onFocus:{type:g(Function)},onBlur:{type:g(Function)},onContextmenu:{type:g(Function)},id:String,open:Boolean}),xe=Symbol("elForwardRef"),Oe=n({name:"ElOnlyChild",setup(e,{slots:t,attrs:n}){var o;const r=l(xe),a=(i=null!=(o=null==r?void 0:r.setForwardRef)?o:m,{mounted(e){i(e)},updated(e){i(e)},unmounted(){i(null)}});var i;return()=>{var e;const o=null==(e=t.default)?void 0:e.call(t,n);if(!o)return null;if(o.length>1)return null;const r=Re(o);return r?h(b(r,n),[[a]]):null}}});function Re(e){if(!e)return null;const t=e;for(const n of t){if(y(n))switch(n.type){case O:continue;case x:case"svg":return Ae(n);case w:return Re(n.children);default:return n}return Ae(n)}return null}function Ae(e){const t=s("only-child");return R("span",{class:t.e("content")},[e])}const Ee=n({name:"ElPopperTrigger",inheritAttrs:!1});var Ce=t(n({...Ee,props:we,setup(e,{expose:t}){const n=e,{role:o,triggerRef:s}=l(ce,void 0);var p;p=s,i(xe,{setForwardRef:e=>{p.value=e}});const c=r(()=>g.value?n.id:void 0),d=r(()=>{if(o&&"tooltip"===o.value)return n.open&&n.id?n.id:void 0}),g=r(()=>{if(o&&"tooltip"!==o.value)return o.value}),m=r(()=>g.value?`${n.open}`:void 0);let h;const b=["onMouseenter","onMouseleave","onClick","onKeydown","onFocus","onBlur","onContextmenu"];return A(()=>{E(()=>n.virtualRef,e=>{e&&(s.value=B(e))},{immediate:!0}),E(s,(e,t)=>{null==h||h(),h=void 0,C(e)&&(b.forEach(o=>{var r;const a=n[o];a&&(e.addEventListener(o.slice(2).toLowerCase(),a),null==(r=null==t?void 0:t.removeEventListener)||r.call(t,o.slice(2).toLowerCase(),a))}),ie(e)&&(h=E([c,d,g,m],t=>{["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach((n,o)=>{M(t[o])?e.removeAttribute(n):e.setAttribute(n,t[o])})},{immediate:!0}))),C(t)&&ie(t)&&["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach(e=>t.removeAttribute(e))},{immediate:!0})}),u(()=>{if(null==h||h(),h=void 0,s.value&&C(s.value)){const e=s.value;b.forEach(t=>{const o=n[t];o&&e.removeEventListener(t.slice(2).toLowerCase(),o)}),s.value=void 0}}),t({triggerRef:s}),(e,t)=>e.virtualTriggering?T("v-if",!0):(f(),S(v(Oe),k({key:0},e.$attrs,{"aria-controls":v(c),"aria-describedby":v(d),"aria-expanded":v(m),"aria-haspopup":v(g)}),{default:j(()=>[a(e.$slots,"default")]),_:3},16,["aria-controls","aria-describedby","aria-expanded","aria-haspopup"]))}}),[["__file","trigger.vue"]]),Se="top",Te="bottom",je="right",ke="left",Be="auto",Me=[Se,Te,je,ke],Pe="start",Fe="end",Le="viewport",De="popper",Ie=Me.reduce(function(e,t){return e.concat([t+"-"+Pe,t+"-"+Fe])},[]),_e=[].concat(Me,[Be]).reduce(function(e,t){return e.concat([t,t+"-"+Pe,t+"-"+Fe])},[]),He=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function We(e){return e?(e.nodeName||"").toLowerCase():null}function $e(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function qe(e){return e instanceof $e(e).Element||e instanceof Element}function ze(e){return e instanceof $e(e).HTMLElement||e instanceof HTMLElement}function Ue(e){return"undefined"!=typeof ShadowRoot&&(e instanceof $e(e).ShadowRoot||e instanceof ShadowRoot)}var Ne={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},o=t.attributes[e]||{},r=t.elements[e];!ze(r)||!We(r)||(Object.assign(r.style,n),Object.keys(o).forEach(function(e){var t=o[e];!1===t?r.removeAttribute(e):r.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var o=t.elements[e],r=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});!ze(o)||!We(o)||(Object.assign(o.style,a),Object.keys(r).forEach(function(e){o.removeAttribute(e)}))})}},requires:["computeStyles"]};function Ve(e){return e.split("-")[0]}var Ke=Math.max,Ze=Math.min,Xe=Math.round;function Ye(e,t){void 0===t&&(t=!1);var n=e.getBoundingClientRect(),o=1,r=1;if(ze(e)&&t){var a=e.offsetHeight,i=e.offsetWidth;i>0&&(o=Xe(n.width)/i||1),a>0&&(r=Xe(n.height)/a||1)}return{width:n.width/o,height:n.height/r,top:n.top/r,right:n.right/o,bottom:n.bottom/r,left:n.left/o,x:n.left/o,y:n.top/r}}function Ge(e){var t=Ye(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function Je(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Ue(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function Qe(e){return $e(e).getComputedStyle(e)}function et(e){return["table","td","th"].indexOf(We(e))>=0}function tt(e){return((qe(e)?e.ownerDocument:e.document)||window.document).documentElement}function nt(e){return"html"===We(e)?e:e.assignedSlot||e.parentNode||(Ue(e)?e.host:null)||tt(e)}function ot(e){return ze(e)&&"fixed"!==Qe(e).position?e.offsetParent:null}function rt(e){for(var t=$e(e),n=ot(e);n&&et(n)&&"static"===Qe(n).position;)n=ot(n);return n&&("html"===We(n)||"body"===We(n)&&"static"===Qe(n).position)?t:n||function(e){var t=-1!==navigator.userAgent.toLowerCase().indexOf("firefox");if(-1!==navigator.userAgent.indexOf("Trident")&&ze(e)&&"fixed"===Qe(e).position)return null;var n=nt(e);for(Ue(n)&&(n=n.host);ze(n)&&["html","body"].indexOf(We(n))<0;){var o=Qe(n);if("none"!==o.transform||"none"!==o.perspective||"paint"===o.contain||-1!==["transform","perspective"].indexOf(o.willChange)||t&&"filter"===o.willChange||t&&o.filter&&"none"!==o.filter)return n;n=n.parentNode}return null}(e)||t}function at(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function it(e,t,n){return Ke(e,Ze(t,n))}function st(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function lt(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}var ut={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,r=e.options,a=n.elements.arrow,i=n.modifiersData.popperOffsets,s=Ve(n.placement),l=at(s),u=[ke,je].indexOf(s)>=0?"height":"width";if(a&&i){var p=function(e,t){return st("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:lt(e,Me))}(r.padding,n),f=Ge(a),c="y"===l?Se:ke,d="y"===l?Te:je,v=n.rects.reference[u]+n.rects.reference[l]-i[l]-n.rects.popper[u],g=i[l]-n.rects.reference[l],m=rt(a),h=m?"y"===l?m.clientHeight||0:m.clientWidth||0:0,b=v/2-g/2,y=p[c],w=h-f[u]-p[d],x=h/2-f[u]/2+b,O=it(y,x,w),R=l;n.modifiersData[o]=((t={})[R]=O,t.centerOffset=O-x,t)}},effect:function(e){var t=e.state,n=e.options.element,o=void 0===n?"[data-popper-arrow]":n;null!=o&&("string"==typeof o&&!(o=t.elements.popper.querySelector(o))||!Je(t.elements.popper,o)||(t.elements.arrow=o))},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function pt(e){return e.split("-")[1]}var ft={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ct(e){var t,n=e.popper,o=e.popperRect,r=e.placement,a=e.variation,i=e.offsets,s=e.position,l=e.gpuAcceleration,u=e.adaptive,p=e.roundOffsets,f=e.isFixed,c=i.x,d=void 0===c?0:c,v=i.y,g=void 0===v?0:v,m="function"==typeof p?p({x:d,y:g}):{x:d,y:g};d=m.x,g=m.y;var h=i.hasOwnProperty("x"),b=i.hasOwnProperty("y"),y=ke,w=Se,x=window;if(u){var O=rt(n),R="clientHeight",A="clientWidth";if(O===$e(n)&&("static"!==Qe(O=tt(n)).position&&"absolute"===s&&(R="scrollHeight",A="scrollWidth")),r===Se||(r===ke||r===je)&&a===Fe)w=Te,g-=(f&&O===x&&x.visualViewport?x.visualViewport.height:O[R])-o.height,g*=l?1:-1;if(r===ke||(r===Se||r===Te)&&a===Fe)y=je,d-=(f&&O===x&&x.visualViewport?x.visualViewport.width:O[A])-o.width,d*=l?1:-1}var E,C=Object.assign({position:s},u&&ft),S=!0===p?function(e){var t=e.x,n=e.y,o=window.devicePixelRatio||1;return{x:Xe(t*o)/o||0,y:Xe(n*o)/o||0}}({x:d,y:g}):{x:d,y:g};return d=S.x,g=S.y,l?Object.assign({},C,((E={})[w]=b?"0":"",E[y]=h?"0":"",E.transform=(x.devicePixelRatio||1)<=1?"translate("+d+"px, "+g+"px)":"translate3d("+d+"px, "+g+"px, 0)",E)):Object.assign({},C,((t={})[w]=b?g+"px":"",t[y]=h?d+"px":"",t.transform="",t))}var dt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,r=void 0===o||o,a=n.adaptive,i=void 0===a||a,s=n.roundOffsets,l=void 0===s||s,u={placement:Ve(t.placement),variation:pt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ct(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ct(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},vt={passive:!0};var gt={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,r=o.scroll,a=void 0===r||r,i=o.resize,s=void 0===i||i,l=$e(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&u.forEach(function(e){e.addEventListener("scroll",n.update,vt)}),s&&l.addEventListener("resize",n.update,vt),function(){a&&u.forEach(function(e){e.removeEventListener("scroll",n.update,vt)}),s&&l.removeEventListener("resize",n.update,vt)}},data:{}},mt={left:"right",right:"left",bottom:"top",top:"bottom"};function ht(e){return e.replace(/left|right|bottom|top/g,function(e){return mt[e]})}var bt={start:"end",end:"start"};function yt(e){return e.replace(/start|end/g,function(e){return bt[e]})}function wt(e){var t=$e(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function xt(e){return Ye(tt(e)).left+wt(e).scrollLeft}function Ot(e){var t=Qe(e),n=t.overflow,o=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+o)}function Rt(e){return["html","body","#document"].indexOf(We(e))>=0?e.ownerDocument.body:ze(e)&&Ot(e)?e:Rt(nt(e))}function At(e,t){var n;void 0===t&&(t=[]);var o=Rt(e),r=o===(null==(n=e.ownerDocument)?void 0:n.body),a=$e(o),i=r?[a].concat(a.visualViewport||[],Ot(o)?o:[]):o,s=t.concat(i);return r?s:s.concat(At(nt(i)))}function Et(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Ct(e,t){return t===Le?Et(function(e){var t=$e(e),n=tt(e),o=t.visualViewport,r=n.clientWidth,a=n.clientHeight,i=0,s=0;return o&&(r=o.width,a=o.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(i=o.offsetLeft,s=o.offsetTop)),{width:r,height:a,x:i+xt(e),y:s}}(e)):qe(t)?function(e){var t=Ye(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}(t):Et(function(e){var t,n=tt(e),o=wt(e),r=null==(t=e.ownerDocument)?void 0:t.body,a=Ke(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),i=Ke(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),s=-o.scrollLeft+xt(e),l=-o.scrollTop;return"rtl"===Qe(r||n).direction&&(s+=Ke(n.clientWidth,r?r.clientWidth:0)-a),{width:a,height:i,x:s,y:l}}(tt(e)))}function St(e,t,n){var o="clippingParents"===t?function(e){var t=At(nt(e)),n=["absolute","fixed"].indexOf(Qe(e).position)>=0&&ze(e)?rt(e):e;return qe(n)?t.filter(function(e){return qe(e)&&Je(e,n)&&"body"!==We(e)}):[]}(e):[].concat(t),r=[].concat(o,[n]),a=r[0],i=r.reduce(function(t,n){var o=Ct(e,n);return t.top=Ke(o.top,t.top),t.right=Ze(o.right,t.right),t.bottom=Ze(o.bottom,t.bottom),t.left=Ke(o.left,t.left),t},Ct(e,a));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function Tt(e){var t,n=e.reference,o=e.element,r=e.placement,a=r?Ve(r):null,i=r?pt(r):null,s=n.x+n.width/2-o.width/2,l=n.y+n.height/2-o.height/2;switch(a){case Se:t={x:s,y:n.y-o.height};break;case Te:t={x:s,y:n.y+n.height};break;case je:t={x:n.x+n.width,y:l};break;case ke:t={x:n.x-o.width,y:l};break;default:t={x:n.x,y:n.y}}var u=a?at(a):null;if(null!=u){var p="y"===u?"height":"width";switch(i){case Pe:t[u]=t[u]-(n[p]/2-o[p]/2);break;case Fe:t[u]=t[u]+(n[p]/2-o[p]/2)}}return t}function jt(e,t){void 0===t&&(t={});var n=t,o=n.placement,r=void 0===o?e.placement:o,a=n.boundary,i=void 0===a?"clippingParents":a,s=n.rootBoundary,l=void 0===s?Le:s,u=n.elementContext,p=void 0===u?De:u,f=n.altBoundary,c=void 0!==f&&f,d=n.padding,v=void 0===d?0:d,g=st("number"!=typeof v?v:lt(v,Me)),m=p===De?"reference":De,h=e.rects.popper,b=e.elements[c?m:p],y=St(qe(b)?b:b.contextElement||tt(e.elements.popper),i,l),w=Ye(e.elements.reference),x=Tt({reference:w,element:h,placement:r}),O=Et(Object.assign({},h,x)),R=p===De?O:w,A={top:y.top-R.top+g.top,bottom:R.bottom-y.bottom+g.bottom,left:y.left-R.left+g.left,right:R.right-y.right+g.right},E=e.modifiersData.offset;if(p===De&&E){var C=E[r];Object.keys(A).forEach(function(e){var t=[je,Te].indexOf(e)>=0?1:-1,n=[Se,Te].indexOf(e)>=0?"y":"x";A[e]+=C[n]*t})}return A}var kt={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var r=n.mainAxis,a=void 0===r||r,i=n.altAxis,s=void 0===i||i,l=n.fallbackPlacements,u=n.padding,p=n.boundary,f=n.rootBoundary,c=n.altBoundary,d=n.flipVariations,v=void 0===d||d,g=n.allowedAutoPlacements,m=t.options.placement,h=Ve(m),b=l||(h===m||!v?[ht(m)]:function(e){if(Ve(e)===Be)return[];var t=ht(e);return[yt(e),t,yt(t)]}(m)),y=[m].concat(b).reduce(function(e,n){return e.concat(Ve(n)===Be?function(e,t){void 0===t&&(t={});var n=t,o=n.placement,r=n.boundary,a=n.rootBoundary,i=n.padding,s=n.flipVariations,l=n.allowedAutoPlacements,u=void 0===l?_e:l,p=pt(o),f=p?s?Ie:Ie.filter(function(e){return pt(e)===p}):Me,c=f.filter(function(e){return u.indexOf(e)>=0});0===c.length&&(c=f);var d=c.reduce(function(t,n){return t[n]=jt(e,{placement:n,boundary:r,rootBoundary:a,padding:i})[Ve(n)],t},{});return Object.keys(d).sort(function(e,t){return d[e]-d[t]})}(t,{placement:n,boundary:p,rootBoundary:f,padding:u,flipVariations:v,allowedAutoPlacements:g}):n)},[]),w=t.rects.reference,x=t.rects.popper,O=new Map,R=!0,A=y[0],E=0;E<y.length;E++){var C=y[E],S=Ve(C),T=pt(C)===Pe,j=[Se,Te].indexOf(S)>=0,k=j?"width":"height",B=jt(t,{placement:C,boundary:p,rootBoundary:f,altBoundary:c,padding:u}),M=j?T?je:ke:T?Te:Se;w[k]>x[k]&&(M=ht(M));var P=ht(M),F=[];if(a&&F.push(B[S]<=0),s&&F.push(B[M]<=0,B[P]<=0),F.every(function(e){return e})){A=C,R=!1;break}O.set(C,F)}if(R)for(var L=function(e){var t=y.find(function(t){var n=O.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return A=t,"break"},D=v?3:1;D>0;D--){if("break"===L(D))break}t.placement!==A&&(t.modifiersData[o]._skip=!0,t.placement=A,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Bt(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Mt(e){return[Se,je,Te,ke].some(function(t){return e[t]>=0})}var Pt={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,o=t.rects.reference,r=t.rects.popper,a=t.modifiersData.preventOverflow,i=jt(t,{elementContext:"reference"}),s=jt(t,{altBoundary:!0}),l=Bt(i,o),u=Bt(s,r,a),p=Mt(l),f=Mt(u);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:p,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":f})}};var Ft={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,r=n.offset,a=void 0===r?[0,0]:r,i=_e.reduce(function(e,n){return e[n]=function(e,t,n){var o=Ve(e),r=[ke,Se].indexOf(o)>=0?-1:1,a="function"==typeof n?n(Object.assign({},t,{placement:e})):n,i=a[0],s=a[1];return i=i||0,s=(s||0)*r,[ke,je].indexOf(o)>=0?{x:s,y:i}:{x:i,y:s}}(n,t.rects,a),e},{}),s=i[t.placement],l=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[o]=i}};var Lt={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Tt({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}};var Dt={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,r=n.mainAxis,a=void 0===r||r,i=n.altAxis,s=void 0!==i&&i,l=n.boundary,u=n.rootBoundary,p=n.altBoundary,f=n.padding,c=n.tether,d=void 0===c||c,v=n.tetherOffset,g=void 0===v?0:v,m=jt(t,{boundary:l,rootBoundary:u,padding:f,altBoundary:p}),h=Ve(t.placement),b=pt(t.placement),y=!b,w=at(h),x=function(e){return"x"===e?"y":"x"}(w),O=t.modifiersData.popperOffsets,R=t.rects.reference,A=t.rects.popper,E="function"==typeof g?g(Object.assign({},t.rects,{placement:t.placement})):g,C="number"==typeof E?{mainAxis:E,altAxis:E}:Object.assign({mainAxis:0,altAxis:0},E),S=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,T={x:0,y:0};if(O){if(a){var j,k="y"===w?Se:ke,B="y"===w?Te:je,M="y"===w?"height":"width",P=O[w],F=P+m[k],L=P-m[B],D=d?-A[M]/2:0,I=b===Pe?R[M]:A[M],_=b===Pe?-A[M]:-R[M],H=t.elements.arrow,W=d&&H?Ge(H):{width:0,height:0},$=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},q=$[k],z=$[B],U=it(0,R[M],W[M]),N=y?R[M]/2-D-U-q-C.mainAxis:I-U-q-C.mainAxis,V=y?-R[M]/2+D+U+z+C.mainAxis:_+U+z+C.mainAxis,K=t.elements.arrow&&rt(t.elements.arrow),Z=K?"y"===w?K.clientTop||0:K.clientLeft||0:0,X=null!=(j=null==S?void 0:S[w])?j:0,Y=P+V-X,G=it(d?Ze(F,P+N-X-Z):F,P,d?Ke(L,Y):L);O[w]=G,T[w]=G-P}if(s){var J,Q="x"===w?Se:ke,ee="x"===w?Te:je,te=O[x],ne="y"===x?"height":"width",oe=te+m[Q],re=te-m[ee],ae=-1!==[Se,ke].indexOf(h),ie=null!=(J=null==S?void 0:S[x])?J:0,se=ae?oe:te-R[ne]-A[ne]-ie+C.altAxis,le=ae?te+R[ne]+A[ne]-ie-C.altAxis:re,ue=d&&ae?function(e,t,n){var o=it(e,t,n);return o>n?n:o}(se,te,le):it(d?se:oe,te,d?le:re);O[x]=ue,T[x]=ue-te}t.modifiersData[o]=T}},requiresIfExists:["offset"]};function It(e,t,n){void 0===n&&(n=!1);var o=ze(t),r=ze(t)&&function(e){var t=e.getBoundingClientRect(),n=Xe(t.width)/e.offsetWidth||1,o=Xe(t.height)/e.offsetHeight||1;return 1!==n||1!==o}(t),a=tt(t),i=Ye(e,r),s={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(o||!o&&!n)&&(("body"!==We(t)||Ot(a))&&(s=function(e){return e!==$e(e)&&ze(e)?function(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}(e):wt(e)}(t)),ze(t)?((l=Ye(t,!0)).x+=t.clientLeft,l.y+=t.clientTop):a&&(l.x=xt(a))),{x:i.left+s.scrollLeft-l.x,y:i.top+s.scrollTop-l.y,width:i.width,height:i.height}}function _t(e){var t=new Map,n=new Set,o=[];function r(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach(function(e){if(!n.has(e)){var o=t.get(e);o&&r(o)}}),o.push(e)}return e.forEach(function(e){t.set(e.name,e)}),e.forEach(function(e){n.has(e.name)||r(e)}),o}function Ht(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}var Wt={placement:"bottom",modifiers:[],strategy:"absolute"};function $t(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function qt(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,o=void 0===n?[]:n,r=t.defaultOptions,a=void 0===r?Wt:r;return function(e,t,n){void 0===n&&(n=a);var r={placement:"bottom",orderedModifiers:[],options:Object.assign({},Wt,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},i=[],s=!1,l={state:r,setOptions:function(n){var s="function"==typeof n?n(r.options):n;u(),r.options=Object.assign({},a,r.options,s),r.scrollParents={reference:qe(e)?At(e):e.contextElement?At(e.contextElement):[],popper:At(t)};var p=function(e){var t=_t(e);return He.reduce(function(e,n){return e.concat(t.filter(function(e){return e.phase===n}))},[])}(function(e){var t=e.reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{});return Object.keys(t).map(function(e){return t[e]})}([].concat(o,r.options.modifiers)));return r.orderedModifiers=p.filter(function(e){return e.enabled}),r.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,o=void 0===n?{}:n,a=e.effect;if("function"==typeof a){var s=a({state:r,name:t,instance:l,options:o}),u=function(){};i.push(s||u)}}),l.update()},forceUpdate:function(){if(!s){var e=r.elements,t=e.reference,n=e.popper;if($t(t,n)){r.rects={reference:It(t,rt(n),"fixed"===r.options.strategy),popper:Ge(n)},r.reset=!1,r.placement=r.options.placement,r.orderedModifiers.forEach(function(e){return r.modifiersData[e.name]=Object.assign({},e.data)});for(var o=0;o<r.orderedModifiers.length;o++)if(!0!==r.reset){var a=r.orderedModifiers[o],i=a.fn,u=a.options,p=void 0===u?{}:u,f=a.name;"function"==typeof i&&(r=i({state:r,options:p,name:f,instance:l})||r)}else r.reset=!1,o=-1}}},update:Ht(function(){return new Promise(function(e){l.forceUpdate(),e(r)})}),destroy:function(){u(),s=!0}};if(!$t(e,t))return l;function u(){i.forEach(function(e){return e()}),i=[]}return l.setOptions(n).then(function(e){!s&&n.onFirstUpdate&&n.onFirstUpdate(e)}),l}}qt(),qt({defaultModifiers:[gt,Lt,dt,Ne]});var zt=qt({defaultModifiers:[gt,Lt,dt,Ne,Ft,kt,Dt,ut,Pt]});const Ut=e({arrowOffset:{type:Number,default:5}}),Nt=e({boundariesPadding:{type:Number,default:0},fallbackPlacements:{type:g(Array),default:void 0},gpuAcceleration:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:String,values:_e,default:"bottom"},popperOptions:{type:g(Object),default:()=>({})},strategy:{type:String,values:["fixed","absolute"],default:"absolute"}}),Vt=e({...Nt,...Ut,id:String,style:{type:g([String,Array,Object])},className:{type:g([String,Array,Object])},effect:{type:g(String),default:"dark"},visible:Boolean,enterable:{type:Boolean,default:!0},pure:Boolean,focusOnShow:Boolean,trapping:Boolean,popperClass:{type:g([String,Array,Object])},popperStyle:{type:g([String,Array,Object])},referenceEl:{type:g(Object)},triggerTargetEl:{type:g(Object)},stopPopperMouseEvent:{type:Boolean,default:!0},virtualTriggering:Boolean,zIndex:Number,...te(["ariaLabel"])}),Kt={mouseenter:e=>e instanceof MouseEvent,mouseleave:e=>e instanceof MouseEvent,focus:()=>!0,blur:()=>!0,close:()=>!0},Zt=(e,t=[])=>{const{placement:n,strategy:o,popperOptions:r}=e,a={placement:n,strategy:o,...r,modifiers:[...Xt(e),...t]};return function(e,t){t&&(e.modifiers=[...e.modifiers,...null!=t?t:[]])}(a,null==r?void 0:r.modifiers),a};function Xt(e){const{offset:t,gpuAcceleration:n,fallbackPlacements:o}=e;return[{name:"offset",options:{offset:[0,null!=t?t:12]}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5,fallbackPlacements:o}},{name:"computeStyles",options:{gpuAcceleration:n}}]}const Yt=(e,t,n={})=>{const a={name:"updateState",enabled:!0,phase:"write",fn:({state:e})=>{const t=function(e){const t=Object.keys(e.elements),n=L(t.map(t=>[t,e.styles[t]||{}])),o=L(t.map(t=>[t,e.attributes[t]]));return{styles:n,attributes:o}}(e);Object.assign(l.value,t)},requires:["computeStyles"]},i=r(()=>{const{onFirstUpdate:e,placement:t,strategy:o,modifiers:r}=v(n);return{onFirstUpdate:e,placement:t||"bottom",strategy:o||"absolute",modifiers:[...r||[],a,{name:"applyStyles",enabled:!1}]}}),s=F(),l=o({styles:{popper:{position:v(i).strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),p=()=>{s.value&&(s.value.destroy(),s.value=void 0)};return E(i,e=>{const t=v(s);t&&t.setOptions(e)},{deep:!0}),E([e,t],([e,t])=>{p(),e&&t&&(s.value=zt(e,t,v(i)))}),u(()=>{p()}),{state:r(()=>{var e;return{...(null==(e=v(s))?void 0:e.state)||{}}}),styles:r(()=>v(l).styles),attributes:r(()=>v(l).attributes),update:()=>{var e;return null==(e=v(s))?void 0:e.update()},forceUpdate:()=>{var e;return null==(e=v(s))?void 0:e.forceUpdate()},instanceRef:r(()=>v(s))}};const Gt=e=>{const{popperInstanceRef:t,contentRef:n,triggerRef:a,role:i}=l(ce,void 0),s=o(),u=r(()=>e.arrowOffset),p=r(()=>({name:"eventListeners",enabled:!!e.visible})),f=r(()=>{var e;const t=v(s),n=null!=(e=v(u))?e:0;return{name:"arrow",enabled:(o=t,!(void 0===o)),options:{element:t,padding:n}};var o}),c=r(()=>({onFirstUpdate:()=>{b()},...Zt(e,[v(f),v(p)])})),d=r(()=>(e=>{if(P)return B(e)})(e.referenceEl)||v(a)),{attributes:g,state:m,styles:h,update:b,forceUpdate:y,instanceRef:w}=Yt(d,n,c);return E(w,e=>t.value=e,{flush:"sync"}),A(()=>{E(()=>{var e;return null==(e=v(d))?void 0:e.getBoundingClientRect()},()=>{b()})}),{attributes:g,arrowRef:s,contentRef:n,instanceRef:w,state:m,styles:h,role:i,forceUpdate:y,update:b}},Jt=n({name:"ElPopperContent"});var Qt=t(n({...Jt,props:Vt,emits:Kt,setup(e,{expose:t,emit:n}){const c=e,{focusStartRef:d,trapped:g,onFocusAfterReleased:h,onFocusAfterTrapped:b,onFocusInTrap:y,onFocusoutPrevented:w,onReleaseRequested:x}=((e,t)=>{const n=o(!1),r=o();return{focusStartRef:r,trapped:n,onFocusAfterReleased:e=>{var n;"pointer"!==(null==(n=e.detail)?void 0:n.focusReason)&&(r.value="first",t("blur"))},onFocusAfterTrapped:()=>{t("focus")},onFocusInTrap:t=>{e.visible&&!n.value&&(t.target&&(r.value=t.target),n.value=!0)},onFocusoutPrevented:t=>{e.trapping||("pointer"===t.detail.focusReason&&t.preventDefault(),n.value=!1)},onReleaseRequested:()=>{n.value=!1,t("close")}}})(c,n),{attributes:O,arrowRef:S,contentRef:T,styles:B,instanceRef:P,role:F,update:L}=Gt(c),{ariaModal:_,arrowStyle:H,contentAttrs:W,contentClass:$,contentStyle:q,updateZIndex:z}=((e,{attributes:t,styles:n,role:a})=>{const{nextZIndex:i}=D(),l=s("popper"),u=r(()=>v(t).popper),p=o(I(e.zIndex)?e.zIndex:i()),f=r(()=>[l.b(),l.is("pure",e.pure),l.is(e.effect),e.popperClass]),c=r(()=>[{zIndex:v(p)},v(n).popper,e.popperStyle||{}]);return{ariaModal:r(()=>"dialog"===a.value?"false":void 0),arrowStyle:r(()=>v(n).arrow||{}),contentAttrs:u,contentClass:f,contentStyle:c,contentZIndex:p,updateZIndex:()=>{p.value=I(e.zIndex)?e.zIndex:i()}}})(c,{styles:B,attributes:O,role:F}),U=l(fe,void 0);let N;i(de,{arrowStyle:H,arrowRef:S}),U&&i(fe,{...U,addInputId:m,removeInputId:m});const V=(e=!0)=>{L(),e&&z()},K=()=>{V(!1),c.visible&&c.focusOnShow?g.value=!0:!1===c.visible&&(g.value=!1)};return A(()=>{E(()=>c.triggerTargetEl,(e,t)=>{null==N||N(),N=void 0;const n=v(e||T.value),o=v(t||T.value);C(n)&&(N=E([F,()=>c.ariaLabel,_,()=>c.id],e=>{["role","aria-label","aria-modal","id"].forEach((t,o)=>{M(e[o])?n.removeAttribute(t):n.setAttribute(t,e[o])})},{immediate:!0})),o!==n&&C(o)&&["role","aria-label","aria-modal","id"].forEach(e=>{o.removeAttribute(e)})},{immediate:!0}),E(()=>c.visible,K,{immediate:!0})}),u(()=>{null==N||N(),N=void 0}),t({popperContentRef:T,popperInstanceRef:P,updatePopper:V,contentStyle:q}),(e,t)=>(f(),p("div",k({ref_key:"contentRef",ref:T},v(W),{style:v(q),class:v($),tabindex:"-1",onMouseenter:t=>e.$emit("mouseenter",t),onMouseleave:t=>e.$emit("mouseleave",t)}),[R(v(ue),{trapped:v(g),"trap-on-focus-in":!0,"focus-trap-el":v(T),"focus-start-el":v(d),onFocusAfterTrapped:v(b),onFocusAfterReleased:v(h),onFocusin:v(y),onFocusoutPrevented:v(w),onReleaseRequested:v(x)},{default:j(()=>[a(e.$slots,"default")]),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusin","onFocusoutPrevented","onReleaseRequested"])],16,["onMouseenter","onMouseleave"]))}}),[["__file","content.vue"]]);const en=_(he),tn=Symbol("elTooltip"),nn=e({...re,...Vt,appendTo:{type:ne.to.type},content:{type:String,default:""},rawContent:Boolean,persistent:Boolean,visible:{type:g(Boolean),default:null},transition:String,teleported:{type:Boolean,default:!0},disabled:Boolean,...te(["ariaLabel"])}),on=e({...we,disabled:Boolean,trigger:{type:g([String,Array]),default:"hover"},triggerKeys:{type:g(Array),default:()=>[H.enter,H.numpadEnter,H.space]}}),rn=W({type:g(Boolean),default:null}),an=W({type:g(Function)}),{useModelToggleProps:sn,useModelToggleEmits:ln,useModelToggle:un}=(e=>{const t=`update:${e}`,n=`onUpdate:${e}`,o=[t];return{useModelToggle:({indicator:o,toggleReason:a,shouldHideWhenRouteChanges:i,shouldProceed:s,onShow:l,onHide:u})=>{const p=$(),{emit:f}=p,c=p.props,d=r(()=>q(c[n])),v=r(()=>null===c[e]),g=e=>{!0!==o.value&&(o.value=!0,a&&(a.value=e),q(l)&&l(e))},m=e=>{!1!==o.value&&(o.value=!1,a&&(a.value=e),q(u)&&u(e))},h=e=>{if(!0===c.disabled||q(s)&&!s())return;const n=d.value&&P;n&&f(t,!0),!v.value&&n||g(e)},b=e=>{if(!0===c.disabled||!P)return;const n=d.value&&P;n&&f(t,!1),!v.value&&n||m(e)},y=e=>{z(e)&&(c.disabled&&e?d.value&&f(t,!1):o.value!==e&&(e?g():m()))};return E(()=>c[e],y),i&&void 0!==p.appContext.config.globalProperties.$route&&E(()=>({...p.proxy.$route}),()=>{i.value&&o.value&&b()}),A(()=>{y(c[e])}),{hide:b,show:h,toggle:()=>{o.value?b():h()},hasUpdateHandler:d}},useModelToggleProps:{[e]:rn,[n]:an},useModelToggleEmits:o}})("visible"),pn=e({...ge,...sn,...nn,...on,...Ut,showArrow:{type:Boolean,default:!0}}),fn=[...ln,"before-show","before-hide","show","hide","open","close"],cn=(e,t,n)=>o=>{((e,t)=>U(e)?e.includes(t):e===t)(v(e),t)&&n(o)},dn=(e,t,{checkForDefaultPrevented:n=!0}={})=>o=>{const r=null==e?void 0:e(o);if(!1===n||!r)return null==t?void 0:t(o)},vn=e=>t=>"mouse"===t.pointerType?e(t):void 0,gn=n({name:"ElTooltipTrigger"});var mn=t(n({...gn,props:on,setup(e,{expose:t}){const n=e,r=s("tooltip"),{controlled:i,id:u,open:p,onOpen:c,onClose:g,onToggle:m}=l(tn,void 0),h=o(null),b=()=>{if(v(i)||n.disabled)return!0},y=N(n,"trigger"),w=dn(b,cn(y,"hover",c)),x=dn(b,cn(y,"hover",g)),O=dn(b,cn(y,"click",e=>{0===e.button&&m(e)})),R=dn(b,cn(y,"focus",c)),A=dn(b,cn(y,"focus",g)),E=dn(b,cn(y,"contextmenu",e=>{e.preventDefault(),m(e)})),C=dn(b,e=>{const{code:t}=e;n.triggerKeys.includes(t)&&(e.preventDefault(),m(e))});return t({triggerRef:h}),(e,t)=>(f(),S(v(Ce),{id:v(u),"virtual-ref":e.virtualRef,open:v(p),"virtual-triggering":e.virtualTriggering,class:d(v(r).e("trigger")),onBlur:v(A),onClick:v(O),onContextmenu:v(E),onFocus:v(R),onMouseenter:v(w),onMouseleave:v(x),onKeydown:v(C)},{default:j(()=>[a(e.$slots,"default")]),_:3},8,["id","virtual-ref","open","virtual-triggering","class","onBlur","onClick","onContextmenu","onFocus","onMouseenter","onMouseleave","onKeydown"]))}}),[["__file","trigger.vue"]]);const hn=()=>{const e=K(),t=se(),n=r(()=>`${e.value}-popper-container-${t.prefix}`),o=r(()=>`#${n.value}`);return{id:n,selector:o}},bn=()=>{const{id:e,selector:t}=hn();return V(()=>{P&&(document.body.querySelector(t.value)||(e=>{const t=document.createElement("div");t.id=e,document.body.appendChild(t)})(e.value))}),{id:e,selector:t}},yn=n({name:"ElTooltipContent",inheritAttrs:!1});var wn=t(n({...yn,props:nn,setup(e,{expose:t}){const n=e,{selector:i}=hn(),p=s("tooltip"),c=o(),d=Z(()=>{var e;return null==(e=c.value)?void 0:e.popperContentRef});let g;const{controlled:m,id:b,open:y,trigger:w,onClose:x,onOpen:O,onShow:A,onHide:C,onBeforeShow:B,onBeforeHide:M}=l(tn,void 0),P=r(()=>n.transition||`${p.namespace.value}-fade-in-linear`),F=r(()=>n.persistent);u(()=>{null==g||g()});const L=r(()=>!!v(F)||v(y)),D=r(()=>!n.disabled&&v(y)),I=r(()=>n.appendTo||i.value),_=r(()=>{var e;return null!=(e=n.style)?e:{}}),H=o(!0),W=()=>{C(),J()&&pe(document.body),H.value=!0},$=()=>{if(v(m))return!0},q=dn($,()=>{n.enterable&&"hover"===v(w)&&O()}),z=dn($,()=>{"hover"===v(w)&&x()}),U=()=>{var e,t;null==(t=null==(e=c.value)?void 0:e.updatePopper)||t.call(e),null==B||B()},N=()=>{null==M||M()},V=()=>{A()},K=()=>{n.virtualTriggering||x()},J=e=>{var t;const n=null==(t=c.value)?void 0:t.popperContentRef,o=(null==e?void 0:e.relatedTarget)||document.activeElement;return null==n?void 0:n.contains(o)};return E(()=>v(y),e=>{e?(H.value=!1,g=G(d,()=>{if(v(m))return;"hover"!==v(w)&&x()})):null==g||g()},{flush:"post"}),E(()=>n.content,()=>{var e,t;null==(t=null==(e=c.value)?void 0:e.updatePopper)||t.call(e)}),t({contentRef:c,isFocusInsideContent:J}),(e,t)=>(f(),S(v(oe),{disabled:!e.teleported,to:v(I)},{default:j(()=>[R(X,{name:v(P),onAfterLeave:W,onBeforeEnter:U,onAfterEnter:V,onBeforeLeave:N},{default:j(()=>[v(L)?h((f(),S(v(Qt),k({key:0,id:v(b),ref_key:"contentRef",ref:c},e.$attrs,{"aria-label":e.ariaLabel,"aria-hidden":H.value,"boundaries-padding":e.boundariesPadding,"fallback-placements":e.fallbackPlacements,"gpu-acceleration":e.gpuAcceleration,offset:e.offset,placement:e.placement,"popper-options":e.popperOptions,"arrow-offset":e.arrowOffset,strategy:e.strategy,effect:e.effect,enterable:e.enterable,pure:e.pure,"popper-class":e.popperClass,"popper-style":[e.popperStyle,v(_)],"reference-el":e.referenceEl,"trigger-target-el":e.triggerTargetEl,visible:v(D),"z-index":e.zIndex,onMouseenter:v(q),onMouseleave:v(z),onBlur:K,onClose:v(x)}),{default:j(()=>[a(e.$slots,"default")]),_:3},16,["id","aria-label","aria-hidden","boundaries-padding","fallback-placements","gpu-acceleration","offset","placement","popper-options","arrow-offset","strategy","effect","enterable","pure","popper-class","popper-style","reference-el","trigger-target-el","visible","z-index","onMouseenter","onMouseleave","onClose"])),[[Y,v(D)]]):T("v-if",!0)]),_:3},8,["name"])]),_:3},8,["disabled","to"]))}}),[["__file","content.vue"]]);const xn=n({name:"ElTooltip"});const On=_(t(n({...xn,props:pn,emits:fn,setup(e,{expose:t,emit:n}){const l=e;bn();const u=s("tooltip"),c=le(),d=o(),g=o(),m=()=>{var e;const t=v(d);t&&(null==(e=t.popperInstanceRef)||e.update())},h=o(!1),b=o(),{show:y,hide:w,hasUpdateHandler:x}=un({indicator:h,toggleReason:b}),{onOpen:O,onClose:A}=ae({showAfter:N(l,"showAfter"),hideAfter:N(l,"hideAfter"),autoClose:N(l,"autoClose"),open:y,close:w}),C=r(()=>z(l.visible)&&!x.value),k=r(()=>[u.b(),l.popperClass]);i(tn,{controlled:C,id:c,open:J(h),trigger:N(l,"trigger"),onOpen:e=>{O(e)},onClose:e=>{A(e)},onToggle:e=>{v(h)?A(e):O(e)},onShow:()=>{n("show",b.value)},onHide:()=>{n("hide",b.value)},onBeforeShow:()=>{n("before-show",b.value)},onBeforeHide:()=>{n("before-hide",b.value)},updatePopper:m}),E(()=>l.disabled,e=>{e&&h.value&&(h.value=!1)});return Q(()=>h.value&&w()),t({popperRef:d,contentRef:g,isFocusInsideContent:e=>{var t;return null==(t=g.value)?void 0:t.isFocusInsideContent(e)},updatePopper:m,onOpen:O,onClose:A,hide:w}),(e,t)=>(f(),S(v(en),{ref_key:"popperRef",ref:d,role:e.role},{default:j(()=>[R(mn,{disabled:e.disabled,trigger:e.trigger,"trigger-keys":e.triggerKeys,"virtual-ref":e.virtualRef,"virtual-triggering":e.virtualTriggering},{default:j(()=>[e.$slots.default?a(e.$slots,"default",{key:0}):T("v-if",!0)]),_:3},8,["disabled","trigger","trigger-keys","virtual-ref","virtual-triggering"]),R(wn,{ref_key:"contentRef",ref:g,"aria-label":e.ariaLabel,"boundaries-padding":e.boundariesPadding,content:e.content,disabled:e.disabled,effect:e.effect,enterable:e.enterable,"fallback-placements":e.fallbackPlacements,"hide-after":e.hideAfter,"gpu-acceleration":e.gpuAcceleration,offset:e.offset,persistent:e.persistent,"popper-class":v(k),"popper-style":e.popperStyle,placement:e.placement,"popper-options":e.popperOptions,"arrow-offset":e.arrowOffset,pure:e.pure,"raw-content":e.rawContent,"reference-el":e.referenceEl,"trigger-target-el":e.triggerTargetEl,"show-after":e.showAfter,strategy:e.strategy,teleported:e.teleported,transition:e.transition,"virtual-triggering":e.virtualTriggering,"z-index":e.zIndex,"append-to":e.appendTo},{default:j(()=>[a(e.$slots,"content",{},()=>[e.rawContent?(f(),p("span",{key:0,innerHTML:e.content},null,8,["innerHTML"])):(f(),p("span",{key:1},ee(e.content),1))]),e.showArrow?(f(),S(v(ye),{key:0})):T("v-if",!0)]),_:3},8,["aria-label","boundaries-padding","content","disabled","effect","enterable","fallback-placements","hide-after","gpu-acceleration","offset","persistent","popper-class","popper-style","placement","popper-options","arrow-offset","pure","raw-content","reference-el","trigger-target-el","show-after","strategy","teleported","transition","virtual-triggering","z-index","append-to"])]),_:3},8,["role"]))}}),[["__file","tooltip.vue"]]));export{On as E,Oe as O,tn as T,_e as a,on as b,dn as c,ve as r,nn as u,vn as w};
