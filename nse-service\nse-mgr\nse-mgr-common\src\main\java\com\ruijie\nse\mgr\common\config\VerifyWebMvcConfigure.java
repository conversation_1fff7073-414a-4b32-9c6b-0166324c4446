package com.ruijie.nse.mgr.common.config;

import com.ruijie.nse.mgr.common.interceptors.RequestWrapperFilter;
import com.ruijie.nse.mgr.common.interceptors.VerifySignInterceptor;
import jakarta.annotation.Resource;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 拦截器配置
 * <AUTHOR>
 */
@Configuration
public class VerifyWebMvcConfigure implements WebMvcConfigurer {

    @Resource
    private VerifySignInterceptor verifySignInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册VerifySign拦截器
        registry.addInterceptor(verifySignInterceptor).excludePathPatterns("/static/**").addPathPatterns("/**");
    }

    /**
     * 定义request过滤器，用于流复制传递
     * @return
     */
    @SuppressWarnings("java:S3740")
    @Bean
    public FilterRegistrationBean<RequestWrapperFilter> repeatedlyReadFilter() {
        FilterRegistrationBean<RequestWrapperFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new RequestWrapperFilter());
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE);
        registration.addUrlPatterns("/**");
        return registration;
    }

}
