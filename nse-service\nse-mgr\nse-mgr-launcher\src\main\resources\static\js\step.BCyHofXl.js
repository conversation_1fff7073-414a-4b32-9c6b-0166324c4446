import{t as e,aV as s,_ as a,d as t,b as i,I as l,g as r,f as n,l as o,m as u,i as p,n as c,B as v,y as d,v as f,r as m,A as S,o as y,c as h,V as g,a8 as x,h as $,C as w,k,e as _,w as C,D as E,E as b,aO as B,$ as j,F as I,a1 as V,G as q,q as z}from"./index.Ckm1SagX.js";import{C as D}from"./event.BwRzfsZt.js";import{u as N}from"./index.DeprZc7T.js";const P=e({space:{type:[Number,String],default:""},active:{type:Number,default:0},direction:{type:String,default:"horizontal",values:["horizontal","vertical"]},alignCenter:{type:Boolean},simple:{type:Boolean},finishStatus:{type:String,values:["wait","process","finish","error","success"],default:"finish"},processStatus:{type:String,values:["wait","process","finish","error","success"],default:"process"}}),W={[D]:(e,a)=>[e,a].every(s)},A="ElSteps",F=t({name:"ElSteps"});var G=a(t({...F,props:P,emits:W,setup(e,{emit:s}){const a=e,t=i("steps"),{children:f,addChild:m,removeChild:S,ChildrenSorter:y}=N(v(),"ElStep");return l(f,()=>{f.value.forEach((e,s)=>{e.setIndex(s)})}),d(A,{props:a,steps:f,addStep:m,removeStep:S}),l(()=>a.active,(e,a)=>{s(D,e,a)}),(e,s)=>(n(),r("div",{class:c([p(t).b(),p(t).m(e.simple?"simple":e.direction)])},[o(e.$slots,"default"),u(p(y))],2))}}),[["__file","steps.vue"]]);const O=e({title:{type:String,default:""},icon:{type:f},description:{type:String,default:""},status:{type:String,values:["","wait","process","finish","error","success"],default:""}}),H=t({name:"ElStep"});var J=a(t({...H,props:O,setup(e){const a=e,t=i("step"),d=m(-1),f=m({}),q=m(""),z=S(A),D=v();y(()=>{l([()=>z.props.active,()=>z.props.processStatus,()=>z.props.finishStatus],([e])=>{M(e)},{immediate:!0})});const N=h(()=>a.status||q.value),P=h(()=>{const e=z.steps.value[d.value-1];return e?e.currentStatus:"wait"}),W=h(()=>z.props.alignCenter),F=h(()=>"vertical"===z.props.direction),G=h(()=>z.props.simple),O=h(()=>z.steps.value.length),H=h(()=>{var e;return(null==(e=z.steps.value[O.value-1])?void 0:e.uid)===D.uid}),J=h(()=>G.value?"":z.props.space),K=h(()=>[t.b(),t.is(G.value?"simple":z.props.direction),t.is("flex",H.value&&!J.value&&!W.value),t.is("center",W.value&&!F.value&&!G.value)]),L=h(()=>{const e={flexBasis:s(J.value)?`${J.value}px`:J.value?J.value:100/(O.value-(W.value?0:1))+"%"};return F.value||H.value&&(e.maxWidth=100/O.value+"%"),e}),M=e=>{e>d.value?q.value=z.props.finishStatus:e===d.value&&"error"!==P.value?q.value=z.props.processStatus:q.value="wait";const s=z.steps.value[d.value-1];s&&s.calcProgress(q.value)},Q=g({uid:D.uid,getVnode:()=>D.vnode,currentStatus:N,setIndex:e=>{d.value=e},calcProgress:e=>{const s="wait"===e,a={transitionDelay:`${s?"-":""}${150*d.value}ms`},t=e===z.props.processStatus||s?0:100;a.borderWidth=t&&!G.value?"1px":0,a["vertical"===z.props.direction?"height":"width"]=`${t}%`,f.value=a}});return z.addStep(Q),x(()=>{z.removeStep(Q)}),(e,s)=>(n(),r("div",{style:k(p(L)),class:c(p(K))},[$(" icon & line "),w("div",{class:c([p(t).e("head"),p(t).is(p(N))])},[p(G)?$("v-if",!0):(n(),r("div",{key:0,class:c(p(t).e("line"))},[w("i",{class:c(p(t).e("line-inner")),style:k(f.value)},null,6)],2)),w("div",{class:c([p(t).e("icon"),p(t).is(e.icon||e.$slots.icon?"icon":"text")])},[o(e.$slots,"icon",{},()=>[e.icon?(n(),_(p(b),{key:0,class:c(p(t).e("icon-inner"))},{default:C(()=>[(n(),_(E(e.icon)))]),_:1},8,["class"])):"success"===p(N)?(n(),_(p(b),{key:1,class:c([p(t).e("icon-inner"),p(t).is("status")])},{default:C(()=>[u(p(B))]),_:1},8,["class"])):"error"===p(N)?(n(),_(p(b),{key:2,class:c([p(t).e("icon-inner"),p(t).is("status")])},{default:C(()=>[u(p(j))]),_:1},8,["class"])):p(G)?$("v-if",!0):(n(),r("div",{key:3,class:c(p(t).e("icon-inner"))},I(d.value+1),3))])],2)],2),$(" title & description "),w("div",{class:c(p(t).e("main"))},[w("div",{class:c([p(t).e("title"),p(t).is(p(N))])},[o(e.$slots,"title",{},()=>[V(I(e.title),1)])],2),p(G)?(n(),r("div",{key:0,class:c(p(t).e("arrow"))},null,2)):(n(),r("div",{key:1,class:c([p(t).e("description"),p(t).is(p(N))])},[o(e.$slots,"description",{},()=>[V(I(e.description),1)])],2))],2)],6))}}),[["__file","item.vue"]]);const K=z(G,{Step:J}),L=q(J);export{L as E,K as a};
