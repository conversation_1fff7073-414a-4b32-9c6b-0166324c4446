@charset "UTF-8";:root{--menu-background: #fff;--menu-text: #212121;--menu-active-text: var( --el-menu-active-color );--menu-hover: #e6f4ff;--sidebar-logo-background: #f5f5f5;--sidebar-logo-text-color: #333}html.sidebar-color-blue{--menu-background: #304156;--menu-text: #bfcbd9;--menu-active-text: var(--el-menu-active-color);--menu-hover: #263445;--sidebar-logo-background: #2d3748;--sidebar-logo-text-color: #fff}html.dark{--menu-background: var(--el-bg-color-overlay);--menu-text: #fff;--menu-active-text: var(--el-menu-active-color);--menu-hover: rgb(0 0 0 / 20%);--sidebar-logo-background: rgb(0 0 0 / 20%);--sidebar-logo-text-color: #fff;--w-e-textarea-bg-color: var(--el-bg-color);--w-e-textarea-color: var(--el-text-color-primary);--w-e-textarea-border-color: var(--el-border-color);--w-e-textarea-slight-border-color: var(--el-border-color-lighter);--w-e-textarea-slight-color: var(--el-text-color-secondary);--w-e-textarea-slight-bg-color: var(--el-bg-color-overlay);--w-e-textarea-selected-border-color: var(--el-color-info-light-5);--w-e-textarea-handler-bg-color: var(--el-color-primary);--w-e-toolbar-color: var(--el-text-color-regular);--w-e-toolbar-bg-color: var(--el-bg-color);--w-e-toolbar-active-color: var(--el-text-color-primary);--w-e-toolbar-active-bg-color: var(--el-fill-color-light);--w-e-toolbar-disabled-color: var(--el-text-color-secondary);--w-e-toolbar-border-color: var(--el-border-color-base);--w-e-modal-button-bg-color: var(--el-bg-color-light-3);--w-e-modal-button-border-color: var(--el-border-color-light)}.el-switch{--el-switch-on-color: var(--el-color-primary);--el-switch-off-color: var(--el-border-color)}.el-switch{display:inline-flex;align-items:center;position:relative;font-size:14px;line-height:20px;height:32px;vertical-align:middle}.el-switch.is-disabled .el-switch__core,.el-switch.is-disabled .el-switch__label{cursor:not-allowed}.el-switch__label{transition:var(--el-transition-duration-fast);height:20px;display:inline-block;font-size:14px;font-weight:500;cursor:pointer;vertical-align:middle;color:var(--el-text-color-primary)}.el-switch__label.is-active{color:var(--el-color-primary)}.el-switch__label--left{margin-right:10px}.el-switch__label--right{margin-left:10px}.el-switch__label *{line-height:1;font-size:14px;display:inline-block}.el-switch__label .el-icon{height:inherit}.el-switch__label .el-icon svg{vertical-align:middle}.el-switch__input{position:absolute;width:0;height:0;opacity:0;margin:0}.el-switch__input:focus-visible~.el-switch__core{outline:2px solid var(--el-switch-on-color);outline-offset:1px}.el-switch__core{display:inline-flex;position:relative;align-items:center;min-width:40px;height:20px;border:1px solid var(--el-switch-border-color, var(--el-switch-off-color));outline:none;border-radius:10px;box-sizing:border-box;background:var(--el-switch-off-color);cursor:pointer;transition:border-color var(--el-transition-duration),background-color var(--el-transition-duration)}.el-switch__core .el-switch__inner{width:100%;transition:all var(--el-transition-duration);height:16px;display:flex;justify-content:center;align-items:center;overflow:hidden;padding:0 4px 0 18px}.el-switch__core .el-switch__inner .is-icon,.el-switch__core .el-switch__inner .is-text{font-size:12px;color:var(--el-color-white);-webkit-user-select:none;user-select:none;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.el-switch__core .el-switch__action{position:absolute;left:1px;border-radius:var(--el-border-radius-circle);transition:all var(--el-transition-duration);width:16px;height:16px;background-color:var(--el-color-white);display:flex;justify-content:center;align-items:center;color:var(--el-switch-off-color)}.el-switch.is-checked .el-switch__core{border-color:var(--el-switch-border-color, var(--el-switch-on-color));background-color:var(--el-switch-on-color)}.el-switch.is-checked .el-switch__core .el-switch__action{left:calc(100% - 17px);color:var(--el-switch-on-color)}.el-switch.is-checked .el-switch__core .el-switch__inner{padding:0 18px 0 4px}.el-switch.is-disabled{opacity:.6}.el-switch--wide .el-switch__label.el-switch__label--left span{left:10px}.el-switch--wide .el-switch__label.el-switch__label--right span{right:10px}.el-switch .label-fade-enter-from,.el-switch .label-fade-leave-active{opacity:0}.el-switch--large{font-size:14px;line-height:24px;height:40px}.el-switch--large .el-switch__label{height:24px;font-size:14px}.el-switch--large .el-switch__label *{font-size:14px}.el-switch--large .el-switch__core{min-width:50px;height:24px;border-radius:12px}.el-switch--large .el-switch__core .el-switch__inner{height:20px;padding:0 6px 0 22px}.el-switch--large .el-switch__core .el-switch__action{width:20px;height:20px}.el-switch--large.is-checked .el-switch__core .el-switch__action{left:calc(100% - 21px)}.el-switch--large.is-checked .el-switch__core .el-switch__inner{padding:0 22px 0 6px}.el-switch--small{font-size:12px;line-height:16px;height:24px}.el-switch--small .el-switch__label{height:16px;font-size:12px}.el-switch--small .el-switch__label *{font-size:12px}.el-switch--small .el-switch__core{min-width:30px;height:16px;border-radius:8px}.el-switch--small .el-switch__core .el-switch__inner{height:12px;padding:0 2px 0 14px}.el-switch--small .el-switch__core .el-switch__action{width:12px;height:12px}.el-switch--small.is-checked .el-switch__core .el-switch__action{left:calc(100% - 13px)}.el-switch--small.is-checked .el-switch__core .el-switch__inner{padding:0 14px 0 2px}
