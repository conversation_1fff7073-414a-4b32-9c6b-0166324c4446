package com.ruijie.nse.mgr.common.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.net.URI;
import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class WebSocketHandler extends TextWebSocketHandler {

    private static final ConcurrentHashMap<String, WebSocketSession> userSessionMap = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        URI uri = session.getUri();
        if (Objects.nonNull(uri)) {
            String path = uri.getPath();
            String userId = path.substring(path.lastIndexOf('/') + 1);
            userSessionMap.put(userId, session);
            log.info("用户连接：userId:" + userId);
        }
    }

    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) {
        log.info("收到用户发来的消息：{}", message.getPayload());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String path = session.getUri().getPath();
        String userId = path.substring(path.lastIndexOf('/') + 1);
        log.info("用户断开了 {} 连接", userId);
        userSessionMap.remove(userId);
    }


    /**
     * 获取连接数
     * @return
     */
    public static int getLinkNum() {
        return userSessionMap.size();
    }

    /**
     * 发送消息给指定用户
     * @param userId
     * @param message
     * @throws Exception
     */
    public static boolean sendMsgToUser(String userId, String message) {
        WebSocketSession webSocketSession = userSessionMap.get(userId);
        if (webSocketSession != null && webSocketSession.isOpen()) {
            try {
                webSocketSession.sendMessage(new TextMessage(message));
                return true;
            } catch (Exception e) {
                log.error("发送消息给用户失败：{}", userId, e);
            }
        }
        return false;
    }


    /**
     * 发送给所有用户
     * @param message
     * @throws Exception
     */
    public static void sendMsg(String message) {
        Collection<WebSocketSession> sessions = userSessionMap.values();
        for (WebSocketSession session : sessions) {
            if (!session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(message));
                } catch (Exception e) {
                    log.error("发送消息给用户失败：{}", session.getId(), e);
                }
            }
        }
    }

}
