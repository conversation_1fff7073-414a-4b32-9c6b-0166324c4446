var e=Object.defineProperty,t=(t,a,s)=>((t,a,s)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s)(t,"symbol"!=typeof a?a+"":a,s);import{d as a,r as s,o as n,a8 as l,db as i,g as u,f as o,C as r,X as c,h as d,k as p,a1 as v,F as g,Q as A,m,w as f,aS as h,aP as C}from"./index.Ckm1SagX.js";/* empty css                *//* empty css               */import{E as S}from"./progress.ChnKapv7.js";import{_ as y}from"./play.Drozyqoq.js";import{E as k}from"./index.DCgMX_7R.js";import{v as b}from"./directive.C7vihscI.js";import{E as W}from"./index.CbYeWxT8.js";import{E as x}from"./index.KtapGdwl.js";import{_ as q}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.BRUQ9gWw.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./index.4JfkAhur.js";import"./index.BLy3nyPI.js";import"./_arrayPush.Dbwejsrt.js";import"./event.BwRzfsZt.js";import"./index.DJHzyRe5.js";import"./index.Byj-i824.js";import"./aria.C1IWO_Rd.js";import"./index.BjYFza3j.js";import"./vnode.BkZiIFpS.js";import"./scroll.XdyICIdv.js";import"./focus-trap.Bd_uzvDY.js";class K{constructor(e){t(this,"ws",null),t(this,"url"),t(this,"onMessageCallback",null),this.url=e}connect(){this.ws=new WebSocket(this.url),this.ws.onopen=()=>{},this.ws.onmessage=e=>{if(this.onMessageCallback)try{const t=JSON.parse(e.data);this.onMessageCallback(t)}catch{this.onMessageCallback(e.data)}},this.ws.onclose=()=>{setTimeout(()=>this.connect(),3e3)},this.ws.onerror=()=>{var e;null==(e=this.ws)||e.close()}}send(e){if(!this.ws||this.ws.readyState!==WebSocket.OPEN)return!1;try{const t="string"==typeof e?e:JSON.stringify(e);return this.ws.send(t),!0}catch(t){return!1}}onMessage(e){this.onMessageCallback=e}close(){var e;null==(e=this.ws)||e.close()}}const w={class:"login-queue"},I={class:"login-queue-dialog"},Q={class:"login-queue-container"},N={key:0,class:"avatar-container",src:"data:image/png;base64,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",alt:"用户头像"},P={key:1,class:"avatar-container",src:"data:image/png;base64,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",alt:"用户头像"},D={key:2,class:"avatar-container",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAKr0lEQVR4nO2dXWxcxRWAv3PXprY3uLazhthtAg0xTQjG/NZpqqYUEBjaSIBK8xIqVEEakfASAS9RBTwgISHxkhCF0Ado8sCfmkopTUJpIAlCSSkowU0Cwgn5gUDije1aXse12Xv6cK/jtb327t6dO7ux95NW1r135pyZOZ77c2bmjFDkdCa0wYnQjMsCVeYBc4BGEWKq1AhUKZQDCAwp9IvQo0ocOA2cFKEDhyNukvb6qHxTyPpkQgpdgLHEe3U+EW4T4efAIlWuNClfhOPAPlX2kmRXrFo+Myk/X4rCIPF+bQXuQ1kKXGNZ/WGEbcDWWJXst6x7HAUzyOlejZU7POQIyxVaClWOVAQOusqWIZdXGqslXqAy2KWrT5uTwiqBFYXQnyWqsCmivFg3Q9ptKrbWIF192uw6PIHyoC2dRhA2Oy7P2zJM6AY53auxSyI8BawOW1fIrB9M8kzYt7JQDRJP6KPAs0BNmHos0gOsjUVlQ1gKQjFIvFfnS4QXFO4OQ36hEdiuSdaE8cps3CCdCX3EgfUKl5iWXUwIDLqwuj4qLxuWa47OhG4U+INJmcWOwkv1UVlpSp4Rg3Sd1znJJJtFWGJC3sWGKnsiER6sq5ST+crK2yDxfm1FeR24Il9ZFzknEJbl+7Wfl0HODeid6vIXlGg+cqYMQkIc7p9ZIe8EFxGQroT+yoVt+ciYoqgDS+ui8naQzIEa89yA3qlJdgTNPw1QidAWpKfk3KC+Z/afpdtUBoQEcHuuz5ScDNJ1Xue4LnsoPcCz5YTjsCSXty8nF+nJJJspGSMXrvDbLGuyNkhnQjdO1++MfBBhSWdCN2adPptEnQl9RGBT8GKVUFiRjZslo0F8R+HBqe6bChuBQU3SkskhmfGW5XttS8bIE4VLJMILmdJNapB4Qh+dqi70QqBwtz9GNCET3rL8kb4vmDqDS8VCz2CSpolGHssmyuUPuxaVMc5/Bx9/DUe7oXsAuvuhawB6znvXayqhrgJqq6C2Aq6qhZt+AJUT1rIg1Pht+1i6i2l7SFefNrvCp6EWK0u+7YNPvoED33h/g3BjA1zf4P2dNcNs+YLiKNelmziR1iDxfv1zoWeHnOqFnR2w4wuzctua4K55MLvarNycETbHquR340+PodC9ozMBO4/Cjg4YGApHR0U5tM2Du66C+gJ65NL1knEGKeQw7PvHYctB6Bmwo6+mApa3wK1GZw9nT7rh31EG8d+szo49b4O/HoEtBeqXy6+DexcURLUOJrks9Y1r1HdIucNDFMAYz+0tnDHA0/3c3oKoFr/NR06kHpxL6AHbE59/87pNbZl5a5ldfQIHZ0bl+uHjCz0k3q+tto3xZOCR5/CwXSaFFn/QDxh9y7rPZkE2fgTHum1qzI5j3V7ZLHOh7UcM4i2WscLODnj3mC1tufPuMa+M1khpewf8ZWSWVi51JuDNQzY05cebh7yyWuIa3wZ+D4lwmy3NO4/a+87Ih54Br6zW8G3gAPgLLEPnVK/3BX6xsKPDK7MNhm0w/AxZZEPpzhDdIWEwMGT1WbIIQDoT2iDeeu5QOZOAVX/LPd/s70P1mPHKQ53Z5V1YP/q4dxBO/Tf3Mrz4a7jcgs9LobHMidCsyfCVtZ8Jlu+nP4TfXjty/J0LT70Hn2dYWPbjGPzxVihLebF/4z/BDNJ+Bi6fm3u+XHEiNDu4WPHifPptsHxvHIJdX44clzmw+idQVzlxnrpKL02qMXZ96ckKQtCy54zLAscPVxEq//sOPjwVPP+Gf0H72ZHjhkvhsdaJ0z/W6qUZpv2sJyMoH57y6hA2qsxz8GKHhMrHBqKLPPMenOkbOW6+HFalMcqqVu/aMGf6vLz5YqIOWTDHARrD1vLFOTNynn4fku7I8S+vhGUpz5dl13rnhkm6Xh4TmKpDBhodEWJha+k29CHYmYCnd48+98BCuGOu93tg4ehrT+8297Vtqg6TIULMUQ1/Zkl3vzlZR87CujET/Ffe4v1SWbffS2sKk3WYCFVqHIGqsBV1Gf7v2n0cXpsk0MVr7V4ak5iuQzoEqpzh4F9hMjxvyiRvHU7vMX73mHfNNGHUYSwK5TmtDykRPo5A6N6lmkk+4oJy73zvQT6WO+Z610wTRh3GIjDkKIT+uKqrMCtv0Wxv+s5ELG/x0pjEdB3SodDviNATtqJag68NV9bC44tHn3vp394vlccXe2lNYbIOEyFCj+NH7wyVWkP/XZVl8OQYY2z7HP5x1Ptt+3z0tScXm5tobaoOk6FK3MGC671pphk5a34Gl6VMlv7oa3j1wMjxqwe8c8NcNsPLYwJTdcjAaQfIO2BKJm5qyF/G72+AG2aNHH/VO/4DEbxzX6WM8t0wy8ubLybqkAUnHRFCHxP7XhkszuMh29YE91w9cpx0Yf1+6E/zftg/5F1L9Xndc7UnIyiLZ3t1CBsROhwcjoSvCq6blTlNOlpmwcM3jj63bj90dE2cp6NrfO95+EZPVhCClj1nHI6UuUnabUzmTXWJ50K03Bvpu4DAB1ncZD84CY2Xjj4XDeiTCFr2XLlgi3P9+qXpkN7p+NMn5hfghE1b0/geGgYiHJ9ZJT8adp3sC1+lt3KpInTPmTkqyr0yW2If+NOAVLEyGX92tbdy6WKhzeLSt2EbeD0kyS47ar1lZDUWPrLypabCK6s1fBs4AH64hxCc1uOpj44f2StGHlhodf3h4eGQGyPud2/LBivcNS+9p7ZYuGOu1WfHqLZPHQ/ZarEIrLwF5hp0/plibu344WALXGj70pK2MRTNkjYAV9litzheA9wc+kSkzNzcaN8YML7NS8uiKeJl0Y3VEtcCRY67dwGsbrX7SlxT4ekskDFQ2DQ2KlAptEaxh9aAUvAZK2QbfAYK30tSKYVn8okndB1Ftm/UFAlgBrA+FpXsA5hBKcRfiEwa4m/CmYt+hrWhFWv6snaynd4yfm+cS+jfS5FJzSCwfWZU7pksTca5vZpkjcCguWJNT/xAymsypctokFi1fOYW2cP9YsSF1dlss5fV7Pf6qLys8FL+xZqe+KH8stpeLyefVWef7i7tkJAbquypnyG/yDZ9TutDIhEeBE7kXKrpywm/zbImJ4PUVcpJhGX+dj4lJkNIICzLdW/DnFdQxapkvzjcD2iueacRKg73B9nTMNCStpkV8o4DSykZJR3qwNKgexkGXmNYF5W3JUJb6faVgpCQCG1B9zCEPAwCXk8Bbqf0oAevDW7PZ5dPyNMg4D1THIclquzJV9bFiip7HIcl+e6DC6Xtu/PG9PbdRtep10dlpcKK6eD7Ehj0d14zZgwwbBDw3SxJWgS2m5ZdLAhs1yQt2bpDcpQdHv4GWM8ydQa5eoC1sahsCEtBqKE1YlHZMJikCVgfph5LrB9M0hSmMcDihLiuPm12HZ4o9GyWnBE2Oy7Pp5uQEI46y3T1aXNSWCWwohD6s0QVNkWUF20ZYpiCNcjpXo2VOzzkCMttT/CeCIGDrrJlyOWVyca9Qy5D4fH3z7jP3yXAyqYAKRz212dsNfFhly9FYZBU4r06nwi3+bHQF5leHSzCcWCfKntJsiubYVWbFJ1BxtKZ0AYnQjMuC/wYw3OARhFiqtQIVA1HxRMYUugXoccPqnMaOClCBw5H3CTt9VGxE/A1IP8HnkuQ1aPNCN0AAAAASUVORK5CYII=",alt:"用户头像"},X={class:"info-text"},E={class:"queue-info"},T={class:"position"},U={class:"estimated-time"},B={class:"time"},O={class:"actual-time"},V={class:"expected-login-time"},M={class:"expected-login-content"},j={class:"buttons"},F={class:"estimated-time"},J={class:"time"},H={class:"buttons",style:{"margin-bottom":"40px"}},Y={class:"buttons"},G=q(a({__name:"LoginQueue",props:{userId:{}},emits:["close","login"],setup(e,{emit:t}){const a=e,q=t,G=s(!1),Z=s("queuing");s(!0);const L=s({id:"",name:"",account:"",doLoginTime:0,waitingCount:0,totalWaitingCount:0,expectedLoginTime:0,queueSuccessful:!1}),z=s(),R=s(0),_=s(0),$=s(0),ee=s(null);let te=null;const ae=()=>{if(L.value.queueSuccessful||!L.value.doLoginTime)L.value.queueSuccessful&&($.value=Math.max(L.value.enterQueueTime+3e5-Date.now(),0),0===$.value&&(Z.value="timeout",i.cancelQueuing(a.userId)));else if(z.value=Date.now()-L.value.doLoginTime,L.value.expectedLoginTime){R.value=Math.max(L.value.expectedLoginTime-Date.now(),0);const e=L.value.expectedLoginTime-L.value.doLoginTime,t=100*(1-R.value/e);_.value=parseFloat(t.toFixed(2)),0===R.value&&re()}};function se(e){"REFRESH_QUEUE"===e.event&&re()}function ne(e){return function(e){const t=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),s=String(e.getDate()).padStart(2,"0"),n=String(e.getHours()).padStart(2,"0"),l=String(e.getMinutes()).padStart(2,"0"),i=String(e.getSeconds()).padStart(2,"0");return`${t}-${a}-${s} ${n}:${l}:${i}`}(new Date(e))}function le(e){const t=Math.floor(e/1e3),a=Math.floor(t/60),s=Math.floor(a/60),n=String(t%60).padStart(2,"0"),l=String(a%60).padStart(2,"0");return`${String(s).padStart(2,"0")}:${l}:${n}`}function ie(e){const t=Math.floor(e/1e3),a=t%60;return`${Math.floor(t/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`}function ue(){const e="success"===Z.value?"登录":"排队";x.confirm(`确定要取消${e}吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{i.cancelQueuing(a.userId).then(t=>{oe(),C.success(`已取消${e}`)})})}function oe(){te&&clearInterval(te),q("close")}function re(){const e=k.service({lock:!0,text:"Loading",background:"rgba(0, 0, 0, 0.7)"});i.getQueueInfo(a.userId).then(e=>{L.value=e,ae(),te||(te=setInterval(ae,1e3)),L.value.queueSuccessful&&(Z.value="success")}).finally(()=>{e.close()})}function ce(){C.success("正在跳转到系统页面..."),q("login")}return n(()=>{if(a.userId){const e=`undefined${a.userId}`;ee.value=new K(e),ee.value.onMessage(se),ee.value.connect(),re()}window.addEventListener("beforeunload",function(e){const t=performance.navigation.type===performance.navigation.TYPE_RELOAD;ee.value&&(t?ee.value.send("refresh"):ee.value.send("close"),ee.value.close()),t||(e.preventDefault(),e.returnValue="您确定要离开吗？您的更改可能尚未保存。")})}),l(()=>{te&&clearInterval(te),ee.value&&ee.value.close()}),(e,t)=>{const a=S,s=W,n=b;return o(),u("div",w,[r("div",I,[c((o(),u("div",Q,[r("div",{class:"user-info",style:p({"margin-top":"queuing"===Z.value?"":"20px"})},["queuing"===Z.value?(o(),u("img",N)):"success"===Z.value?(o(),u("img",P)):"timeout"===Z.value?(o(),u("img",D)):d("",!0),r("div",X,[r("div",null,[t[0]||(t[0]=v(" 姓名: ")),r("span",null,g(L.value.name),1)]),r("div",null,[t[1]||(t[1]=v(" 学号: ")),r("span",null,g(L.value.account),1)])])],4),"queuing"===Z.value?(o(),u(A,{key:0},[t[12]||(t[12]=r("div",{class:"queue-status"},[r("div",{class:"queue-status-text"},"系统登录排队中"),r("p",{style:{color:"#666"}},"当前系统访问人数比较多，请耐心等待！")],-1)),r("div",E,[r("div",T,[t[2]||(t[2]=r("span",null,"您的排队位置",-1)),r("strong",null,"第 "+g(L.value.waitingCount)+" 位",1)]),m(a,{"text-inside":!0,"stroke-width":16,percentage:_.value},null,8,["percentage"]),r("p",null,"前方还有 "+g(L.value.waitingCount-1)+" 位用户等待登录",1)]),r("div",U,[t[3]||(t[3]=r("span",null,"预计等待时间",-1)),r("div",B,g(le(R.value)),1),t[4]||(t[4]=r("small",null,"若有用户退出，排队可能提前，请及时查看排队状态",-1))]),r("div",O,[r("div",null,[t[5]||(t[5]=r("span",null,"已等待时间",-1)),r("strong",null,g(le(z.value)),1)]),r("div",null,[t[6]||(t[6]=r("span",null,"总排队人数",-1)),r("strong",null,g(L.value.totalWaitingCount),1)])]),r("div",V,[t[8]||(t[8]=r("div",null,"预计登录时间",-1)),r("div",M,[t[7]||(t[7]=r("div",{class:"blue-dot"},null,-1)),r("div",null,g(ne(L.value.expectedLoginTime)),1)]),t[9]||(t[9]=r("small",null,"排队成功后需在5分钟内登录，否则需重新排队！每名用户登录后有2小时使用时长限制！",-1))]),r("div",j,[m(s,{icon:"Close",onClick:ue},{default:f(()=>t[10]||(t[10]=[v("取消排队")])),_:1,__:[10]}),m(s,{icon:"Refresh",type:"primary",onClick:re},{default:f(()=>t[11]||(t[11]=[v("刷新排队状态")])),_:1,__:[11]})])],64)):"success"===Z.value?(o(),u(A,{key:1},[t[16]||(t[16]=r("div",{class:"queue-status"},[r("div",{class:"queue-status-text"},"排队成功"),r("p",{style:{color:"#666"}},"您排队成功，请尽快进入系统！")],-1)),r("div",F,[t[13]||(t[13]=r("span",null,"剩余时间",-1)),r("div",J,g(ie($.value)),1)]),t[17]||(t[17]=r("div",{style:{color:"#44a0ff","margin-bottom":"30px"}}," 排队成功后5分钟内未进入将自动退出重新排队！ ",-1)),r("div",H,[m(s,{icon:"Close",onClick:ue},{default:f(()=>t[14]||(t[14]=[v("取消登录")])),_:1,__:[14]}),m(s,{type:"primary",onClick:ce},{default:f(()=>t[15]||(t[15]=[r("img",{style:{width:"14px",height:"14px","margin-right":"6px"},src:y,alt:""},null,-1),v(" 立即进入系统 ")])),_:1,__:[15]})])],64)):"timeout"===Z.value?(o(),u(A,{key:2},[t[20]||(t[20]=h('<div class="queue-status" data-v-d7f6409b><div class="queue-status-text" data-v-d7f6409b>登录超时</div><p style="color:#666;" data-v-d7f6409b>登录超时，请重新排队登录！</p></div><div class="estimated-time" data-v-d7f6409b><span data-v-d7f6409b>剩余时间</span><div class="time" data-v-d7f6409b>00:00</div></div><div style="color:#44a0ff;margin-bottom:30px;" data-v-d7f6409b> 排队成功后5分钟内未进入将自动退出重新排队！ </div>',3)),r("div",Y,[m(s,{onClick:oe},{default:f(()=>t[18]||(t[18]=[v("取消")])),_:1,__:[18]}),m(s,{type:"primary",onClick:oe},{default:f(()=>t[19]||(t[19]=[v("确定")])),_:1,__:[19]})])],64)):d("",!0)])),[[n,G.value,void 0,{fullscreen:!0,lock:!0}]])])])}}}),[["__scopeId","data-v-d7f6409b"]]);export{G as default};
