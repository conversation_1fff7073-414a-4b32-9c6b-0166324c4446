import{d as e}from"./index.D2P-30Pk.js";import{aw as t,V as p}from"./index.Ckm1SagX.js";const i="/api/mgr/experiment",r={getAssignedExperimentsApi:e=>t({url:"/api/mgr/lesson/assigned/page",method:"post",data:e}),getMyExperimentsApi:e=>t({url:`${i}/my/page`,method:"post",data:e}),createExperimentApi:e=>t({url:`${i}/create`,method:"post",data:e}),previpreviewExperimentApi:e=>t({url:`${i}/preview`,method:"post",data:e}),deleteExperiment:e=>t({url:`${i}/delete`,method:"delete",data:e}),renameExperimentApi:e=>t({url:`${i}/rename/${e.id}`,method:"put",data:e}),getProjectUrlApi:e=>t({url:`${i}/project/url/sign/${e}`,method:"get"}),duplicateExperimentApi:e=>t({url:`${i}/duplicate/${e}`,method:"get"}),exportExperimentApi:e=>t({url:`${i}/project/export/${e}`,method:"get",responseType:"blob"}),submitExperiment:(e,p)=>t({url:`${i}/submit/${e}`,method:"post",data:p,headers:{"Content-Type":"multipart/form-data"}}),previewExpResult:e=>t({url:`${i}/preview/${e}`,method:"get",responseType:"blob"}),getExperimentDetail:e=>t({url:`${i}/detail/${e}`,method:"get"}),previewFile:(e,p="manual")=>t({url:"/api/file/preview",method:"get",params:{filePath:e,type:p},responseType:"blob"})};function a(){const t=p({visible:!1,title:"",blob:new Blob});return{previewDialog:t,previewExpResult:p=>{r.previewExpResult(p.id).then(async i=>{var r,a;t.title=p.expResultFilename,(null==(r=p.expResultFilename)?void 0:r.endsWith(".pdf"))?window.open(URL.createObjectURL(new Blob([i.data],{type:"application/pdf"})),"_blank"):(null==(a=p.expResultFilename)?void 0:a.endsWith(".docx"))?(t.blob=new Blob([i.data],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),t.visible=!0):e(new Blob([i.data]),p.expResultFilename)})},stopPreview:()=>{t.visible=!1}}}export{r as E,a as u};
