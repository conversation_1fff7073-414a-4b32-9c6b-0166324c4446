17:17:58.065 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
17:17:58.193 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 45200 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
17:17:58.195 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
17:18:00.920 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:18:02.190 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
17:18:02.193 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
17:18:02.194 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
17:18:02.293 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
17:18:02.489 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
17:18:02.669 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
17:18:04.116 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
17:18:04.561 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@780546f8
17:18:04.563 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
17:18:04.627 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
17:18:04.973 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.140s)
17:18:05.299 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
17:18:05.418 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
17:18:06.831 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
17:18:06.871 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
17:18:07.115 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
17:18:07.208 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
17:18:07.230 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
17:18:07.395 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
17:18:07.459 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 216ms : this is harmless.
17:18:07.485 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
17:18:07.487 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
17:18:07.523 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
17:18:07.524 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
17:18:07.525 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
17:18:08.937 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
17:18:08.990 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
17:18:09.415 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
17:18:10.040 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c65121
17:18:10.491 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
17:18:10.537 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 13.586 seconds (process running for 15.289)
17:18:10.653 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,56] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
17:18:10.880 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_THREAT_DETECTED, evtDetails=RuntimeDetectedService.SecurityStatus(debuggingDetected=true, jvmTampered=false, fileIntegrityViolated=false, memoryAnomalyDetected=false, networkAnomalyDetected=false, securityLevel=MEDIUM_RISK, lastCheckTime=1756804690635), remark=null, createdDate=2025-09-02T17:18:10.653041900, modifiedDate=2025-09-02T17:18:10.653041900, evtLevel=WARNING, evtMessage=安全事件检查)
17:18:12.040 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 17:18:11 CST 2025, errorMessage=null, details=[], extensions=null)
17:18:12.040 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_LIC_CONTEXT_ERROR, evtDetails=null, remark=null, createdDate=2025-09-02T17:18:11.982372400, modifiedDate=2025-09-02T17:18:11.982372400, evtLevel=ERROR, evtMessage=LIC上下文内容不存在)
17:18:12.041 [scheduling-1] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,56] - [System|系统] - 事件消息发布成功: type=EVT_LIC_CONTEXT_ERROR, message=LIC上下文内容不存在
17:18:12.041 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=false, status=null, validationTime=Tue Sep 02 17:18:12 CST 2025, errorMessage=未找到有效的License, details=[], extensions=null)
17:18:13.088 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
17:18:13.089 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
17:18:13.089 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
17:18:13.089 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
17:18:13.089 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
17:18:13.089 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
17:18:13.089 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
17:18:13.090 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
17:18:13.090 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
17:18:13.090 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
17:18:13.097 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| 50             | 5                | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

17:18:16.656 [http-nio-8090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:18:16.853 [http-nio-8090-exec-1] INFO  c.r.n.m.s.s.LoginService - [loginByAccount,62] - [System|System] - 用户登录: admin
17:18:17.148 [http-nio-8090-exec-1] INFO  c.r.n.m.s.s.OnlineUserService - [getOnlineUsers,60] - [System|System] - 当前在线用户数量: 0
17:18:17.323 [http-nio-8090-exec-1] INFO  c.r.n.m.s.s.LoginService - [loginByAccount,117] - [System|System] - 用户登录成功: LoginOutput(accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TXGKoFQ_4eUfEON1RDLbYhsBPaRXOoniPxk7HCaiu98, refreshToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsInVzZXJJZCI6IjE5NDUzNzY1NjkzMTI3MDI0NjUiLCJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzU2ODA0Njk3LCJleHAiOjE3NTc0MDk0OTd9.hfOPPsWa6NAE7Zs5abWN_IG_1rWwTsXjdRTHQiHrE2U, tokenType=Bearer, expiresIn=86400, userInfo=LoginOutput.UserInfo(userId=1945376569312702465, username=admin), loginResult=200, loginErrMsg=null, onlineUsers=null)
17:18:26.882 [http-nio-8090-exec-7] INFO  c.r.n.m.s.s.OnlineUserService - [getOnlineUsers,60] - [1945376569312702465|admin] - 当前在线用户数量: 1
17:19:47.977 [http-nio-8090-exec-7] INFO  c.r.n.m.s.c.EnvironmentController - [get,144] - [1945376569312702465|admin] - 当前NSE Server未启动，等待启动......
17:19:48.005 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [beforeLauncher,73] - [1945376569312702465|admin] - |--------- [NSE SERVER] 1945376569312702465 启动前检查通过 ---------|
17:19:48.009 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [launcher0,82] - [1945376569312702465|admin] - |--------- [NSE SERVER] 1945376569312702465 服务启动 ---------|
17:19:48.034 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [startPythonProcess,166] - [1945376569312702465|admin] - |--------- [NSE SERVER] 1945376569312702465 获取到可用端口：20000 ---------|
17:19:48.264 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,247] - [1945376569312702465|admin] - |--------- [NSE SERVER] 启动成功 ---------|
17:19:48.265 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,248] - [1945376569312702465|admin] - | 用户: 1945376569312702465
17:19:48.265 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,249] - [1945376569312702465|admin] - | PID: 31604
17:19:48.265 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,250] - [1945376569312702465|admin] - | 端口: 20000
17:19:48.265 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,251] - [1945376569312702465|admin] - | 最大内存: 55.0 GB
17:19:48.265 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,252] - [1945376569312702465|admin] - | CPU核心: 6
17:19:48.265 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,253] - [1945376569312702465|admin] - | 最大磁盘: 10.0 GB
17:19:48.266 [http-nio-8090-exec-7] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,254] - [1945376569312702465|admin] - |----------------------------------------|
17:19:48.427 [http-nio-8090-exec-7] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=SiAEYiuwU6RbBVX5, secret=KQkRqqkCWg5iA7TdLHX6ath4ulF2UaC7d0LD8p0GhM27fbu0gYyL8CGa8hW4063P), pid=31604, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
17:19:50.182 [http-nio-8090-exec-7] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.02321981424148607, memoryTotal=2147483647, memoryUsage=5283840, diskTotal=2147483647, diskUsage=127866402)
17:19:51.490 [http-nio-8090-exec-6] INFO  c.r.n.m.p.a.ServerReachableAspect - [validateServerReachability,56] - [1945376569312702465|admin] - 用户 1945376569312702465 的 NSE 服务可达，服务地址: 127.0.0.1:20000
17:19:51.491 [http-nio-8090-exec-6] INFO  c.r.n.m.c.c.ExperimentController - [createExperiment,62] - [1945376569312702465|admin] - 创建实验，输入参数：ExperimentCreateInput(experimentType=布置作业, experimentName=2323, courseRepoId=, lessonId=1954782347642093569)
17:19:54.118 [http-nio-8090-exec-8] INFO  c.r.n.m.p.a.ServerReachableAspect - [validateServerReachability,56] - [1945376569312702465|admin] - 用户 1945376569312702465 的 NSE 服务可达，服务地址: 127.0.0.1:20000
17:19:54.118 [http-nio-8090-exec-8] INFO  c.r.n.m.c.c.ExperimentController - [createExperiment,62] - [1945376569312702465|admin] - 创建实验，输入参数：ExperimentCreateInput(experimentType=布置作业, experimentName=1231, courseRepoId=, lessonId=1957369508075196418)
17:20:00.771 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
17:20:00.827 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
17:20:00.828 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
17:20:14.238 [http-nio-8090-exec-10] INFO  c.r.n.m.p.a.ServerReachableAspect - [validateServerReachability,56] - [1945376569312702465|admin] - 用户 1945376569312702465 的 NSE 服务可达，服务地址: 127.0.0.1:20000
17:20:14.239 [http-nio-8090-exec-10] INFO  c.r.n.m.c.c.ExperimentController - [createExperiment,62] - [1945376569312702465|admin] - 创建实验，输入参数：ExperimentCreateInput(experimentType=布置作业, experimentName=2323, courseRepoId=, lessonId=1954782347642093569)
17:20:17.247 [http-nio-8090-exec-9] INFO  c.r.n.m.p.a.ServerReachableAspect - [validateServerReachability,56] - [1945376569312702465|admin] - 用户 1945376569312702465 的 NSE 服务可达，服务地址: 127.0.0.1:20000
17:20:17.247 [http-nio-8090-exec-9] INFO  c.r.n.m.c.c.ExperimentController - [createExperiment,62] - [1945376569312702465|admin] - 创建实验，输入参数：ExperimentCreateInput(experimentType=布置作业, experimentName=1231, courseRepoId=, lessonId=1957369508075196418)
17:20:18.018 [http-nio-8090-exec-1] INFO  c.r.n.m.p.a.ServerReachableAspect - [validateServerReachability,56] - [1945376569312702465|admin] - 用户 1945376569312702465 的 NSE 服务可达，服务地址: 127.0.0.1:20000
17:20:18.034 [http-nio-8090-exec-1] INFO  c.r.n.c.m.ResourceMonitor - [logResourceUsage,204] - [1945376569312702465|admin] - 执行 ExperimentController.buildProjectSignUrl 前资源使用情况: 进程内存 0.7099421084920103%, CPU 17.75465549468925%
17:20:18.117 [http-nio-8090-exec-1] INFO  c.r.n.m.c.s.ExperimentService - [buildProjectSignUrl,276] - [1945376569312702465|admin] - 实验实验签名url: http://127.0.0.1:20000/static/web-ui/server/577150394136010753/project/9d5ccd87-7879-be75-9d5c-cd877879be75
17:20:18.118 [http-nio-8090-exec-1] INFO  c.r.n.c.m.ResourceMonitor - [logResourceUsage,204] - [1945376569312702465|admin] - 执行 ExperimentController.buildProjectSignUrl 后资源使用情况: 进程内存 0.7100830086170584%, CPU 17.75465549468925%
17:23:10.761 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 17:23:10 CST 2025, errorMessage=null, details=[], extensions=null)
17:25:00.003 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
17:25:00.058 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
17:25:00.058 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
17:28:10.776 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 17:28:10 CST 2025, errorMessage=null, details=[], extensions=null)
17:30:00.008 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
17:30:00.062 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
17:30:00.062 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
17:33:11.031 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 17:33:10 CST 2025, errorMessage=null, details=[], extensions=null)
17:35:00.001 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
17:35:00.056 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
17:35:00.056 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
17:38:11.321 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 17:38:11 CST 2025, errorMessage=null, details=[], extensions=null)
17:40:00.006 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
17:40:00.061 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
17:40:00.061 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
17:43:11.569 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 17:43:11 CST 2025, errorMessage=null, details=[], extensions=null)
17:45:00.277 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
17:45:00.332 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
17:45:00.332 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
17:48:10.718 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 17:48:10 CST 2025, errorMessage=null, details=[], extensions=null)
17:50:00.703 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
17:50:00.817 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
17:50:00.817 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
17:53:10.737 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 17:53:10 CST 2025, errorMessage=null, details=[], extensions=null)
17:55:00.006 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
17:55:00.062 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
17:55:00.062 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
17:58:10.723 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 17:58:10 CST 2025, errorMessage=null, details=[], extensions=null)
18:00:00.006 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:00:00.065 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
18:00:00.065 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
18:03:11.242 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 18:03:11 CST 2025, errorMessage=null, details=[], extensions=null)
18:05:00.105 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:05:00.161 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
18:05:00.161 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
18:08:10.705 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 18:08:10 CST 2025, errorMessage=null, details=[], extensions=null)
18:10:00.582 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:10:00.639 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
18:10:00.639 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
18:13:10.750 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 18:13:10 CST 2025, errorMessage=null, details=[], extensions=null)
18:15:00.952 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:15:01.007 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
18:15:01.008 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
18:18:10.705 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 18:18:10 CST 2025, errorMessage=null, details=[], extensions=null)
18:20:00.012 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:20:00.129 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
18:20:00.129 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
18:23:11.234 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Tue Sep 02 18:23:11 CST 2025, errorMessage=null, details=[], extensions=null)
18:25:00.011 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
18:25:00.068 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
18:25:00.069 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
