package com.ruijie.nse.mgr.course.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.service.cache.EhcacheService;
import com.ruijie.nse.common.utils.enctry.JwtUtil;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.common.handler.StudentLoginManager;
import com.ruijie.nse.mgr.course.constants.CourseConstants;
import com.ruijie.nse.mgr.course.constants.LessonConstants;
import com.ruijie.nse.mgr.course.dto.input.AssignHomeworkInput;
import com.ruijie.nse.mgr.course.dto.input.CourseInput;
import com.ruijie.nse.mgr.course.dto.output.*;
import com.ruijie.nse.mgr.license.service.LicenseActivationInfoService;
import com.ruijie.nse.mgr.py3server.dto.ProcessDto;
import com.ruijie.nse.mgr.py3server.launcher.Python3ServerLauncher;
import com.ruijie.nse.mgr.py3server.service.ProcessService;
import com.ruijie.nse.mgr.repository.dto.input.CourseQueryInput;
import com.ruijie.nse.mgr.repository.dto.input.CourseStudentQueryInput;
import com.ruijie.nse.mgr.repository.dto.input.UserQueryInput;
import com.ruijie.nse.mgr.repository.dto.output.CourseStudentOutput;
import com.ruijie.nse.mgr.repository.dto.output.UserOutput;
import com.ruijie.nse.mgr.repository.entity.*;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import com.ruijie.nse.mgr.repository.mapper.CourseDao;
import com.ruijie.nse.mgr.repository.mapper.UserDao;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.dromara.hutool.core.bean.BeanUtil;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.collection.set.SetUtil;
import org.dromara.hutool.core.date.DateFormatPool;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.map.MapUtil;
import org.dromara.hutool.core.text.StrPool;
import org.dromara.hutool.core.text.StrValidator;
import org.dromara.hutool.poi.excel.style.StyleUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程服务类
 */
@Slf4j
@Service
public class CourseService extends ServiceImpl<CourseDao, Course> {


    @Resource
    private LessonService lessonService;

    @Resource
    private CourseLessonService courseLessonService;

    @Resource
    private CourseRepoService courseRepoService;

    @Resource
    private UserDao userDao;

    @Resource
    private LicenseActivationInfoService licenseActivationInfoService;

    @Resource
    private StudentLoginManager studentLoginManager;

    @Resource
    private EhcacheService ehcacheService;

    @Resource
    private Python3ServerLauncher python3ServerLauncher;

    @Resource
    private ProcessService processService;


    /**
     * 分页查询课程
     *
     * @param queryInput 查询条件
     * @return 分页结果
     */
    public PageOutput<CourseOutput> findByPage(CourseQueryInput queryInput) {
        String userType = SecurityUtils.getUserType();
        if (UserType.TEACHER.getValue().equals(userType)) {
            queryInput.setUserId(SecurityUtils.getUserId());
        }
        Page<Course> page = baseMapper.findPage(new Page<>(queryInput.getPageNumber(), queryInput.getPageSize()), queryInput);
        List<CourseOutput> records = BeanUtil.copyToList(page.getRecords(), CourseOutput.class);
        return new PageOutput<>(page.getTotal(), records);
    }

    /**
     * 新建课程
     * @param courseInput
     */
    @Transactional
    public void create(CourseInput courseInput) {
        Course course = BeanUtil.toBean(courseInput, Course.class);
        String userId = SecurityUtils.getUserId();
        course.setUserId(userId);
        course.setStatus(CourseConstants.CourseStatus.NOT_STARTED);
        course.setStudentCnt(CollUtil.size(courseInput.getUserIds()));
        course.setExpCnt(0);
        baseMapper.insert(course);

        addCourseStudent(course, courseInput.getUserIds());
    }

    /**
     * 根据ID获取课程详情
     *
     * @param id 课程ID
     * @return 课程详情
     */
    public CourseOutput getById(String id) {
        Course course = baseMapper.selectById(id);
        if (course == null) {
            throw BusinessException.errorByMessage("课程不存在");
        }
        return BeanUtil.toBean(course, CourseOutput.class);
    }

    /**
     * 批量删除课程
     *
     * @param ids 课程ID列表
     */
    @Transactional
    public void deleteBatch(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw BusinessException.errorByMessage("请选择要删除的数据");
        }
        List<Course> courses = baseMapper.selectByIds(ids);
        long count = courses.stream().filter(course -> CourseConstants.CourseStatus.STARTED.equals(course.getStatus())).count();
        if (count > 0) {
            throw BusinessException.errorByMessage("正在上课中的课程，不允许删除");
        }
        List<CourseLesson> courseLessonList = courseLessonService.list(Wrappers.lambdaQuery(CourseLesson.class).in(CourseLesson::getCourseId, ids));
        if (CollUtil.isNotEmpty(courseLessonList)) {
            List<String> lessonIds = courseLessonList.stream().map(CourseLesson::getLessonId).toList();
            lessonService.remove(Wrappers.lambdaQuery(Lesson.class)
                    .in(Lesson::getId, lessonIds)
                    .eq(Lesson::getLessonType, LessonConstants.LessonType.CLASS)
            );
        }
        baseMapper.deleteByIds(ids);
    }


    /**
     * 分页查询课程学生
     *
     * @param courseStudentQueryInput 查询条件
     * @return 分页结果
     */
    public CourseStudentPageOutput findCourseStudentByPage(CourseStudentQueryInput courseStudentQueryInput)  {
        CourseQueryInput queryInput = new CourseQueryInput();
        queryInput.setId(courseStudentQueryInput.getCourseId());
        Page<Course> coursePage = baseMapper.findPage(new Page<>(1, -1), queryInput);
        Course course = CollUtil.getFirst(coursePage.getRecords());
        if (course == null) {
            throw BusinessException.errorByMessage("课程不存在");
        }
        User user = userDao.selectById(course.getUserId());
        Page<CourseStudentOutput> page = baseMapper.findCourseStudentByPage(new Page<>(courseStudentQueryInput.getPageNumber(), courseStudentQueryInput.getPageSize()), courseStudentQueryInput);
        CourseStudentPageOutput pageOutput = new CourseStudentPageOutput();
        pageOutput.setRows(page.getRecords());
        pageOutput.setTotal(page.getTotal());
        pageOutput.setCourseName(course.getCourseName());
        pageOutput.setClassName(course.getClassName());
        pageOutput.setStudentCnt(course.getStudentCnt());
        Optional.ofNullable(user).ifPresent(u -> pageOutput.setTeacherName(u.getName()));
        return pageOutput;
    }


    /**
     * 获取课程学生ID列表
     *
     * @param courseId 课程ID
     * @return 学生ID列表
     */
    public List<String> getStudentIds(String courseId) {
        List<Lesson> lessonList = lessonService.getLessonsByCourseId(courseId, LessonConstants.LessonType.CLASS);
        return lessonList.stream().map(Lesson::getStudentId).toList();
    }

    /**
     * 添加课程学生
     *
     * @param courseInput 课程信息
     */
    @Transactional
    public void addStudent(CourseInput courseInput) {
        Course course = baseMapper.selectById(courseInput.getId());
        if (course == null) {
            throw BusinessException.errorByMessage("课程不存在");
        }
        if (CourseConstants.CourseStatus.STARTED.equals(course.getStatus())) {
            throw BusinessException.errorByMessage("上课中的课程不允许新增学生");
        }
        addCourseStudent(course, courseInput.getUserIds());

        List<Lesson> homeworkList = lessonService.getLessonsByCourseId(course.getId(), LessonConstants.LessonType.HOMEWORK);
        if (CollUtil.isEmpty(homeworkList)) {
            return;
        }
        // 需要同步原来学生已经布置的作业
        Map<String, List<Lesson>> map = homeworkList.stream().collect(Collectors.groupingBy(Lesson::getStudentId));
        List<Lesson> lessons = map.values().stream().max(Comparator.comparingInt(List::size)).orElse(new ArrayList<>());

        List<Lesson> lessonList = courseInput.getUserIds().stream().flatMap(studentId -> {
            List<String> homeworkNos = map.get(studentId) == null ? Collections.emptyList() : map.get(studentId).stream().map(Lesson::getRemark).toList();
            return lessons.stream().filter(lesson -> !homeworkNos.contains(lesson.getRemark()))
                    .map(lesson -> {
                        Lesson homework = new Lesson();
                        homework.setRemark(lesson.getRemark());
                        return homework
                                .setStudentId(studentId)
                                .setTeacherId(lesson.getTeacherId())
                                .setCourseName(lesson.getCourseName())
                                .setExpName(lesson.getExpName())
                                .setCourseRepoId(lesson.getCourseRepoId())
                                .setLessonType(LessonConstants.LessonType.HOMEWORK)
                                .setSubmitStatus(LessonConstants.LessonStatus.NOT_SUBMIT);
                    }).toList().stream();
        }).toList();
        lessonService.saveBatch(lessonList);

        List<CourseLesson> courseLessonList = lessonList.stream()
                .map(lesson -> new CourseLesson()
                        .setCourseId(course.getId())
                        .setLessonId(lesson.getId()))
                .toList();
        courseLessonService.saveBatch(courseLessonList);
    }

    @Transactional
    public void addCourseStudent(Course course, List<String> studentIds) {
        if (CollUtil.isEmpty(studentIds)) {
            return;
        }
        List<Lesson> lessonList = studentIds.stream()
                .map(studentId -> new Lesson()
                        .setStudentId(studentId)
                        .setTeacherId(course.getUserId())
                        .setLessonType(LessonConstants.LessonType.CLASS)
                        .setCourseName(course.getCourseName()))
                .toList();
        lessonService.saveBatch(lessonList);
        List<CourseLesson> courseLessonList = lessonList.stream()
                .map(lesson -> new CourseLesson()
                        .setCourseId(course.getId())
                        .setLessonId(lesson.getId()))
                .toList();
        courseLessonService.saveBatch(courseLessonList);
    }


    /**
     * 删除课程学生
     *
     * @param ids
     */
    @Transactional
    public void deleteStudent(List<String> ids) {
        List<Lesson> lessons = lessonService.listByIds(ids);
        if (CollUtil.isEmpty(lessons)) {
            return;
        }
        List<CourseLesson> courses = courseLessonService.list(Wrappers.lambdaQuery(CourseLesson.class).in(CourseLesson::getLessonId, ids));
        if (CollUtil.isNotEmpty(courses)) {
            List<String> courseIds = courses.stream().map(CourseLesson::getCourseId).distinct().toList();
            List<Course> courseList = baseMapper.selectByIds(courseIds);
            long count = courseList.stream().filter(course -> CourseConstants.CourseStatus.STARTED.equals(course.getStatus()))
                    .count();
            if (count > 0) {
                throw BusinessException.errorByMessage("上课中的课程不允许删除学生");
            }
            courseLessonService.remove(Wrappers.lambdaUpdate(CourseLesson.class).in(CourseLesson::getLessonId, ids));
        }
        lessonService.removeByIds(ids);
    }

    /**
     * 导出课程数据
     *
     * @param courseId
     * @param response
     */
    public void exportCourseData(String courseId, HttpServletResponse response) {
        Course course = baseMapper.selectById(courseId);
        if (course == null) {
            throw BusinessException.errorByMessage("课程不存在");
        }
        String fileName = String.format("%s-%s-%s.xlsx", course.getCourseName(), course.getClassName(), DateUtil.format(new Date(), DateFormatPool.PURE_DATETIME_PATTERN));
        List<String> studentHeadList = Arrays.asList("学号", "姓名", "班级", "实验完成率", "实验平均分");
        List<String> submitHeadList = Arrays.asList("提交状态", "提交时间", "分数");
        List<String> expNameList = Collections.emptyList();
        List<UserOutput> userList = Collections.emptyList();
        Map<String, List<Lesson>> studentLessonMap = MapUtil.newHashMap();
        List<Lesson> lessonExpList = Collections.emptyList();

        List<Lesson> classLessonList = lessonService.getLessonsByCourseId(courseId, LessonConstants.LessonType.CLASS);
        List<String> classStudentIds = classLessonList.stream().map(Lesson::getStudentId).toList();
        if (CollUtil.isNotEmpty(classStudentIds)) {
            // 学生信息获取
            UserQueryInput userQueryInput = new UserQueryInput();
            userQueryInput.setUserIds(String.join(StrPool.COMMA, classStudentIds));
            Page<UserOutput> page = userDao.findPage(new Page<>(1, -1), userQueryInput);
            userList = page.getRecords();

            // 获取课程所有的实验作业筛选出还在课程下的学生作业
            List<Lesson> lessonList = lessonService.getLessonsByCourseId(courseId, LessonConstants.LessonType.HOMEWORK);
            lessonList = lessonList.stream()
                    .filter(lesson -> StrValidator.isNotBlank(lesson.getStudentId()))
                    .filter(lesson -> classStudentIds.contains(lesson.getStudentId()))
                    .sorted(Comparator.comparing(Lesson::getStudentId))
                    .toList();
            if (CollUtil.isNotEmpty(lessonList)) {
                studentLessonMap = lessonList.stream().collect(Collectors.groupingBy(Lesson::getStudentId));

                // 获取布置的实验作业表头
                lessonExpList = studentLessonMap.values().stream().max(Comparator.comparingInt(List::size)).orElse(new ArrayList<>());
                lessonExpList.sort(Comparator.comparing(Lesson::getRemark));
                expNameList = lessonExpList.stream().map(Lesson::getExpName).toList();
            }
        }

        try (Workbook wb = new SXSSFWorkbook();
             ServletOutputStream outputStream = response.getOutputStream()) {
            Sheet sheet = wb.createSheet("课程实验数据明细");

            // 表头样式
            CellStyle studentInfoCellStyle = StyleUtil.createDefaultCellStyle(wb);
            studentInfoCellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
            studentInfoCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            CellStyle expNameCellStyle = StyleUtil.createHeadCellStyle(wb);
            expNameCellStyle.setWrapText(true);
            CellStyle submitHeadCellStyle = StyleUtil.createDefaultCellStyle(wb);

            // 写入学生信息表头
            Row row = sheet.createRow(0);
            Row submitHeadRow = sheet.createRow(1);
            for (int i = 0; i < studentHeadList.size(); i++) {
                Cell cell = createCell(row, i, studentInfoCellStyle);
                cell.setCellValue(studentHeadList.get(i));
                createCell(submitHeadRow, i, studentInfoCellStyle);
                sheet.addMergedRegion(new CellRangeAddress(0, 1, i, i));
                sheet.setColumnWidth(i, 20 * 256);
            }

            // 写入实验名称表头
            int expStartCol = studentHeadList.size();
            for (int i = 0; i < expNameList.size(); i++) {
                int startColIndex = expStartCol + (i * 3);
                Cell cell = createCell(row, startColIndex, expNameCellStyle);
                cell.setCellValue(expNameList.get(i));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, startColIndex, startColIndex + 2));
                for (int i1 = 0; i1 < submitHeadList.size(); i1++) {
                    Cell cell1 = createCell(submitHeadRow, startColIndex + i1, submitHeadCellStyle);
                    cell1.setCellValue(submitHeadList.get(i1));
                    sheet.setColumnWidth(startColIndex + i1, 20 * 256);
                }
            }

            int dataRowStartIndex = 2;
            if (CollUtil.isNotEmpty(userList)) {
                CellStyle cellStyle = StyleUtil.createDefaultCellStyle(wb);
                cellStyle.setAlignment(HorizontalAlignment.LEFT);
                for (UserOutput user : userList) {
                    Row userRow = sheet.createRow(dataRowStartIndex);
                    createCell(userRow, 0, cellStyle).setCellValue(user.getAccount());
                    createCell(userRow, 1, cellStyle).setCellValue(user.getName());
                    createCell(userRow, 2, cellStyle).setCellValue(user.getClasses());

                    List<Lesson> lessonList = studentLessonMap.get(user.getId());
                    double completeRate = 0.0;
                    double avgScore = 0.0;
                    if (CollUtil.isNotEmpty(lessonList)) {
                        long count = lessonList.stream()
                                .filter(lesson -> LessonConstants.LessonStatus.SUBMITTED.equals(lesson.getSubmitStatus()))
                                .count();
                        // 计算作业的完成率
                        completeRate = count * 100.0 / lessonExpList.size();
                        // 计算作业的平均分
                        avgScore = lessonList.stream()
                                .filter(lesson -> lesson.getScore() != null)
                                .mapToDouble(lesson -> lesson.getScore().doubleValue())
                                .average()
                                .orElse(0.0);

                        int expStartColIndex = 5;
                        for (Lesson lesson : lessonExpList) {
                            int finalExpStartColIndex = expStartColIndex;
                            lessonList.stream().filter(userLesson -> lesson.getRemark().equals(userLesson.getRemark()))
                                    .findFirst()
                                    .ifPresent(userLesson -> {
                                        String submitDate = Objects.nonNull(userLesson.getSubmitDate()) ? userLesson.getSubmitDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : StrValidator.EMPTY;
                                        String score = Objects.nonNull(userLesson.getScore()) ? userLesson.getScore().toString() : StrValidator.EMPTY;
                                        createCell(userRow, finalExpStartColIndex, cellStyle).setCellValue(userLesson.getSubmitStatus());
                                        createCell(userRow, finalExpStartColIndex + 1, cellStyle).setCellValue(submitDate);
                                        createCell(userRow, finalExpStartColIndex + 2, cellStyle).setCellValue(score);
                                    });
                            expStartColIndex += 3;
                        }
                    }
                    createCell(userRow, 3, cellStyle).setCellValue(String.format("%.2f%%", completeRate));
                    createCell(userRow, 4, cellStyle).setCellValue(String.format("%.2f", avgScore));
                    dataRowStartIndex++;
                }
            }

            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            wb.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            throw BusinessException.errorByMessage("导出Excel失败");
        }
    }

    private Cell createCell(Row row, int colIndex, CellStyle cellStyle) {
        Cell rowCell = row.createCell(colIndex);
        if (cellStyle != null) {
            rowCell.setCellStyle(cellStyle);
        }
        return rowCell;
    }

    /**
     * 上课前检查课程
     * @param courseId 课程ID
     */
    public Course checkCourseBeforeStartCourse(String courseId) {
        // 查询课程信息
        Course course = baseMapper.selectById(courseId);
        if (Objects.isNull(course)) {
            throw BusinessException.errorByMessage("课程不存在");
        }

        // 校验课程状态
        if (CourseConstants.CourseStatus.STARTED.equals(course.getStatus())) {
            throw BusinessException.errorByMessage("课程已在上课中");
        }

        // 校验教师是否已有课程在上课
        String userId = course.getUserId();
        Long count = baseMapper.selectCount(Wrappers.lambdaQuery(Course.class)
                .eq(Course::getUserId, userId)
                .eq(Course::getStatus, CourseConstants.CourseStatus.STARTED));
        if (count > 0) {
            throw BusinessException.errorByMessage("您当前已有课程在上课，无法重复上课");
        }
        return course;
    }

    /**
     * 上课前检查课程并发
     * @param courseId 课程ID
     */
    public CourseStartCheckResultOutput checkBeforeStartCourse(String courseId) {
        // 检查课程信息
        checkCourseBeforeStartCourse(courseId);

        List<Lesson> lessons = lessonService.getLessonsByCourseId(courseId, LessonConstants.LessonType.CLASS);
        // 校验并发数量
        List<String> userIds = lessons.stream()
                .map(Lesson::getStudentId)
                .filter(StrValidator::isNotBlank)
                .toList();
        CourseStartCheckResultOutput output = new CourseStartCheckResultOutput()
                .setCanStart(true);
        try {
            List<String> toBeOngoingUserIds = studentLoginManager.addOngoingStudentCheck(userIds);
            List<String> exerciseStudentUserId = studentLoginManager.getExerciseStudentUserId();
            long count = toBeOngoingUserIds.stream().filter(id -> !exerciseStudentUserId.contains(id)).count();
            if (count > studentLoginManager.studentRemainLoginCount()) {
                output.setMessage(String.format("当前还有%s位非上课班级学生正在使用，确认上课后将在3分钟后强制退出非上课班级学生账号，本班级学生部分账号需等待3分钟后再登录",
                        studentLoginManager.getExerciseStudentCount()));
            }
            return output;
        } catch (BusinessException e) {
            output.setCanStart(false)
                    .setMessage(e.getMessage());
        }
        return output;
    }

    /**
     * 开始课程
     * @param courseId
     */
    @Transactional
    public void startCourse(String courseId) {
        Course course = checkCourseBeforeStartCourse(courseId);
        course.setStatus(CourseConstants.CourseStatus.STARTED);
        baseMapper.updateById(course);

        List<Lesson> lessons = lessonService.getLessonsByCourseId(courseId, LessonConstants.LessonType.CLASS);
        List<String> userIds = lessons.stream().map(Lesson::getStudentId).filter(StrValidator::isNotBlank).toList();
        studentLoginManager.addOngoingStudent(userIds);
    }

    /**
     * 结束课程
     * @param courseId
     */
    @Transactional
    public void endCourse(String courseId) {
        Course course = baseMapper.selectById(courseId);
        if (Objects.isNull(course)) {
            throw BusinessException.errorByMessage("课程不存在");
        }
        if (CourseConstants.CourseStatus.NOT_STARTED.equals(course.getStatus())) {
            throw BusinessException.errorByMessage("课程未开始");
        }
        course.setStatus(CourseConstants.CourseStatus.NOT_STARTED);
        baseMapper.updateById(course);

        // 排除同一个学生在不同上课课程的
        LambdaQueryWrapper<Course> wrapper = Wrappers.lambdaQuery(Course.class)
                .eq(Course::getStatus, CourseConstants.CourseStatus.STARTED);
        List<Course> courses = baseMapper.selectList(wrapper);
        Set<String> set = SetUtil.zero();
        if (CollUtil.isNotEmpty(courses)) {
            for (Course c : courses) {
                List<Lesson> lessonList = lessonService.getLessonsByCourseId(c.getId(), LessonConstants.LessonType.CLASS);
                lessonList.stream().map(Lesson::getStudentId).filter(StrValidator::isNotBlank).forEach(set::add);
            }
        }
        List<Lesson> lessonList = lessonService.getLessonsByCourseId(courseId, LessonConstants.LessonType.CLASS);
        List<String> userIds = lessonList.stream()
                .map(Lesson::getStudentId)
                .filter(StrValidator::isNotBlank)
                .filter(userId -> !set.contains(userId))
                .toList();
        studentLoginManager.endOngoingStudent(userIds);
    }

    /**
     * 布置作业
     * @param input 布置作业参数
     */
    @Transactional
    public void assignHomework(AssignHomeworkInput input) {
        // 查询课程信息
        Course course = baseMapper.selectById(input.getCourseId());
        if (Objects.isNull(course)) {
            throw BusinessException.errorByMessage("课程不存在");
        }

        // 查询课程库信息
        Optional<CourseRepo> repoOpt = courseRepoService.lambdaQuery().eq(CourseRepo::getId, input.getRepoId()).oneOpt();
        CourseRepo repo = repoOpt.orElseThrow(() -> BusinessException.errorByMessage("课程库实验不存在"));

        List<Lesson> lessonList = lessonService.getLessonsByCourseId(input.getCourseId(), LessonConstants.LessonType.CLASS);
        if (CollUtil.isEmpty(lessonList)) {
            throw BusinessException.errorByMessage("该课程下暂无学生");
        }
        // 作业批次号
        long no = DateUtil.current();
        List<Lesson> homeworks = lessonList.stream().map(lesson -> {
            Lesson homework = new Lesson();
            homework.setTeacherId(lesson.getTeacherId());
            homework.setStudentId(lesson.getStudentId());
            homework.setCourseName(course.getCourseName());
            homework.setExpName(repo.getExpName());
            homework.setCourseRepoId(repo.getId());
            homework.setLessonType(LessonConstants.LessonType.HOMEWORK);
            homework.setSubmitStatus(LessonConstants.LessonStatus.NOT_SUBMIT);
            homework.setRemark(String.valueOf(no));
            homework.setCourseRepoSnapshot(repo);
            return homework;
        }).toList();
        lessonService.saveBatch(homeworks);
        List<CourseLesson> courseLessons = homeworks.stream()
                .map(homework -> new CourseLesson().setCourseId(input.getCourseId()).setLessonId(homework.getId()))
                .toList();
        courseLessonService.saveBatch(courseLessons);
    }

    /**
     * 获取进行中的课程
     * @return
     */
    public List<OngoingCourseOutput> getOngoingCourses() {
        List<Course> courseList = baseMapper.selectList(Wrappers.lambdaQuery(Course.class).eq(Course::getStatus, CourseConstants.CourseStatus.STARTED));
        if (CollUtil.isNotEmpty(courseList)) {
            List<String> teacherIds = courseList.stream().map(Course::getUserId).toList();
            List<User> teacherList = userDao.selectList(Wrappers.lambdaQuery(User.class).in(User::getId, teacherIds));
            Map<String, User> teacherMap = teacherList.stream().collect(Collectors.toMap(User::getId, user -> user));
            List<String> onlineUserIds = getOnlineUserIds();
            return courseList.stream().map(course -> {
                OngoingCourseOutput output = BeanUtil.toBean(course, OngoingCourseOutput.class);
                Optional.ofNullable(teacherMap.get(course.getUserId()))
                        .ifPresent(user -> output.setTeacherName(user.getName()));
                List<String> courseUserIds = getCourseUserIds(course.getId());
                output.setTotalCnt(courseUserIds.size());
                output.setStudentCnt(courseUserIds.size() - 1);
                output.setLoginCnt((int) courseUserIds.stream().filter(onlineUserIds::contains).count());
                return output;
            }).toList();
        }
        return Collections.emptyList();
    }


    /**
     * 获取课程用户id
     * @param courseId
     * @return
     */
    public List<String> getCourseUserIds(String courseId) {
        Course course = baseMapper.selectById(courseId);
        if (Objects.isNull(course)) {
            return Collections.emptyList();
        }
        List<String> userIds = new ArrayList<>();
        userIds.add(course.getUserId());
        List<Lesson> lessonList = lessonService.getLessonsByCourseId(courseId, LessonConstants.LessonType.CLASS);
        if (CollUtil.isEmpty(lessonList)) {
            return userIds;
        }
        lessonList.stream().map(Lesson::getStudentId).forEach(userIds::add);
        return userIds;
    }

    /**
     * 获取进行中的课程学生
     * @param courseId 课程id
     * @return
     */
    public List<LoginUserOutput> getOngoingCourseAccount(String courseId) {
        List<String> courseUserIds = getCourseUserIds(courseId);
        List<LoginUserOutput> outputList = getLoginUsers(courseUserIds);
        List<String> onlineUserIds = getOnlineUserIds();
        for (LoginUserOutput loginUserOutput : outputList) {
            if (onlineUserIds.contains(loginUserOutput.getId())) {
                loginUserOutput.setLoginStatus("已登录");
                fillUserUsage(loginUserOutput);
                continue;
            }
            loginUserOutput.setLoginTime(null);
            loginUserOutput.setLoginStatus("未登录");
        }

        List<LoginUserOutput> sortedLoginUsers = new ArrayList<>(outputList);
        sortedLoginUsers.sort(Comparator.comparing(LoginUserOutput::getLoginTime, Comparator.nullsLast(Comparator.reverseOrder())));
        return sortedLoginUsers;
    }


    /**
     * 获取登录用户输出列表
     * @param userIds
     * @return
     */
    private List<LoginUserOutput> getLoginUsers(List<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        UserQueryInput queryInput = new UserQueryInput();
        queryInput.setUserIds(CollUtil.join(userIds, StrPool.COMMA));
        List<UserOutput> users = userDao.findPage(new Page<>(1, userIds.size()), queryInput).getRecords();
        return users.stream()
                .sorted(Comparator.comparing(UserOutput::getUserType))
                .map(this::userToLoginUserOutput).toList();
    }

    /**
     * 用户转成进行中的课程用户输出
     * @param user
     * @return
     */
    private LoginUserOutput userToLoginUserOutput(UserOutput user) {
        LoginUserOutput userOutput = new LoginUserOutput();
        userOutput.setId(user.getId());
        userOutput.setAccount(user.getAccount());
        userOutput.setName(user.getName());
        String description = Optional.ofNullable(user.getUserType())
                .orElse(UserType.UNKNOWN)
                .getDescription();
        userOutput.setRole(description);
        userOutput.setClassName(user.getClasses());
        userOutput.setLoginTime(user.getLoginDate());
        return userOutput;
    }

    /**
     * 获取练习的账号列表
     * @return
     */
    public PageOutput<LoginUserOutput> getExerciseAccount(PageInput pageInput) {
        List<String> userIds = getOnlineUserIds();
        List<Course> courseList = baseMapper.selectList(Wrappers.lambdaQuery(Course.class)
                .eq(Course::getStatus, CourseConstants.CourseStatus.STARTED));
        if (CollUtil.isNotEmpty(courseList)) {
            List<String> courseUserIds = courseList.stream().flatMap(course -> getCourseUserIds(course.getId()).stream()).toList();
            userIds = userIds.stream().filter(id -> !courseUserIds.contains(id)).toList();
        }
        PageOutput<LoginUserOutput> pageOutput = new PageOutput<>();
        pageOutput.setTotal((long)userIds.size());
        if (CollUtil.isEmpty(userIds)) {
            return pageOutput;
        }
        // 对userIds进行分页
        List<List<String>> lists = CollUtil.partition(userIds, pageInput.getPageSize());
        userIds = CollUtil.get(lists, pageInput.getPageNumber() - 1);
        if (CollUtil.isEmpty(userIds)) {
            return pageOutput;
        }
        List<LoginUserOutput> loginUsers = getLoginUsers(userIds);
        loginUsers.forEach(loginUserOutput -> {
            loginUserOutput.setLoginStatus("已登录");
            fillUserUsage(loginUserOutput);
        });
        List<LoginUserOutput> sortedLoginUsers = new ArrayList<>(loginUsers);
        sortedLoginUsers.sort(Comparator.comparing(LoginUserOutput::getLoginTime, Comparator.nullsLast(Comparator.reverseOrder())));
        pageOutput.setRows(sortedLoginUsers);
        return pageOutput;
    }

    /**
     * 填充用户资源信息
     * @param userOutput
     */
    public void fillUserUsage(LoginUserOutput userOutput) {
        try {
            SerHosts serHost = python3ServerLauncher.getServerInfo(userOutput.getId());
            if(serHost != null && processService.isValidPid(serHost.getPid())) {
                // 根据pid获取当前的资源情况
                ProcessDto processDto = processService.getProcessInfo(serHost.getPid());
                userOutput.setCpuUsage(processDto.getCpuUsage());
                userOutput.setMemoryUsage(processDto.getMemoryUsage());
                userOutput.setStorageUsage(processDto.getDiskUsage());
            }
        } catch (Exception e) {
            log.info("获取用户资源信息失败: ", e);
        }
    }

    /**
     * 获取在线用户列表
     * @return
     */
    public List<String> getOnlineUserIds() {
        List<String> onlineUsers = new ArrayList<>();
        try {
            // 获取JWT缓存中的所有Token
            List<String> allTokens = ehcacheService.getAllValues(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, String.class);
            for (String token : allTokens) {
                if (JwtUtil.validateToken(token)) {
                    Optional.ofNullable(JwtUtil.getUserId(token))
                            .ifPresent(onlineUsers::add);
                }
            }
        } catch (Exception e) {
            log.error("获取在线用户列表失败", e);
        }
        return onlineUsers;
    }

}