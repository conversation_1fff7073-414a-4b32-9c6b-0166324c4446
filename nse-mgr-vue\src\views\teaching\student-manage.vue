<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
        <el-form-item label="学号" prop="account">
          <el-input
            v-model="queryParams.account"
            placeholder="请输入"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="班级名称" prop="className">
          <el-input
            v-model="queryParams.className"
            placeholder="请输入"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <!-- 展开后显示的查询条件 -->
        <template v-if="isExpand">
          <el-form-item label="添加人" prop="createdBy">
            <el-input
              v-model="queryParams.createdBy"
              placeholder="请输入"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <el-form-item label="添加时间" prop="createdDate">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 380px"
            />
          </el-form-item>
        </template>
      </el-form>
      <div class="search-buttons">
        <el-button type="primary" icon="search" @click="handleQuery">查询</el-button>
        <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
        <el-link class="ml-3" type="primary" underline="never" @click="isExpand = !isExpand">
          {{ isExpand ? "收起" : "展开" }}
          <component :is="isExpand ? ArrowUp : ArrowDown" class="w-4 h-4 ml-2" />
        </el-link>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card shadow="hover" class="data-table">
      <div class="preview-row" v-show="courseName">
        <span class="preview-title">课程：</span>
        <span class="preview-value">{{ courseName }}-{{ className }}</span>
        <span class="preview-title">任课老师：</span>
        <span class="preview-value">{{ teacherName }}</span>
        <span class="preview-title">总人数：</span>
        <span class="preview-value">{{ studentCnt }}人</span>
      </div>
      <div class="data-table__toolbar">
        <div class="data-table__toolbar--actions">
          <el-button
            v-hasPerm="['teaching:student:add']"
            type="primary"
            icon="CirclePlus"
            @click="handleAddStudent"
            :disabled="course?.status === '上课中'"
          >
            新增学生
          </el-button>
          <el-button
            v-hasPerm="['teaching:student:delete']"
            type="danger"
            plain
            icon="delete"
            :disabled="selectIds.length === 0"
            @click="handleBatchDeleteStudents()"
          >
            批量删除学生
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="students"
        border
        stripe
        highlight-current-row
        class="data-table__content"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" :selectable="(row: StudentVO) => course?.status !== '上课中'"/>
        <el-table-column type="index" label="序号" width="70" />
        <el-table-column label="学号" width="200">
          <template #default="scope">
            <span style="color: #4080FF"> {{ scope.row.account }} </span>
          </template>
        </el-table-column>
        <el-table-column label="角色" prop="role" width="150">
          <template #default="scope">
            <span>  学生  </span>
          </template>
        </el-table-column>
        <el-table-column label="姓名" width="150">
          <template #default="scope">
            <span style="color: #4080FF"> {{ scope.row.name }} </span>
          </template>
        </el-table-column>
        <el-table-column label="班级" prop="className" width="150" />
        <el-table-column label="添加人" prop="createdBy" width="150" />
        <el-table-column label="添加时间" prop="createdDate" width="180" />
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="scope">
            <el-button
              v-hasPerm="['teaching:student:delete']"
              type="danger"
              link
              icon="delete"
              size="small"
              :disabled="course?.status === '上课中'"
              @click="handleBatchDeleteStudents(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNumber"
        v-model:limit="queryParams.pageSize"
        @pagination="fetchStudents"
      />
    </el-card>

    <!-- 新增学生对话框 -->
    <el-dialog v-model="dialogVisible" title="新增学生" width="800px">
      <Transfer
        v-model="selectedStudents"
        :data="userOptions"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmAddStudents">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import type { FormInstance } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { ArrowUp, ArrowDown } from "@element-plus/icons-vue";
import Transfer from './components/transfer.vue';
import TeachingAPI from "@/api/teaching/teaching.api";
import AccountAPI from "@/api/account/account.api";
import type { StudentPageQuery, StudentVO, StudentPageResult } from "@/api/teaching/types/teaching.types";
import { useTagsViewStore } from "@/store";

const tagsViewStore = useTagsViewStore();
const router = useRouter();
const route = useRoute();

// 从路由参数中获取课程ID
const courseId = computed(() => route.query.id as string);
const course = ref();

// 展开/收起状态
const isExpand = ref(false);

// 查询参数
const queryParams = reactive<StudentPageQuery>({
  pageNumber: 1,
  pageSize: 10,
  courseId: courseId.value,
  account: '',
  name: '',
  className: '',
  createdBy: '',
  startTime: '',
  endTime: ''
});

const courseName = ref();
const className = ref();
const teacherName = ref();
const studentCnt = ref(0);

// 时间范围
const dateRange = ref<string[]>([]);

// 表格数据
const loading = ref(false);
const students = ref<StudentVO[]>([]);
const studentPageData = ref<StudentVO[]>([]);
const total = ref(0);
const selectIds = ref<string[]>([]);

// 表单引用
const queryFormRef = ref<FormInstance>();

// 新增学生对话框
const dialogVisible = ref(false);
const selectedStudents = ref<string[]>([]);
const userOptions = ref([]);

// 获取学生列表
const fetchStudents = () => {
  loading.value = true;
  // 处理时间范围
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startTime = dateRange.value[0];
    queryParams.endTime = dateRange.value[1];
  } else {
    queryParams.startTime = '';
    queryParams.endTime = '';
  }
  TeachingAPI.getCourseStudentPage(queryParams).then(data => {
    students.value = data.rows;
    total.value = data.total;
    courseName.value = data.courseName;
    className.value = data.className;
    teacherName.value = data.teacherName;
    studentCnt.value = data.studentCnt;
    loading.value = false;
  });
};

// 搜索
const handleQuery = () => {
  queryParams.pageNumber = 1;
  fetchStudents();
};

// 重置搜索
const handleResetQuery = () => {
  queryFormRef.value?.resetFields();
  dateRange.value = [];
  queryParams.pageNumber = 1;
  fetchStudents();
};

// 表格选择变化
const handleSelectionChange = (selection: StudentVO[]) => {
  selectIds.value = selection.map((item) => item.id);
};

// 新增学生
const handleAddStudent = () => {
  AccountAPI.getTree(false).then((data) => {
    TeachingAPI.getCourseStudent(courseId.value).then(studentIds => {
      userOptions.value = (data || [])
        .filter((item: any) => item.children && item.children.length)
        .map((item: any) => {
          return {
            ...item,
            children: item.children.filter((child: any) => !studentIds.includes(child.value))
          };
        })
        .filter((item: any) => item.children && item.children.length);

      selectedStudents.value = [];
      dialogVisible.value = true;
    })
  });
};

// 确认添加学生
const handleConfirmAddStudents = () => {
  if (!selectedStudents.value || selectedStudents.value.length === 0) {
    ElMessage.warning('请选择学生');
    return;
  }
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在添加学生...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  TeachingAPI.addCourseStudent({
    id: courseId.value,
    userIds: selectedStudents.value
  }).then(() => {
    ElMessage.success('添加学生成功');
    dialogVisible.value = false;
    fetchStudents();
    const tags: TagView = {
      name: "Teaching"
    };
    tagsViewStore.delCachedView(tags);
  }).finally(() => {
    loadingInstance.close();
  });
};

// 删除学生
const handleBatchDeleteStudents = (id?: string) => {
  const ids = id ? [id] : selectIds.value;
  if (ids.length === 0) {
    ElMessage.warning("请选择要删除的学生");
    return;
  }

  ElMessageBox.confirm(`此操作将永久删除该课程下所有学生的实验数据，请确认是否继续?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    TeachingAPI.deleteCourseStudent(ids).then(() => {
      ElMessage.success("删除成功");
      fetchStudents();
      const tags: TagView = {
        name: "Teaching"
      };
      tagsViewStore.delCachedView(tags);
    });
  });
};

// 页面加载时获取数据
onMounted(() => {
  fetchStudents();
  TeachingAPI.getCourseDetail(courseId.value).then(data => {
    course.value = data;
  });
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h3 {
    margin: 0;
  }

  p {
    margin: 5px 0 0;
    font-size: 14px;
    color: #666;
  }
}

.search-container {
  display: flex;
  justify-content: space-between;
  padding: 18px 16px 0;
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;

  .search-buttons {
    min-width: 250px;
    margin-left: auto;
  }
}

.operation-buttons {
  margin-bottom: 20px;
}

.preview-row {
  margin-bottom: 10px;
}
.preview-title {
  font-size: 14px;
  margin-right: 0px;
}
.preview-value {
  font-size: 14px;
  color: #666;
  margin-right: 30px;
}

.data-table {
  .data-table__content {
    margin-bottom: 16px;
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 12px;
  }

  .search-container {
    padding: 16px;

    :deep(.el-form--inline) {
      .el-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
