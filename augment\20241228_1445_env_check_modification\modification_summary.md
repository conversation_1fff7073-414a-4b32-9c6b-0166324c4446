# 01__env_check.sh 脚本修改说明

## 修改时间
2024年12月28日 14:45

## 修改目标
调整 `01__env_check.sh` 文件，实现用户输入LIC并发数、每个节点分配CPU核心数、内存、磁盘空间大小，然后根据这些参数检查当前服务器资源是否满足要求。

## 主要功能增强

### 1. 用户输入收集
- **LIC并发数**: 支持1-1000范围的整数输入
- **CPU核心数**: 支持1-64范围的整数输入，表示每个节点分配的CPU核心数
- **内存大小**: 支持1-512GB范围的整数输入，表示每个节点分配的内存
- **磁盘空间**: 支持1-10240GB范围的整数输入，表示每个节点分配的磁盘空间

### 2. 输入验证
- 所有输入都有严格的数值范围验证
- 使用正则表达式确保输入为正整数
- 输入错误时会提示用户重新输入

### 3. 资源需求计算
- **总CPU需求** = LIC并发数 × 每节点CPU核心数
- **总内存需求** = LIC并发数 × 每节点内存大小
- **总磁盘需求** = LIC并发数 × 每节点磁盘空间

### 4. 系统资源检查
- **CPU检查**: 使用 `nproc` 命令获取系统可用CPU核心数
- **内存检查**: 使用 `free -m` 命令获取系统可用内存
- **磁盘检查**: 使用 `df /` 命令获取根分区可用磁盘空间

### 5. 配置文件保存
生成 `/tmp/nse-resource-config.env` 配置文件，包含以下变量：
```bash
export LIC_CONCURRENCY="用户输入的并发数"
export CPU_CORES_PER_NODE="每节点CPU核心数"
export MEMORY_PER_NODE_GB="每节点内存GB"
export DISK_PER_NODE_GB="每节点磁盘空间GB"
export TOTAL_CPU_REQUIRED="计算的总CPU需求"
export TOTAL_MEMORY_REQUIRED_GB="计算的总内存需求"
export TOTAL_DISK_REQUIRED_GB="计算的总磁盘需求"
```

### 6. 详细报告输出
脚本会输出完整的资源检查报告，包括：
- 用户配置信息
- 计算的总资源需求
- 系统可用资源
- 检查结果状态

## 保留的原有功能
- 操作系统检查（仅支持Ubuntu）
- 系统架构检查（仅支持x86_64）
- 端口占用检查（DB_PORT, NSE_MGR_PORT, NSE_CLOUD_PORT, GNS3_WEB_PORT, GNS3_SERVER_PORT）
- 离线包目录检查
- 部署状态保存

## 错误处理
- 资源不足时会调用 `error_exit` 函数终止脚本执行
- 提供详细的错误信息，说明需要的资源和实际可用资源

## 使用示例
```bash
# 运行脚本
bash 01__env_check.sh

# 用户输入示例：
# LIC并发数: 10
# 每节点CPU核心数: 2
# 每节点内存: 4GB
# 每节点磁盘空间: 20GB

# 计算结果：
# 总CPU需求: 20核心
# 总内存需求: 40GB
# 总磁盘需求: 200GB
```

## 文件位置
`c:\__ruijie_work_space\nse\nse-service\nse-mgr\installer\script\subs\01__env_check.sh`

## 修改完成状态
✅ 已完成所有功能实现
✅ 保留了原有的系统检查功能
✅ 添加了用户输入和资源计算功能
✅ 实现了详细的资源检查和报告输出
✅ 配置文件保存功能正常工作