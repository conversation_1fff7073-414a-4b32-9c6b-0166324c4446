# -*- coding: utf-8 -*-
#
# Copyright (C) 2015 GNS3 Technologies Inc.
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

import sys
import json
import urllib
import asyncio
import aiohttp
import traceback
import jsonschema
import jsonschema.exceptions

from ..compute.error import NodeError, ImageMissingError
from ..controller.controller_error import ControllerError
from ..ubridge.ubridge_error import UbridgeError
from ..controller.gns3vm.gns3_vm_error import GNS3VMError
from .response import Response
from ..crash_report import CrashReport
from ..config import Config
from ..nse.nse_env import nse_env


import logging
log = logging.getLogger(__name__)


async def parse_request(request, input_schema, raw):
    """Parse body of request and raise HTTP errors in case of problems"""

    request.json = {}
    if not raw:
        body = await request.read()
        if body:
            try:
                request.json = json.loads(body.decode('utf-8'))
            except ValueError as e:
                request.json = {"malformed_json": body.decode('utf-8')}
                raise aiohttp.web.HTTPBadRequest(text="Invalid JSON {}".format(e))

    # Parse the query string
    if len(request.query_string) > 0:
        for (k, v) in urllib.parse.parse_qs(request.query_string).items():
            request.json[k] = v[0]

    if input_schema:
        try:
            jsonschema.validate(request.json, input_schema)
        except jsonschema.ValidationError as e:
            message = "JSON schema error with API request '{}' and JSON data '{}': {}".format(request.path_qs,
                                                                                              request.json,
                                                                                              e.message)
            log.error(message)
            log.debug("Input schema: {}".format(json.dumps(input_schema)))
            raise aiohttp.web.HTTPBadRequest(text=message)

    return request


class Route(object):

    """ Decorator adding:
        * json schema verification
        * routing inside handlers
        * documentation information about endpoints
    """

    _routes = []
    _documentation = {}

    _node_locks = {}

    @classmethod
    def get(cls, path, *args, **kw):
        return cls._route('GET', path, *args, **kw)

    @classmethod
    def post(cls, path, *args, **kw):
        return cls._route('POST', path, *args, **kw)

    @classmethod
    def put(cls, path, *args, **kw):
        return cls._route('PUT', path, *args, **kw)

    @classmethod
    def delete(cls, path, *args, **kw):
        return cls._route('DELETE', path, *args, **kw)

    @classmethod
    def authenticate(cls, request, route, server_config):
        """
        Ask user for authentication

        :returns: Response if you need to auth the user otherwise None
        """

        # FIXME: ugly exception to not require authentication for websocket consoles
        # TODO: 这里默认都要鉴权，但是websocket console 不需要鉴权
        # if not server_config.getboolean("auth", False) or request.path.endswith("console/ws"):
        # /static/web-ui/server/577138981936553985/project/8a44521c-d088-67d7-8a44-521cd08867d7
        host_id = str(nse_env.host_id)
        white_url_list = "/server/" + host_id + "/project"
        if request.path.endswith("console/ws") or "subscribe/host" in request.path or white_url_list in request.path:
            return None


        # FIXME: 这里全部不从配置文件拿，保险起见  全部改为从环境变量拿
        # user = server_config.get("user", "").strip()
        # password = server_config.get("password", "").strip()
        user = nse_env.basic_user
        password = nse_env.basic_secret
        if user and "AUTHORIZATION" in request.headers:
            if request.headers["AUTHORIZATION"] == aiohttp.helpers.BasicAuth(user, password, "utf-8").encode():
                return None

        if not user:
            log.error("HTTP authentication is enabled but no username is configured")
        else:
            log.error("Invalid HTTP authentication for username '{}'".format(user))

        response = Response(request=request, route=route)
        response.set_status(401)
        response.headers["WWW-Authenticate"] = 'Basic realm="GNS3 server"'
        # Force close the keep alive. Work around a Qt issue where Qt timeout instead of handling the 401
        # this happen only for the first query send by the client.
        response.force_close()
        return response

    @classmethod
    def _route(cls, method, path, *args, **kw):
        # This block is executed only the first time
        output_schema = kw.get("output", {})
        input_schema = kw.get("input", {})
        api_version = kw.get("api_version", 2)
        raw = kw.get("raw", False)

        def register(func):
            # Add the type of server to the route
            if "controller" in func.__module__:
                route = "/v{version}{path}".format(path=path, version=api_version)
            elif "compute" in func.__module__:
                route = "/v{version}/compute{path}".format(path=path, version=api_version)
            else:
                route = path

            # Compute metadata for the documentation
            if api_version:
                handler = func.__module__.replace("_handler", "").replace("gns3server.handlers.api.", "")
                cls._documentation.setdefault(handler, {})
                cls._documentation[handler].setdefault(route, {"api_version": api_version,
                                                               "controller": kw.get("controller", False),
                                                               "methods": []})

                cls._documentation[handler][route]["methods"].append({
                    "method": method,
                    "status_codes": kw.get("status_codes", {200: "OK"}),
                    "parameters": kw.get("parameters", {}),
                    "output_schema": output_schema,
                    "input_schema": input_schema,
                    "description": kw.get("description", ""),
                })

            async def control_schema(request):
                # This block is executed at each method call
                server_config = Config.instance().get_section_config("Server")

                # Authenticate
                #  fixme  这里鉴权暂时先屏蔽
                # response = cls.authenticate(request, route, server_config)
                # if response is not None:
                #     return response

                try:
                    # Non API call
                    if api_version is None or raw is True:
                        response = Response(request=request, route=route, output_schema=output_schema)

                        request = await parse_request(request, None, raw)
                        if asyncio.iscoroutinefunction(func):
                            await func(request, response)
                        else:
                            func(request, response)
                        return response

                    # API call
                    request = await parse_request(request, input_schema, raw)
                    record_file = server_config.get("record")
                    if record_file:
                        try:
                            with open(record_file, "a", encoding="utf-8") as f:
                                f.write("curl -X {} 'http://{}{}' -d '{}'".format(request.method, request.host, request.path_qs, json.dumps(request.json)))
                                f.write("\n")
                        except OSError as e:
                            log.warning("Could not write to the record file {}: {}".format(record_file, e))
                    response = Response(request=request, route=route, output_schema=output_schema)
                    if asyncio.iscoroutinefunction(func):
                        await func(request, response)
                    else:
                        func(request, response)
                    return response

                except (NodeError, ControllerError, UbridgeError, GNS3VMError, ImageMissingError) as e:
                    if isinstance(e, NodeError):
                        e.render(response)
                    else:
                        response.set_status(e.code)
                        response.json({"message": str(e), "status": e.code})
                    return response
                except jsonschema.exceptions.ValidationError as e:
                    response.set_status(400)
                    response.json({"message": "JSON schema error: {}".format(e.message), "status": 400})
                    return response
                except aiohttp.web.HTTPException:
                    raise
                except Exception as e:
                    try:
                        # Log the traceback as debug
                        log.debug("An exception occurred while handling the request: {}\n{}".format(str(e), traceback.format_exc()))
                        # Report the crash
                        CrashReport.instance().capture_exception(sys.exc_info())
                    except Exception:
                        # Ignore any errors occurring while capturing the exception
                        pass
                    # Return a generic 500 error
                    response.set_status(500)
                    response.json({"message": "Internal server error", "status": 500})
                    return response

            cls._routes.append((method, route, control_schema))
            return control_schema
        return register

    @classmethod
    def get_routes(cls):
        """
        Returns all the routes registered with the Route decorator
        """
        return cls._routes
