13:57:11.545 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
13:57:11.635 [main] INFO  c.r.n.c.l.NseCloudApplication - [logStarting,53] - [System|系统] - Starting NseCloudApplication using Java 21.0.6 with PID 18272 (C:\__ruijie_work_space\nse\nse-service\nse-cloud\nse-cloud-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
13:57:11.636 [main] INFO  c.r.n.c.l.NseCloudApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
13:57:13.750 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8081"]
13:57:13.754 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
13:57:13.755 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
13:57:13.832 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
13:57:35.666 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=./workspace/ehcache-storage-cloud, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
13:57:35.687 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
13:57:35.872 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
13:57:35.988 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
13:57:36.077 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
13:57:36.306 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
13:57:36.434 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
13:57:36.438 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
13:57:36.524 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
13:57:36.526 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
13:57:36.527 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
13:57:36.661 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
13:57:38.432 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@f6c03cb
13:57:38.616 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8081"]
13:57:38.651 [main] INFO  c.r.n.c.l.NseCloudApplication - [logStarted,59] - [System|系统] - Started NseCloudApplication in 27.705 seconds (process running for 28.495)
13:57:38.657 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
13:57:38.657 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
13:57:38.657 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: ./workspace/ehcache-storage-cloud
13:57:38.658 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
13:57:38.658 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
13:57:38.658 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
13:57:38.658 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
14:01:43.794 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:01:43.997 [http-nio-8081-exec-1] INFO  c.r.n.c.s.s.LoginService - [loginByAccount,39] - [System|系统] - 用户登录: admin
14:01:44.080 [http-nio-8081-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
14:01:44.384 [http-nio-8081-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2fa4b11f
14:01:44.386 [http-nio-8081-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
14:01:44.783 [http-nio-8081-exec-1] INFO  c.r.n.c.s.s.LoginService - [loginByAccount,82] - [System|系统] - 用户登录成功: LoginDto.Resp(accessToken=eyJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************.YYr2sLdKdsj8xOcTM6KGWbcJADUbmXl1mhdqcXzyUok, refreshToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************.xOGu8-wQq56Cu7xAV_tF1ZsDJ728zHz2rwh4lVbdNqY, tokenType=Bearer, expiresIn=86400, userInfo=LoginDto.Resp.UserInfo(userId=1945376569312702465, username=admin))
14:05:11.177 [http-nio-8081-exec-4] INFO  c.r.n.c.l.s.LicenseFileBuilderService - [buildLicenseFile,68] - [1945376569312702465|系统] - 开始生成License文件，License ID: 1960946734322507778
14:05:12.002 [http-nio-8081-exec-4] INFO  c.r.n.c.l.s.LicenseFileBuilderService - [saveToFile,271] - [1945376569312702465|系统] - License文件已保存: C:\__ruijie_work_space\nse\nse-service\.\workspace\license-output\LIC-NSE-License-05506EPUUuybApjLNUDlvotvy_20250828_140511.lic
14:05:12.003 [http-nio-8081-exec-4] INFO  c.r.n.c.l.s.LicenseFileBuilderService - [buildLicenseFile,92] - [1945376569312702465|系统] - License文件生成成功: C:\__ruijie_work_space\nse\nse-service\.\workspace\license-output\LIC-NSE-License-05506EPUUuybApjLNUDlvotvy_20250828_140511.lic
