package com.ruijie.nse.mgr.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 资源释放事件数据
 * 用于携带资源释放相关的详细信息
 *
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceReleaseEventData {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户类型
     */
    private String userType;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 释放原因
     */
    private String releaseReason;
    
    /**
     * 释放类型：MANUAL(手动), AUTO(自动), FORCE(强制), TIMEOUT(超时)
     */
    private String releaseType;
    
    /**
     * Python3服务器信息
     */
    private Python3ServerInfo serverInfo;
    
    /**
     * 项目信息列表
     */
    private List<ProjectInfo> projects;
    
    /**
     * 资源使用统计
     */
    private ResourceUsageStats resourceUsage;
    
    /**
     * 释放时间
     */
    private LocalDateTime releaseTime;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> extensions;
    
    /**
     * Python3服务器信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Python3ServerInfo {
        private String hostId;
        private String serverIp;
        private Integer serverPort;
        private Long pid;
        private String storagePath;
        private Double maxMemory;
        private Double currentMemory;
        private Long cpuCore;
        private LocalDateTime startTime;
        private Long runningDuration; // 运行时长（秒）
    }
    
    /**
     * 项目信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProjectInfo {
        private String projectId;
        private String projectName;
        private String projectPath;
        private String projectType;
        private Long projectSize; // 项目大小（字节）
        private LocalDateTime lastModified;
        private Boolean isActive; // 是否为活跃项目
        private List<String> relatedFiles; // 相关文件列表
    }
    
    /**
     * 资源使用统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResourceUsageStats {
        private Double memoryUsed; // 已使用内存（GB）
        private Double diskUsed; // 已使用磁盘（GB）
        private Double cpuUsage; // CPU使用率
        private Long sessionDuration; // 会话时长（秒）
        private Integer projectCount; // 项目数量
        private Long totalFileSize; // 总文件大小（字节）
        private LocalDateTime lastActivity; // 最后活动时间
    }
    
    /**
     * 创建学生资源释放事件数据
     */
    public static ResourceReleaseEventData createStudentReleaseEvent(String userId, String userType, String username,
                                                                   String releaseReason, String releaseType) {
        return ResourceReleaseEventData.builder()
                .userId(userId)
                .username(username)
                .userType(userType)
                .releaseReason(releaseReason)
                .releaseType(releaseType)
                .releaseTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建项目释放事件数据
     */
    public static ResourceReleaseEventData createProjectReleaseEvent(String userId, String username,
                                                                   List<ProjectInfo> projects, String releaseReason) {
        return ResourceReleaseEventData.builder()
                .userId(userId)
                .username(username)
                .projects(projects)
                .releaseReason(releaseReason)
                .releaseType("PROJECT_CLEANUP")
                .releaseTime(LocalDateTime.now())
                .build();
    }
}
