<template>
  <div class="login-queue">
    <div class="login-queue-dialog">
      <div class="login-queue-container" v-loading.fullscreen.lock="fullscreenLoading">
        <div class="user-info" :style="{'margin-top': queueStatus === 'queuing' ? '' : '20px'}">
          <img v-if="queueStatus === 'queuing'" class="avatar-container" src="@/assets/images/user-group.png" alt="用户头像">
          <img v-else-if="queueStatus === 'success'" class="avatar-container" src="@/assets/images/queue-success.png" alt="用户头像">
          <img v-else-if="queueStatus === 'timeout'" class="avatar-container" src="@/assets/images/queue-timeout.png" alt="用户头像">
          <div class="info-text">
            <div>
              姓名:
              <span>{{ queueInfo.name }}</span>
            </div>
            <div>
              学号:
              <span>{{ queueInfo.account }}</span>
            </div>
          </div>
        </div>

        <!-- 排队中 -->
        <template v-if="queueStatus === 'queuing'">
          <div class="queue-status">
            <div class="queue-status-text">系统登录排队中</div>
            <p style="color: #666;">当前系统访问人数比较多，请耐心等待！</p>
          </div>

          <div class="queue-info">
            <div class="position">
              <span>您的排队位置</span>
              <strong>第 {{ queueInfo.waitingCount }} 位</strong>
            </div>
            <el-progress :text-inside="true" :stroke-width="16" :percentage="queueProgress" />
            <p>前方还有 {{ queueInfo.waitingCount - 1 }} 位用户等待登录</p>
          </div>

          <div class="estimated-time">
            <span>预计等待时间</span>
            <div class="time">{{ formatMillisecondsToTime(estimatedTime) }}</div>
            <small>若有用户退出，排队可能提前，请及时查看排队状态</small>
          </div>

          <div class="actual-time">
            <div>
              <span>已等待时间</span>
              <strong>{{ formatMillisecondsToTime(waitTimeMs) }}</strong>
            </div>
            <div>
              <span>总排队人数</span>
              <strong>{{ queueInfo.totalWaitingCount }}</strong>
            </div>
          </div>

          <div class="expected-login-time">
            <div>预计登录时间</div>
            <div class="expected-login-content">
              <div class="blue-dot"></div>
              <div>{{ formatTimestamp(queueInfo.expectedLoginTime) }}</div>
            </div>
            <small>排队成功后需在5分钟内登录，否则需重新排队！每名用户登录后有2小时使用时长限制！</small>
          </div>

          <div class="buttons">
            <el-button icon="Close" @click="cancelQueue">取消排队</el-button>
            <el-button icon="Refresh" type="primary" @click="refreshQueue">刷新排队状态</el-button>
          </div>
        </template>

        <!-- 排队成功 -->
        <template v-else-if="queueStatus === 'success'">
          <div class="queue-status">
            <div class="queue-status-text">排队成功</div>
            <p style="color: #666;">您排队成功，请尽快进入系统！</p>
          </div>
          <div class="estimated-time">
            <span>剩余时间</span>
            <div class="time">{{ formatTime(loginCountdown) }}</div>
          </div>
          <div style="color: #44a0ff;margin-bottom: 30px;">
            排队成功后5分钟内未进入将自动退出重新排队！
          </div>
          <div class="buttons" style="margin-bottom: 40px;">
            <el-button icon="Close" @click="cancelQueue">取消登录</el-button>
            <el-button type="primary" @click="proceedToLogin">
              <img style="width: 14px;height: 14px;margin-right: 6px;" src="@/assets/images/play.png" alt="" />
              立即进入系统
            </el-button>
          </div>
        </template>

        <!-- 排队超时 -->
        <template v-else-if="queueStatus === 'timeout'">
          <div class="queue-status">
            <div class="queue-status-text">登录超时</div>
            <p style="color: #666;">登录超时，请重新排队登录！</p>
          </div>
          <div class="estimated-time">
            <span>剩余时间</span>
            <div class="time">{{ '00:00' }}</div>
          </div>
          <div style="color: #44a0ff;margin-bottom: 30px;">
            排队成功后5分钟内未进入将自动退出重新排队！
          </div>
          <div class="buttons">
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="cancel">确定</el-button>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import "element-plus/theme-chalk/el-message-box.css";
import { MessageWebSocket } from "@/utils/websocket";
import AuthAPI, { type QueueInfo } from "@/api/auth.api";

const props = defineProps<{
  userId: string;
}>();
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'login'): void;
}>();

const fullscreenLoading = ref(false);
// 默认为排队中状态
const queueStatus = ref('queuing');
const dialogVisible = ref(true);
const queueInfo = ref<QueueInfo>({
  id: '',
  name: '',
  account: '',
  doLoginTime: 0,
  waitingCount: 0,
  totalWaitingCount: 0,
  expectedLoginTime: 0,
  queueSuccessful: false,
});
const waitTimeMs = ref();
const estimatedTime = ref(0);
const queueProgress = ref(0);
const loginCountdown = ref(0);
const wsInstance = ref<MessageWebSocket | null>(null);
let updateTimer: any = null;
const updateQueueInfo = () => {
  if (!queueInfo.value.queueSuccessful && queueInfo.value.doLoginTime) {
    waitTimeMs.value = Date.now() - queueInfo.value.doLoginTime;

    if (queueInfo.value.expectedLoginTime) {
      estimatedTime.value = Math.max(queueInfo.value.expectedLoginTime - Date.now(), 0);
      const total = queueInfo.value.expectedLoginTime - queueInfo.value.doLoginTime;

      const progress = (1 - (estimatedTime.value / total)) * 100;
      queueProgress.value = parseFloat(progress.toFixed(2));

      if (estimatedTime.value === 0) {
        refreshQueue();
      }
    }
    return;
  }

  if (queueInfo.value.queueSuccessful) {
    loginCountdown.value = Math.max((queueInfo.value.enterQueueTime + 5 * 60 * 1000) - Date.now(), 0);
    if (loginCountdown.value === 0) {
      showQueueTimeout();
    }
  }

};

function handleWsMessage(data: any) {
  console.log("LoginQueue ws message", data);
  if (data.event === 'REFRESH_QUEUE') {
    refreshQueue()
  }
}

onMounted(() => {
  if (props.userId) {
    const wsUrl = `${import.meta.env.VITE_APP_WS_URL}${props.userId}`;
    wsInstance.value = new MessageWebSocket(wsUrl);
    wsInstance.value.onMessage(handleWsMessage);
    wsInstance.value.connect();
    refreshQueue();
  }

  window.addEventListener('beforeunload', function(event) {
    // 由于测试后发现 navigationType 在关闭与页面刷新时都为1，因此使用其他方式判断是否为刷新操作
    const isPageHide = 'hide' in event;
    const isRefresh = performance.navigation.type === performance.navigation.TYPE_RELOAD;

    if (wsInstance.value) {
      // 如果是刷新操作，发送刷新标识
      if (isRefresh) {
        wsInstance.value.send("refresh");
      } else {
        // 其他情况（包括关闭标签页）发送关闭标识
        wsInstance.value.send("close");
      }
      wsInstance.value.close();
    }

    // 只有在非刷新情况下才显示确认对话框
    if (!isRefresh) {
      event.preventDefault();
      event.returnValue = '您确定要离开吗？您的更改可能尚未保存。';
    }
  });
});

onBeforeUnmount(() => {
  if (updateTimer) {
    clearInterval(updateTimer);
  }
  if (wsInstance.value) {
    wsInstance.value.close();
  }
});

function formatTimestamp(timestamp: number): string {
  return formatDate(new Date(timestamp));
}

function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function formatMillisecondsToTime(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  const formattedSeconds = String(seconds % 60).padStart(2, '0');
  const formattedMinutes = String(minutes % 60).padStart(2, '0');
  const formattedHours = String(hours).padStart(2, '0');

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
}

function formatTime(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

function cancelQueue() {
  const tip = queueStatus.value === 'success' ? '登录' : '排队';
  ElMessageBox.confirm(`确定要取消${tip}吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    AuthAPI.cancelQueuing(props.userId)
      .then(res => {
        cancel();
        ElMessage.success(`已取消${tip}`);
      })
  });
}

function cancel() {
  if (updateTimer) {
    clearInterval(updateTimer);
  }
  emit('close');
}

// 刷新排队状态
function refreshQueue() {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  AuthAPI.getQueueInfo(props.userId)
    .then(data => {
      queueInfo.value = data;
      console.log("LoginQueue refreshQueue", data);
      updateQueueInfo();
      if (!updateTimer) {
        updateTimer = setInterval(updateQueueInfo, 1000);
      }
      if (queueInfo.value.queueSuccessful) {
        showQueueSuccess();
      }
    })
    .finally(() => { loading.close() });
}

// 切换到排队成功状态
function showQueueSuccess() {
  queueStatus.value = 'success';
}

// 切换到排队超时状态
function showQueueTimeout() {
  queueStatus.value = 'timeout';
  AuthAPI.cancelQueuing(props.userId);
}

// 排队成功时的登录操作
function proceedToLogin() {
  ElMessage.success('正在跳转到系统页面...');
  emit('login');
}

</script>
<style scoped>
p {
  margin: 15px 0;
}
.login-queue {
  position: fixed;
  height: 100vh;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  overflow: auto;
}
.login-queue-dialog {
  border-radius: 8px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, .12);
  width: 80%;
  margin: 0 auto;
  background-color: #fff;
  min-width: 600px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.login-queue-container {
  padding: 20px;
  width: 400px;
  margin: 0 auto;
  border: 1px solid #fff;
  border-radius: 4px;
  background-color: #fff;
  color: #000000;
}

.user-info {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-bottom: 15px;
}

.avatar-container {
  width: 80px;
  height: 80px;
}

.info-text {
  margin-left: 10px;
  padding-top: 10px;
}
.info-text div {
  margin-bottom: 5px;
}
.info-text span {
  color: #666;
}

.queue-status {
  text-align: center;
  margin-bottom: 15px;
}
.queue-status-text {
  font-size: 25px;
  font-weight: 600;
}

.queue-info {
  margin-bottom: 15px;
}
.queue-info p {
  color: #666;
  font-size: 14px;
}

.position {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.position strong {
  color: #47a1fb;
}

.estimated-time {
  background: #ecf5fd;
  color: #44a0ff;
  padding: 15px 0 5px 0;
  border-radius: 8px;
}
.estimated-time .time {
  font-size: 40px;
  font-weight: 600;
  color: #44a0ff;
}

.estimated-time {
  text-align: center;
  margin-bottom: 15px;
}

.expected-login-time {
  text-align: left;
  margin-bottom: 15px;
  color: #000000;
}

.expected-login-time small {
  color: #666;
}

.expected-login-content {
  display: flex;
  align-items: center;
  margin: 5px 0;
}
.blue-dot {
  width: 15px;
  height: 15px;
  background: #44a0fb;
  border-radius: 50%;
  margin-right: 8px;
}

.actual-time {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}
.actual-time div {
  width: 45%;
  text-align: center;
  display: flex;
  flex-direction: column;
  color: #666;
  background: #fafaf8;
  border-radius: 8px;
  padding: 15px 0;
}
.actual-time span {
  margin-bottom: 15px;
}

.buttons {
  display: flex;
  justify-content: space-between;
}
</style>
