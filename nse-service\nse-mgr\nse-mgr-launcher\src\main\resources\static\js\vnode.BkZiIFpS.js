import{ab as E,bS as T,P as e,bU as s}from"./index.Ckm1SagX.js";var S=(E=>(E[E.TEXT=1]="TEXT",E[E.CLASS=2]="CLASS",E[E.STYLE=4]="STYLE",E[E.PROPS=8]="PROPS",E[E.FULL_PROPS=16]="FULL_PROPS",E[E.HYDRATE_EVENTS=32]="HYDRATE_EVENTS",E[E.STABLE_FRAGMENT=64]="STABLE_FRAGMENT",E[E.KEYED_FRAGMENT=128]="KEYED_FRAGMENT",E[E.UNKEYED_FRAGMENT=256]="UNKEYED_FRAGMENT",E[E.NEED_PATCH=512]="NEED_PATCH",E[E.DYNAMIC_SLOTS=1024]="DYNAMIC_SLOTS",E[E.HOISTED=-1]="HOISTED",E[E.BAIL=-2]="BAIL",E))(S||{});const r=E=>{if(!T(E))return{};const S=E.props||{},r=(T(E.type)?E.type.props:void 0)||{},A={};return Object.keys(r).forEach(E=>{e(r[E],"default")&&(A[E]=r[E].default)}),Object.keys(S).forEach(E=>{A[s(E)]=S[E]}),A},A=e=>{const s=E(e)?e:[e],S=[];return s.forEach(e=>{var s;E(e)?S.push(...A(e)):T(e)&&(null==(s=e.component)?void 0:s.subTree)?S.push(e,...A(e.component.subTree)):T(e)&&E(e.children)?S.push(...A(e.children)):T(e)&&2===e.shapeFlag?S.push(...A(e.type())):S.push(e)}),S};export{S as P,A as f,r as g};
