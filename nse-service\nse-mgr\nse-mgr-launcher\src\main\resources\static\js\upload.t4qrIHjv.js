import{d as e,ax as t,az as l,r as a,I as s,ap as i,g as u,f as n,m as r,k as o,i as p,aC as d,w as m,a1 as c,F as f,C as y,E as v,j as _,aP as g,d0 as h,e as j,h as x,o as b,Q as w}from"./index.Ckm1SagX.js";import{a as C,E as F}from"./form-item.CUMILu98.js";import{E as V}from"./progress.ChnKapv7.js";import{E as k}from"./upload.CnbKCtkP.js";/* empty css               */import{F as S}from"./file.api.td1NUiAp.js";import{E as z}from"./index.CbYeWxT8.js";import{_ as M}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{E,a as I}from"./image-viewer.ieueOHH5.js";/* empty css             */import{E as O}from"./index.CMOQuMWt.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./castArray.Chmjnshw.js";import"./aria.C1IWO_Rd.js";import"./_baseClone.ByRc02qR.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./isEqual.CZKKciWh.js";import"./index.BRUQ9gWw.js";import"./focus-trap.Bd_uzvDY.js";import"./index.C0OsJ5su.js";import"./debounce.YgIwzEIs.js";import"./position.D3azAgd1.js";import"./index.DJHzyRe5.js";import"./scroll.XdyICIdv.js";const U={class:"el-upload-list__item-info"},N=["onClick"],L={class:"el-upload-list__item-file-name"},P=["onClick"],W=M(e({__name:"FileUpload",props:t({data:{type:Object,default:()=>({})},name:{type:String,default:"file"},limit:{type:Number,default:10},maxFileSize:{type:Number,default:10},accept:{type:String,default:"*"},uploadBtnText:{type:String,default:"上传文件"},style:{type:Object,default:()=>({width:"300px"})}},{modelValue:{type:[Array],required:!0,default:()=>[]},modelModifiers:{}}),emits:["update:modelValue"],setup(e){const t=e,h=l(e,"modelValue"),j=a([]),x=a(!1),b=a(0);function w(e){return!(e.size>1024*t.maxFileSize*1024)||(g.warning("上传文件不能大于"+t.maxFileSize+"M"),!1)}function C(e){return new Promise((l,a)=>{const s=e.file,i=new FormData;i.append(t.name,s),Object.keys(t.data).forEach(e=>{i.append(e,t.data[e])}),S.upload(i).then(e=>{l(e)}).catch(e=>{a(e)})})}s(h,e=>{j.value=e.map(e=>{var t;return{name:e.name?e.name:null==(t=e.url)?void 0:t.substring(e.url.lastIndexOf("/")+1),url:e.url,status:"success",uid:Date.now()<<13|Math.floor(8192*Math.random())}})},{immediate:!0});const F=e=>{b.value=e.percent},M=(e,t,l)=>{if(g.success("上传成功"),l.every(e=>"success"===e.status||"fail"===e.status)){const e=[];l.map(t=>{if("success"===t.status){const l=t.response;l&&e.push({name:l.name,url:l.url})}else j.value.splice(j.value.findIndex(e=>e.uid===t.uid),1)}),e.length>0&&(h.value=[...h.value,...e])}},E=e=>{g.error("上传失败")};return(e,l)=>{const a=z,s=i("Document"),g=v,I=i("Close"),O=k,W=V;return n(),u("div",null,[r(O,{"file-list":p(j),"onUpdate:fileList":l[0]||(l[0]=e=>d(j)?j.value=e:null),style:o(t.style),"before-upload":w,"http-request":C,"on-progress":F,"on-success":M,"on-error":E,accept:t.accept,limit:t.limit,multiple:""},{file:m(({file:e})=>[y("div",U,[y("a",{class:"el-upload-list__item-name",onClick:t=>function(e){const{url:t,name:l}=e;t&&S.download(t,l)}(e)},[r(g,null,{default:m(()=>[r(s)]),_:1}),y("span",L,f(e.name),1),y("span",{class:"el-icon--close",onClick:_(t=>{return l=e.url,void S.delete(l).then(()=>{h.value=h.value.filter(e=>e.url!==l)});var l},["stop"])},[r(g,null,{default:m(()=>[r(I)]),_:1})],8,P)],8,N)])]),default:m(()=>[r(a,{type:"primary",disabled:p(j).length>=t.limit},{default:m(()=>[c(f(t.uploadBtnText),1)]),_:1},8,["disabled"])]),_:1},8,["file-list","style","accept","limit"]),r(W,{style:o({display:p(x)?"inline-flex":"none",width:"100%"}),percentage:p(b)},null,8,["style","percentage"])])}}}),[["__scopeId","data-v-c43823e0"]]),q=M(e({__name:"SingleImageUpload",props:t({data:{type:Object,default:()=>({})},name:{type:String,default:"file"},maxFileSize:{type:Number,default:10},accept:{type:String,default:"image/*"},style:{type:Object,default:()=>({width:"150px",height:"150px"})}},{modelValue:{type:String,default:()=>""},modelModifiers:{}}),emits:["update:modelValue"],setup(e){h(e=>({"8f35162e":t.style.width,"3a7a2e64":t.style.height}));const t=e,a=l(e,"modelValue");function s(e){return t.accept.split(",").map(e=>e.trim()).some(t=>"image/*"===t?e.type.startsWith("image/"):t.startsWith(".")?e.name.toLowerCase().endsWith(t):e.type===t)?!(e.size>1024*t.maxFileSize*1024)||(g.warning("上传图片不能大于"+t.maxFileSize+"M"),!1):(g.warning(`上传文件的格式不正确，仅支持：${t.accept}`),!1)}function u(e){return new Promise((l,a)=>{const s=e.file,i=new FormData;i.append(t.name,s),Object.keys(t.data).forEach(e=>{i.append(e,t.data[e])}),S.upload(i).then(e=>{l(e)}).catch(e=>{a(e)})})}function o(){a.value=""}const p=e=>{g.success("上传成功"),a.value=e.url},d=e=>{g.error("上传失败: "+e.message)};return(e,l)=>{const c=E,f=i("CircleCloseFilled"),y=v,g=i("Plus"),h=k;return n(),j(h,{modelValue:a.value,"onUpdate:modelValue":l[0]||(l[0]=e=>a.value=e),class:"single-upload","list-type":"picture-card","show-file-list":!1,accept:t.accept,"before-upload":s,"http-request":u,"on-success":p,"on-error":d},{default:m(()=>[a.value?(n(),j(c,{key:0,src:a.value},null,8,["src"])):x("",!0),a.value?(n(),j(y,{key:1,class:"single-upload__delete-btn",onClick:_(o,["stop"])},{default:m(()=>[r(f)]),_:1})):(n(),j(y,{key:2,class:"single-upload__add-btn"},{default:m(()=>[r(g)]),_:1}))]),_:1},8,["modelValue","accept"])}}}),[["__scopeId","data-v-888ce7f5"]]),D={style:{width:"100%"}},A=["src"],H={class:"el-upload-list__item-actions"},Q=["onClick"],Z=["onClick"],B=e({__name:"MultiImageUpload",props:t({data:{type:Object,default:()=>({})},name:{type:String,default:"file"},limit:{type:Number,default:10},maxFileSize:{type:Number,default:10},accept:{type:String,default:"image/*"}},{modelValue:{type:[Array],default:()=>[]},modelModifiers:{}}),emits:["update:modelValue"],setup(e){const t=e,s=a(!1),o=a(0),c=l(e,"modelValue"),f=a([]);function _(e){return t.accept.split(",").map(e=>e.trim()).some(t=>"image/*"===t?e.type.startsWith("image/"):t.startsWith(".")?e.name.toLowerCase().endsWith(t):e.type===t)?!(e.size>1024*t.maxFileSize*1024)||(g.warning("上传图片不能大于"+t.maxFileSize+"M"),!1):(g.warning(`上传文件的格式不正确，仅支持：${t.accept}`),!1)}function h(e){return new Promise((l,a)=>{const s=e.file,i=new FormData;i.append(t.name,s),Object.keys(t.data).forEach(e=>{i.append(e,t.data[e])}),S.upload(i).then(e=>{l(e)}).catch(e=>{a(e)})})}function C(){g.warning("最多只能上传"+t.limit+"张图片")}const F=(e,t)=>{g.success("上传成功");const l=f.value.findIndex(e=>e.uid===t.uid);-1!==l&&(f.value[l].url=e.url,f.value[l].status="success",c.value[l]=e.url)},V=e=>{g.error("上传失败: "+e.message)},z=()=>{s.value=!1};return b(()=>{f.value=c.value.map(e=>({url:e}))}),(e,l)=>{const a=i("Plus"),g=v,b=i("zoom-in"),M=i("Delete"),E=k,O=I;return n(),u(w,null,[r(E,{"file-list":p(f),"onUpdate:fileList":l[0]||(l[0]=e=>d(f)?f.value=e:null),"list-type":"picture-card","before-upload":_,"http-request":h,"on-success":F,"on-error":V,"on-exceed":C,accept:t.accept,limit:t.limit,multiple:""},{file:m(({file:e})=>[y("div",D,[y("img",{class:"el-upload-list__item-thumbnail",src:e.url},null,8,A),y("span",H,[y("span",{onClick:t=>{return l=e.url,o.value=c.value.findIndex(e=>e===l),void(s.value=!0);var l}},[r(g,null,{default:m(()=>[r(b)]),_:1})],8,Q),y("span",{onClick:t=>{return l=e.url,void S.delete(l).then(()=>{const e=c.value.indexOf(l);-1!==e&&(c.value.splice(e,1),f.value.splice(e,1))});var l}},[r(g,null,{default:m(()=>[r(M)]),_:1})],8,Z)])])]),default:m(()=>[r(g,null,{default:m(()=>[r(a)]),_:1})]),_:1},8,["file-list","accept","limit"]),p(s)?(n(),j(O,{key:0,"zoom-rate":1.2,"initial-index":p(o),"url-list":c.value,onClose:z},null,8,["initial-index","url-list"])):x("",!0)],64)}}}),K={class:"app-container"},$=e({__name:"upload",setup(e){const t=a("https://s2.loli.net/2023/05/24/yNsxFC8rLHMZQcK.jpg"),l=a(["https://s2.loli.net/2023/05/24/yNsxFC8rLHMZQcK.jpg"]),s=a([{name:"照片1.jpg",url:"https://s2.loli.net/2023/05/24/yNsxFC8rLHMZQcK.jpg"},{name:"照片2.jpg",url:"https://s2.loli.net/2023/05/24/RuHFMwW4rG5lIqs.jpg"}]);return(e,a)=>{const i=O,o=q,f=C,y=W,v=F;return n(),u("div",K,[r(i,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/upload.vue",type:"primary",target:"_blank",class:"mb-10"},{default:m(()=>a[3]||(a[3]=[c(" 示例源码 请点击>>>> ")])),_:1,__:[3]}),r(v,null,{default:m(()=>[r(f,{label:"单图上传"},{default:m(()=>[r(o,{modelValue:p(t),"onUpdate:modelValue":a[0]||(a[0]=e=>d(t)?t.value=e:null)},null,8,["modelValue"])]),_:1}),r(f,{label:"多图上传"},{default:m(()=>[r(B,{modelValue:p(l),"onUpdate:modelValue":a[1]||(a[1]=e=>d(l)?l.value=e:null),limit:2},null,8,["modelValue"])]),_:1}),r(f,{label:"文件上传"},{default:m(()=>[r(y,{modelValue:p(s),"onUpdate:modelValue":a[2]||(a[2]=e=>d(s)?s.value=e:null)},null,8,["modelValue"])]),_:1})]),_:1})])}}});export{$ as default};
