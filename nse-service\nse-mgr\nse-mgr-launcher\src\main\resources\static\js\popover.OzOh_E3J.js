import{u as e,b as t,E as r}from"./popper.DpZVcW1M.js";import{d as o}from"./dropdown.Dp4e0zMH.js";import{ba as a,t as p,_ as s,d as i,c as n,b as l,r as d,i as f,J as b,e as c,f as u,w as v,l as g,h as m,g as h,n as y,F as w,a1 as x,W as S,q as A,cZ as k}from"./index.Ckm1SagX.js";const C=p({trigger:t.trigger,triggerKeys:t.triggerKeys,placement:o.placement,disabled:t.disabled,visible:e.visible,transition:e.transition,popperOptions:o.popperOptions,tabindex:o.tabindex,content:e.content,popperStyle:e.popperStyle,popperClass:e.popperClass,enterable:{...e.enterable,default:!0},effect:{...e.effect,default:"light"},teleported:e.teleported,appendTo:e.appendTo,title:String,width:{type:[String,Number],default:150},offset:{type:Number,default:void 0},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0},showArrow:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},"onUpdate:visible":{type:Function}}),N={"update:visible":e=>a(e),"before-enter":()=>!0,"before-leave":()=>!0,"after-enter":()=>!0,"after-leave":()=>!0},R=i({name:"ElPopover"}),$=i({...R,props:C,emits:N,setup(e,{expose:t,emit:o}){const a=e,p=n(()=>a["onUpdate:visible"]),s=l("popover"),i=d(),A=n(()=>{var e;return null==(e=f(i))?void 0:e.popperRef}),k=n(()=>[{width:b(a.width)},a.popperStyle]),C=n(()=>[s.b(),a.popperClass,{[s.m("plain")]:!!a.content}]),N=n(()=>a.transition===`${s.namespace.value}-fade-in-linear`),R=()=>{o("before-enter")},$=()=>{o("before-leave")},_=()=>{o("after-enter")},B=()=>{o("update:visible",!1),o("after-leave")};return t({popperRef:A,hide:()=>{var e;null==(e=i.value)||e.hide()}}),(e,t)=>(u(),c(f(r),S({ref_key:"tooltipRef",ref:i},e.$attrs,{trigger:e.trigger,"trigger-keys":e.triggerKeys,placement:e.placement,disabled:e.disabled,visible:e.visible,transition:e.transition,"popper-options":e.popperOptions,tabindex:e.tabindex,content:e.content,offset:e.offset,"show-after":e.showAfter,"hide-after":e.hideAfter,"auto-close":e.autoClose,"show-arrow":e.showArrow,"aria-label":e.title,effect:e.effect,enterable:e.enterable,"popper-class":f(C),"popper-style":f(k),teleported:e.teleported,"append-to":e.appendTo,persistent:e.persistent,"gpu-acceleration":f(N),"onUpdate:visible":f(p),onBeforeShow:R,onBeforeHide:$,onShow:_,onHide:B}),{content:v(()=>[e.title?(u(),h("div",{key:0,class:y(f(s).e("title")),role:"title"},w(e.title),3)):m("v-if",!0),g(e.$slots,"default",{},()=>[x(w(e.content),1)])]),default:v(()=>[e.$slots.reference?g(e.$slots,"reference",{key:0}):m("v-if",!0)]),_:3},16,["trigger","trigger-keys","placement","disabled","visible","transition","popper-options","tabindex","content","offset","show-after","hide-after","auto-close","show-arrow","aria-label","effect","enterable","popper-class","popper-style","teleported","append-to","persistent","gpu-acceleration","onUpdate:visible"]))}});const _=(e,t)=>{const r=t.arg||t.value,o=null==r?void 0:r.popperRef;o&&(o.triggerRef=e)};const B=A(s($,[["__file","popover.vue"]]),{directive:k({mounted(e,t){_(e,t)},updated(e,t){_(e,t)}},"popover")});export{B as E};
