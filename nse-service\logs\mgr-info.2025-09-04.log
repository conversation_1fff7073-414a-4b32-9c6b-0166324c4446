10:36:06.926 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:36:07.030 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 3368 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:36:07.031 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "prod"
10:36:09.257 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:36:10.469 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:36:10.482 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:36:10.482 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:36:10.575 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:36:10.721 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:36:10.893 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:36:12.295 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:36:13.417 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
10:37:24.535 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:37:24.614 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 27248 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:37:24.615 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:37:26.628 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:37:27.715 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:37:27.719 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:37:27.720 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:37:27.814 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:37:27.983 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:37:28.137 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:37:29.341 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:37:29.756 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5ac646b3
10:37:29.759 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:37:29.829 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
10:37:30.196 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.152s)
10:37:30.527 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
10:37:30.593 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
10:37:31.929 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:37:31.947 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:37:32.139 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:37:32.439 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:37:32.536 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:37:32.688 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:37:32.782 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2204769ms : this is harmless.
10:37:32.818 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:37:32.821 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:37:32.882 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:37:32.884 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:37:32.884 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:37:34.234 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:37:34.277 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:37:34.680 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:37:35.248 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@159e366
10:37:35.649 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:37:35.698 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 11.751 seconds (process running for 12.738)
10:37:36.373 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
10:37:36.373 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
10:37:36.374 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
10:37:36.374 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:37:36.374 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:37:36.374 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:37:36.374 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:37:36.374 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:37:36.374 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:37:36.375 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:37:36.454 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,60] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
10:37:36.713 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_THREAT_DETECTED, evtDetails=RuntimeDetectedService.SecurityStatus(debuggingDetected=true, jvmTampered=false, fileIntegrityViolated=false, memoryAnomalyDetected=false, networkAnomalyDetected=false, securityLevel=MEDIUM_RISK, lastCheckTime=1756953456445), remark=null, createdDate=2025-09-04T10:37:36.454804, modifiedDate=2025-09-04T10:37:36.454804, evtLevel=WARNING, evtMessage=安全事件检查)
10:37:37.367 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Thu Sep 04 10:37:37 CST 2025, errorMessage=null, details=[], extensions=null)
10:37:37.375 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| 50             | 5                | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

10:37:37.901 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Thu Sep 04 10:37:37 CST 2025, errorMessage=null, details=[], extensions=null)
