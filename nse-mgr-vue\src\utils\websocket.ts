export class MessageWebSocket {
  private ws: WebSocket | null = null;
  private url: string;
  private onMessageCallback: ((data: any) => void) | null = null;

  constructor(url: string) {
    this.url = url;
  }

  connect() {
    this.ws = new WebSocket(this.url);
    this.ws.onopen = () => {
      // 连接成功
      console.log("WebSocket connected");
    };
    this.ws.onmessage = (event) => {
      if (this.onMessageCallback) {
        try {
          const data = JSON.parse(event.data);
          this.onMessageCallback(data);
        } catch {
          this.onMessageCallback(event.data);
        }
      }
    };
    this.ws.onclose = () => {
      console.log("WebSocket disconnected");
      // 断线重连
      setTimeout(() => this.connect(), 3000);
    };
    this.ws.onerror = () => {
      this.ws?.close();
    };
  }

  // 发送消息
  send(data: any): boolean {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        // 如果数据是对象，转换为JSON字符串
        const message = typeof data === 'string' ? data : JSON.stringify(data);
        this.ws.send(message);
        return true;
      } catch (error) {
        console.error("Failed to send message:", error);
        return false;
      }
    } else {
      console.warn("WebSocket is not connected. Cannot send message.");
      return false;
    }
  }

  onMessage(cb: (data: any) => void) {
    this.onMessageCallback = cb;
  }

  close() {
    this.ws?.close();
  }
}
