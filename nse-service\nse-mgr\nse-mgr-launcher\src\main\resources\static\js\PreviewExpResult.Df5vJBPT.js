import{bm as e,aE as t,d as r,ax as n,r as a,az as s,o as i,a2 as o,g as l,f as c,m as h,w as u,C as d}from"./index.Ckm1SagX.js";import{E as f}from"./dialog.TtqHlFhB.js";import"./overlay.CXfNA60T.js";import{c as p}from"./_commonjs-dynamic-modules.BHR_E30J.js";import{_ as m}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.BjYFza3j.js";import"./vnode.BkZiIFpS.js";import"./aria.C1IWO_Rd.js";import"./scroll.XdyICIdv.js";import"./focus-trap.Bd_uzvDY.js";import"./index.C0OsJ5su.js";import"./refs.biN0GvkM.js";import"./index.BRUQ9gWw.js";import"./event.BwRzfsZt.js";import"./index.Dh_vcBr5.js";var g,b={exports:{}};const y=t((g||(g=1,b.exports=function e(t,r,n){function a(i,o){if(!r[i]){if(!t[i]){var l="function"==typeof p&&p;if(!o&&l)return l(i,!0);if(s)return s(i,!0);var c=new Error("Cannot find module '"+i+"'");throw c.code="MODULE_NOT_FOUND",c}var h=r[i]={exports:{}};t[i][0].call(h.exports,function(e){return a(t[i][1][e]||e)},h,h.exports,e,t,r,n)}return r[i].exports}for(var s="function"==typeof p&&p,i=0;i<n.length;i++)a(n[i]);return a}({1:[function(e,t,r){var n=e("./utils"),a=e("./support"),s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";r.encode=function(e){for(var t,r,a,i,o,l,c,h=[],u=0,d=e.length,f=d,p="string"!==n.getTypeOf(e);u<e.length;)f=d-u,a=p?(t=e[u++],r=u<d?e[u++]:0,u<d?e[u++]:0):(t=e.charCodeAt(u++),r=u<d?e.charCodeAt(u++):0,u<d?e.charCodeAt(u++):0),i=t>>2,o=(3&t)<<4|r>>4,l=1<f?(15&r)<<2|a>>6:64,c=2<f?63&a:64,h.push(s.charAt(i)+s.charAt(o)+s.charAt(l)+s.charAt(c));return h.join("")},r.decode=function(e){var t,r,n,i,o,l,c=0,h=0,u="data:";if(e.substr(0,u.length)===u)throw new Error("Invalid base64 input, it looks like a data url.");var d,f=3*(e=e.replace(/[^A-Za-z0-9+/=]/g,"")).length/4;if(e.charAt(e.length-1)===s.charAt(64)&&f--,e.charAt(e.length-2)===s.charAt(64)&&f--,f%1!=0)throw new Error("Invalid base64 input, bad content length.");for(d=a.uint8array?new Uint8Array(0|f):new Array(0|f);c<e.length;)t=s.indexOf(e.charAt(c++))<<2|(i=s.indexOf(e.charAt(c++)))>>4,r=(15&i)<<4|(o=s.indexOf(e.charAt(c++)))>>2,n=(3&o)<<6|(l=s.indexOf(e.charAt(c++))),d[h++]=t,64!==o&&(d[h++]=r),64!==l&&(d[h++]=n);return d}},{"./support":30,"./utils":32}],2:[function(e,t,r){var n=e("./external"),a=e("./stream/DataWorker"),s=e("./stream/Crc32Probe"),i=e("./stream/DataLengthProbe");function o(e,t,r,n,a){this.compressedSize=e,this.uncompressedSize=t,this.crc32=r,this.compression=n,this.compressedContent=a}o.prototype={getContentWorker:function(){var e=new a(n.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new i("data_length")),t=this;return e.on("end",function(){if(this.streamInfo.data_length!==t.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")}),e},getCompressedWorker:function(){return new a(n.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},o.createWorkerFrom=function(e,t,r){return e.pipe(new s).pipe(new i("uncompressedSize")).pipe(t.compressWorker(r)).pipe(new i("compressedSize")).withStreamInfo("compression",t)},t.exports=o},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(e,t,r){var n=e("./stream/GenericWorker");r.STORE={magic:"\0\0",compressWorker:function(){return new n("STORE compression")},uncompressWorker:function(){return new n("STORE decompression")}},r.DEFLATE=e("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(e,t,r){var n=e("./utils"),a=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t){return void 0!==e&&e.length?"string"!==n.getTypeOf(e)?function(e,t,r,n){var s=a,i=n+r;e^=-1;for(var o=n;o<i;o++)e=e>>>8^s[255&(e^t[o])];return-1^e}(0|t,e,e.length,0):function(e,t,r,n){var s=a,i=n+r;e^=-1;for(var o=n;o<i;o++)e=e>>>8^s[255&(e^t.charCodeAt(o))];return-1^e}(0|t,e,e.length,0):0}},{"./utils":32}],5:[function(e,t,r){r.base64=!1,r.binary=!1,r.dir=!1,r.createFolders=!0,r.date=null,r.compression=null,r.compressionOptions=null,r.comment=null,r.unixPermissions=null,r.dosPermissions=null},{}],6:[function(e,t,r){var n=null;n="undefined"!=typeof Promise?Promise:e("lie"),t.exports={Promise:n}},{lie:37}],7:[function(e,t,r){var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,a=e("pako"),s=e("./utils"),i=e("./stream/GenericWorker"),o=n?"uint8array":"array";function l(e,t){i.call(this,"FlateWorker/"+e),this._pako=null,this._pakoAction=e,this._pakoOptions=t,this.meta={}}r.magic="\b\0",s.inherits(l,i),l.prototype.processChunk=function(e){this.meta=e.meta,null===this._pako&&this._createPako(),this._pako.push(s.transformTo(o,e.data),!1)},l.prototype.flush=function(){i.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},l.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this._pako=null},l.prototype._createPako=function(){this._pako=new a[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var e=this;this._pako.onData=function(t){e.push({data:t,meta:e.meta})}},r.compressWorker=function(e){return new l("Deflate",e)},r.uncompressWorker=function(){return new l("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(e,t,r){function n(e,t){var r,n="";for(r=0;r<t;r++)n+=String.fromCharCode(255&e),e>>>=8;return n}function a(e,t,r,a,i,h){var u,d,f=e.file,p=e.compression,m=h!==o.utf8encode,g=s.transformTo("string",h(f.name)),b=s.transformTo("string",o.utf8encode(f.name)),y=f.comment,v=s.transformTo("string",h(y)),k=s.transformTo("string",o.utf8encode(y)),_=b.length!==f.name.length,w=k.length!==y.length,S="",x="",C="",E=f.dir,A=f.date,P={crc32:0,compressedSize:0,uncompressedSize:0};t&&!r||(P.crc32=e.crc32,P.compressedSize=e.compressedSize,P.uncompressedSize=e.uncompressedSize);var N=0;t&&(N|=8),m||!_&&!w||(N|=2048);var T,z,B,R=0,D=0;E&&(R|=16),"UNIX"===i?(D=798,R|=(T=f.unixPermissions,z=E,B=T,T||(B=z?16893:33204),(65535&B)<<16)):(D=20,R|=function(e){return 63&(e||0)}(f.dosPermissions)),u=A.getUTCHours(),u<<=6,u|=A.getUTCMinutes(),u<<=5,u|=A.getUTCSeconds()/2,d=A.getUTCFullYear()-1980,d<<=4,d|=A.getUTCMonth()+1,d<<=5,d|=A.getUTCDate(),_&&(x=n(1,1)+n(l(g),4)+b,S+="up"+n(x.length,2)+x),w&&(C=n(1,1)+n(l(v),4)+k,S+="uc"+n(C.length,2)+C);var I="";return I+="\n\0",I+=n(N,2),I+=p.magic,I+=n(u,2),I+=n(d,2),I+=n(P.crc32,4),I+=n(P.compressedSize,4),I+=n(P.uncompressedSize,4),I+=n(g.length,2),I+=n(S.length,2),{fileRecord:c.LOCAL_FILE_HEADER+I+g+S,dirRecord:c.CENTRAL_FILE_HEADER+n(D,2)+I+n(v.length,2)+"\0\0\0\0"+n(R,4)+n(a,4)+g+S+v}}var s=e("../utils"),i=e("../stream/GenericWorker"),o=e("../utf8"),l=e("../crc32"),c=e("../signature");function h(e,t,r,n){i.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=t,this.zipPlatform=r,this.encodeFileName=n,this.streamFiles=e,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}s.inherits(h,i),h.prototype.push=function(e){var t=e.meta.percent||0,r=this.entriesCount,n=this._sources.length;this.accumulate?this.contentBuffer.push(e):(this.bytesWritten+=e.data.length,i.prototype.push.call(this,{data:e.data,meta:{currentFile:this.currentFile,percent:r?(t+100*(r-n-1))/r:100}}))},h.prototype.openedSource=function(e){this.currentSourceOffset=this.bytesWritten,this.currentFile=e.file.name;var t=this.streamFiles&&!e.file.dir;if(t){var r=a(e,t,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:r.fileRecord,meta:{percent:0}})}else this.accumulate=!0},h.prototype.closedSource=function(e){this.accumulate=!1;var t,r=this.streamFiles&&!e.file.dir,s=a(e,r,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(s.dirRecord),r)this.push({data:(t=e,c.DATA_DESCRIPTOR+n(t.crc32,4)+n(t.compressedSize,4)+n(t.uncompressedSize,4)),meta:{percent:100}});else for(this.push({data:s.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},h.prototype.flush=function(){for(var e=this.bytesWritten,t=0;t<this.dirRecords.length;t++)this.push({data:this.dirRecords[t],meta:{percent:100}});var r,a,i,o,l,h,u=this.bytesWritten-e,d=(r=this.dirRecords.length,a=u,i=e,o=this.zipComment,l=this.encodeFileName,h=s.transformTo("string",l(o)),c.CENTRAL_DIRECTORY_END+"\0\0\0\0"+n(r,2)+n(r,2)+n(a,4)+n(i,4)+n(h.length,2)+h);this.push({data:d,meta:{percent:100}})},h.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},h.prototype.registerPrevious=function(e){this._sources.push(e);var t=this;return e.on("data",function(e){t.processChunk(e)}),e.on("end",function(){t.closedSource(t.previous.streamInfo),t._sources.length?t.prepareNextSource():t.end()}),e.on("error",function(e){t.error(e)}),this},h.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},h.prototype.error=function(e){var t=this._sources;if(!i.prototype.error.call(this,e))return!1;for(var r=0;r<t.length;r++)try{t[r].error(e)}catch(n){}return!0},h.prototype.lock=function(){i.prototype.lock.call(this);for(var e=this._sources,t=0;t<e.length;t++)e[t].lock()},t.exports=h},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(e,t,r){var n=e("../compressions"),a=e("./ZipFileWorker");r.generateWorker=function(e,t,r){var s=new a(t.streamFiles,r,t.platform,t.encodeFileName),i=0;try{e.forEach(function(e,r){i++;var a=function(e,t){var r=e||t,a=n[r];if(!a)throw new Error(r+" is not a valid compression method !");return a}(r.options.compression,t.compression),o=r.options.compressionOptions||t.compressionOptions||{},l=r.dir,c=r.date;r._compressWorker(a,o).withStreamInfo("file",{name:e,dir:l,date:c,comment:r.comment||"",unixPermissions:r.unixPermissions,dosPermissions:r.dosPermissions}).pipe(s)}),s.entriesCount=i}catch(o){s.error(o)}return s}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(e,t,r){function n(){if(!(this instanceof n))return new n;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var e=new n;for(var t in this)"function"!=typeof this[t]&&(e[t]=this[t]);return e}}(n.prototype=e("./object")).loadAsync=e("./load"),n.support=e("./support"),n.defaults=e("./defaults"),n.version="3.10.1",n.loadAsync=function(e,t){return(new n).loadAsync(e,t)},n.external=e("./external"),t.exports=n},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(e,t,r){var n=e("./utils"),a=e("./external"),s=e("./utf8"),i=e("./zipEntries"),o=e("./stream/Crc32Probe"),l=e("./nodejsUtils");function c(e){return new a.Promise(function(t,r){var n=e.decompressed.getContentWorker().pipe(new o);n.on("error",function(e){r(e)}).on("end",function(){n.streamInfo.crc32!==e.decompressed.crc32?r(new Error("Corrupted zip : CRC32 mismatch")):t()}).resume()})}t.exports=function(e,t){var r=this;return t=n.extend(t||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:s.utf8decode}),l.isNode&&l.isStream(e)?a.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):n.prepareContent("the loaded zip file",e,!0,t.optimizedBinaryString,t.base64).then(function(e){var r=new i(t);return r.load(e),r}).then(function(e){var r=[a.Promise.resolve(e)],n=e.files;if(t.checkCRC32)for(var s=0;s<n.length;s++)r.push(c(n[s]));return a.Promise.all(r)}).then(function(e){for(var a=e.shift(),s=a.files,i=0;i<s.length;i++){var o=s[i],l=o.fileNameStr,c=n.resolve(o.fileNameStr);r.file(c,o.decompressed,{binary:!0,optimizedBinaryString:!0,date:o.date,dir:o.dir,comment:o.fileCommentStr.length?o.fileCommentStr:null,unixPermissions:o.unixPermissions,dosPermissions:o.dosPermissions,createFolders:t.createFolders}),o.dir||(r.file(c).unsafeOriginalName=l)}return a.zipComment.length&&(r.comment=a.zipComment),r})}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(e,t,r){var n=e("../utils"),a=e("../stream/GenericWorker");function s(e,t){a.call(this,"Nodejs stream input adapter for "+e),this._upstreamEnded=!1,this._bindStream(t)}n.inherits(s,a),s.prototype._bindStream=function(e){var t=this;(this._stream=e).pause(),e.on("data",function(e){t.push({data:e,meta:{percent:0}})}).on("error",function(e){t.isPaused?this.generatedError=e:t.error(e)}).on("end",function(){t.isPaused?t._upstreamEnded=!0:t.end()})},s.prototype.pause=function(){return!!a.prototype.pause.call(this)&&(this._stream.pause(),!0)},s.prototype.resume=function(){return!!a.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},t.exports=s},{"../stream/GenericWorker":28,"../utils":32}],13:[function(e,t,r){var n=e("readable-stream").Readable;function a(e,t,r){n.call(this,t),this._helper=e;var a=this;e.on("data",function(e,t){a.push(e)||a._helper.pause(),r&&r(t)}).on("error",function(e){a.emit("error",e)}).on("end",function(){a.push(null)})}e("../utils").inherits(a,n),a.prototype._read=function(){this._helper.resume()},t.exports=a},{"../utils":32,"readable-stream":16}],14:[function(e,t,r){t.exports={isNode:"undefined"!=typeof Buffer,newBufferFrom:function(e,t){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(e,t);if("number"==typeof e)throw new Error('The "data" argument must not be a number');return new Buffer(e,t)},allocBuffer:function(e){if(Buffer.alloc)return Buffer.alloc(e);var t=new Buffer(e);return t.fill(0),t},isBuffer:function(e){return Buffer.isBuffer(e)},isStream:function(e){return e&&"function"==typeof e.on&&"function"==typeof e.pause&&"function"==typeof e.resume}}},{}],15:[function(e,t,r){function n(e,t,r){var n,a=s.getTypeOf(t),o=s.extend(r||{},l);o.date=o.date||new Date,null!==o.compression&&(o.compression=o.compression.toUpperCase()),"string"==typeof o.unixPermissions&&(o.unixPermissions=parseInt(o.unixPermissions,8)),o.unixPermissions&&16384&o.unixPermissions&&(o.dir=!0),o.dosPermissions&&16&o.dosPermissions&&(o.dir=!0),o.dir&&(e=m(e)),o.createFolders&&(n=p(e))&&g.call(this,n,!0);var u="string"===a&&!1===o.binary&&!1===o.base64;r&&void 0!==r.binary||(o.binary=!u),(t instanceof c&&0===t.uncompressedSize||o.dir||!t||0===t.length)&&(o.base64=!1,o.binary=!0,t="",o.compression="STORE",a="string");var b=null;b=t instanceof c||t instanceof i?t:d.isNode&&d.isStream(t)?new f(e,t):s.prepareContent(e,t,o.binary,o.optimizedBinaryString,o.base64);var y=new h(e,b,o);this.files[e]=y}var a=e("./utf8"),s=e("./utils"),i=e("./stream/GenericWorker"),o=e("./stream/StreamHelper"),l=e("./defaults"),c=e("./compressedObject"),h=e("./zipObject"),u=e("./generate"),d=e("./nodejsUtils"),f=e("./nodejs/NodejsStreamInputAdapter"),p=function(e){"/"===e.slice(-1)&&(e=e.substring(0,e.length-1));var t=e.lastIndexOf("/");return 0<t?e.substring(0,t):""},m=function(e){return"/"!==e.slice(-1)&&(e+="/"),e},g=function(e,t){return t=void 0!==t?t:l.createFolders,e=m(e),this.files[e]||n.call(this,e,null,{dir:!0,createFolders:t}),this.files[e]};function b(e){return"[object RegExp]"===Object.prototype.toString.call(e)}var y={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(e){var t,r,n;for(t in this.files)n=this.files[t],(r=t.slice(this.root.length,t.length))&&t.slice(0,this.root.length)===this.root&&e(r,n)},filter:function(e){var t=[];return this.forEach(function(r,n){e(r,n)&&t.push(n)}),t},file:function(e,t,r){if(1!==arguments.length)return e=this.root+e,n.call(this,e,t,r),this;if(b(e)){var a=e;return this.filter(function(e,t){return!t.dir&&a.test(e)})}var s=this.files[this.root+e];return s&&!s.dir?s:null},folder:function(e){if(!e)return this;if(b(e))return this.filter(function(t,r){return r.dir&&e.test(t)});var t=this.root+e,r=g.call(this,t),n=this.clone();return n.root=r.name,n},remove:function(e){e=this.root+e;var t=this.files[e];if(t||("/"!==e.slice(-1)&&(e+="/"),t=this.files[e]),t&&!t.dir)delete this.files[e];else for(var r=this.filter(function(t,r){return r.name.slice(0,e.length)===e}),n=0;n<r.length;n++)delete this.files[r[n].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(e){var t,r={};try{if((r=s.extend(e||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:a.utf8encode})).type=r.type.toLowerCase(),r.compression=r.compression.toUpperCase(),"binarystring"===r.type&&(r.type="string"),!r.type)throw new Error("No output type specified.");s.checkSupport(r.type),"darwin"!==r.platform&&"freebsd"!==r.platform&&"linux"!==r.platform&&"sunos"!==r.platform||(r.platform="UNIX"),"win32"===r.platform&&(r.platform="DOS");var n=r.comment||this.comment||"";t=u.generateWorker(this,r,n)}catch(l){(t=new i("error")).error(l)}return new o(t,r.type||"string",r.mimeType)},generateAsync:function(e,t){return this.generateInternalStream(e).accumulate(t)},generateNodeStream:function(e,t){return(e=e||{}).type||(e.type="nodebuffer"),this.generateInternalStream(e).toNodejsStream(t)}};t.exports=y},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(e,t,r){t.exports=e("stream")},{stream:void 0}],17:[function(e,t,r){var n=e("./DataReader");function a(e){n.call(this,e);for(var t=0;t<this.data.length;t++)e[t]=255&e[t]}e("../utils").inherits(a,n),a.prototype.byteAt=function(e){return this.data[this.zero+e]},a.prototype.lastIndexOfSignature=function(e){for(var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),a=e.charCodeAt(3),s=this.length-4;0<=s;--s)if(this.data[s]===t&&this.data[s+1]===r&&this.data[s+2]===n&&this.data[s+3]===a)return s-this.zero;return-1},a.prototype.readAndCheckSignature=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),a=e.charCodeAt(3),s=this.readData(4);return t===s[0]&&r===s[1]&&n===s[2]&&a===s[3]},a.prototype.readData=function(e){if(this.checkOffset(e),0===e)return[];var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=a},{"../utils":32,"./DataReader":18}],18:[function(e,t,r){var n=e("../utils");function a(e){this.data=e,this.length=e.length,this.index=0,this.zero=0}a.prototype={checkOffset:function(e){this.checkIndex(this.index+e)},checkIndex:function(e){if(this.length<this.zero+e||e<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+e+"). Corrupted zip ?")},setIndex:function(e){this.checkIndex(e),this.index=e},skip:function(e){this.setIndex(this.index+e)},byteAt:function(){},readInt:function(e){var t,r=0;for(this.checkOffset(e),t=this.index+e-1;t>=this.index;t--)r=(r<<8)+this.byteAt(t);return this.index+=e,r},readString:function(e){return n.transformTo("string",this.readData(e))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var e=this.readInt(4);return new Date(Date.UTC(1980+(e>>25&127),(e>>21&15)-1,e>>16&31,e>>11&31,e>>5&63,(31&e)<<1))}},t.exports=a},{"../utils":32}],19:[function(e,t,r){var n=e("./Uint8ArrayReader");function a(e){n.call(this,e)}e("../utils").inherits(a,n),a.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=a},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(e,t,r){var n=e("./DataReader");function a(e){n.call(this,e)}e("../utils").inherits(a,n),a.prototype.byteAt=function(e){return this.data.charCodeAt(this.zero+e)},a.prototype.lastIndexOfSignature=function(e){return this.data.lastIndexOf(e)-this.zero},a.prototype.readAndCheckSignature=function(e){return e===this.readData(4)},a.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=a},{"../utils":32,"./DataReader":18}],21:[function(e,t,r){var n=e("./ArrayReader");function a(e){n.call(this,e)}e("../utils").inherits(a,n),a.prototype.readData=function(e){if(this.checkOffset(e),0===e)return new Uint8Array(0);var t=this.data.subarray(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=a},{"../utils":32,"./ArrayReader":17}],22:[function(e,t,r){var n=e("../utils"),a=e("../support"),s=e("./ArrayReader"),i=e("./StringReader"),o=e("./NodeBufferReader"),l=e("./Uint8ArrayReader");t.exports=function(e){var t=n.getTypeOf(e);return n.checkSupport(t),"string"!==t||a.uint8array?"nodebuffer"===t?new o(e):a.uint8array?new l(n.transformTo("uint8array",e)):new s(n.transformTo("array",e)):new i(e)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(e,t,r){r.LOCAL_FILE_HEADER="PK",r.CENTRAL_FILE_HEADER="PK",r.CENTRAL_DIRECTORY_END="PK",r.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",r.ZIP64_CENTRAL_DIRECTORY_END="PK",r.DATA_DESCRIPTOR="PK\b"},{}],24:[function(e,t,r){var n=e("./GenericWorker"),a=e("../utils");function s(e){n.call(this,"ConvertWorker to "+e),this.destType=e}a.inherits(s,n),s.prototype.processChunk=function(e){this.push({data:a.transformTo(this.destType,e.data),meta:e.meta})},t.exports=s},{"../utils":32,"./GenericWorker":28}],25:[function(e,t,r){var n=e("./GenericWorker"),a=e("../crc32");function s(){n.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}e("../utils").inherits(s,n),s.prototype.processChunk=function(e){this.streamInfo.crc32=a(e.data,this.streamInfo.crc32||0),this.push(e)},t.exports=s},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(e,t,r){var n=e("../utils"),a=e("./GenericWorker");function s(e){a.call(this,"DataLengthProbe for "+e),this.propName=e,this.withStreamInfo(e,0)}n.inherits(s,a),s.prototype.processChunk=function(e){if(e){var t=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=t+e.data.length}a.prototype.processChunk.call(this,e)},t.exports=s},{"../utils":32,"./GenericWorker":28}],27:[function(e,t,r){var n=e("../utils"),a=e("./GenericWorker");function s(e){a.call(this,"DataWorker");var t=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,e.then(function(e){t.dataIsReady=!0,t.data=e,t.max=e&&e.length||0,t.type=n.getTypeOf(e),t.isPaused||t._tickAndRepeat()},function(e){t.error(e)})}n.inherits(s,a),s.prototype.cleanUp=function(){a.prototype.cleanUp.call(this),this.data=null},s.prototype.resume=function(){return!!a.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,n.delay(this._tickAndRepeat,[],this)),!0)},s.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(n.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},s.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var e=null,t=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":e=this.data.substring(this.index,t);break;case"uint8array":e=this.data.subarray(this.index,t);break;case"array":case"nodebuffer":e=this.data.slice(this.index,t)}return this.index=t,this.push({data:e,meta:{percent:this.max?this.index/this.max*100:0}})},t.exports=s},{"../utils":32,"./GenericWorker":28}],28:[function(e,t,r){function n(e){this.name=e||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}n.prototype={push:function(e){this.emit("data",e)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(e){this.emit("error",e)}return!0},error:function(e){return!this.isFinished&&(this.isPaused?this.generatedError=e:(this.isFinished=!0,this.emit("error",e),this.previous&&this.previous.error(e),this.cleanUp()),!0)},on:function(e,t){return this._listeners[e].push(t),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(e,t){if(this._listeners[e])for(var r=0;r<this._listeners[e].length;r++)this._listeners[e][r].call(this,t)},pipe:function(e){return e.registerPrevious(this)},registerPrevious:function(e){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=e.streamInfo,this.mergeStreamInfo(),this.previous=e;var t=this;return e.on("data",function(e){t.processChunk(e)}),e.on("end",function(){t.end()}),e.on("error",function(e){t.error(e)}),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;var e=this.isPaused=!1;return this.generatedError&&(this.error(this.generatedError),e=!0),this.previous&&this.previous.resume(),!e},flush:function(){},processChunk:function(e){this.push(e)},withStreamInfo:function(e,t){return this.extraStreamInfo[e]=t,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var e in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,e)&&(this.streamInfo[e]=this.extraStreamInfo[e])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var e="Worker "+this.name;return this.previous?this.previous+" -> "+e:e}},t.exports=n},{}],29:[function(e,t,r){var n=e("../utils"),a=e("./ConvertWorker"),s=e("./GenericWorker"),i=e("../base64"),o=e("../support"),l=e("../external"),c=null;if(o.nodestream)try{c=e("../nodejs/NodejsStreamOutputAdapter")}catch(d){}function h(e,t){return new l.Promise(function(r,a){var s=[],o=e._internalType,l=e._outputType,c=e._mimeType;e.on("data",function(e,r){s.push(e),t&&t(r)}).on("error",function(e){s=[],a(e)}).on("end",function(){try{var e=function(e,t,r){switch(e){case"blob":return n.newBlob(n.transformTo("arraybuffer",t),r);case"base64":return i.encode(t);default:return n.transformTo(e,t)}}(l,function(e,t){var r,n=0,a=null,s=0;for(r=0;r<t.length;r++)s+=t[r].length;switch(e){case"string":return t.join("");case"array":return Array.prototype.concat.apply([],t);case"uint8array":for(a=new Uint8Array(s),r=0;r<t.length;r++)a.set(t[r],n),n+=t[r].length;return a;case"nodebuffer":return Buffer.concat(t);default:throw new Error("concat : unsupported type '"+e+"'")}}(o,s),c);r(e)}catch(t){a(t)}s=[]}).resume()})}function u(e,t,r){var i=t;switch(t){case"blob":case"arraybuffer":i="uint8array";break;case"base64":i="string"}try{this._internalType=i,this._outputType=t,this._mimeType=r,n.checkSupport(i),this._worker=e.pipe(new a(i)),e.lock()}catch(o){this._worker=new s("error"),this._worker.error(o)}}u.prototype={accumulate:function(e){return h(this,e)},on:function(e,t){var r=this;return"data"===e?this._worker.on(e,function(e){t.call(r,e.data,e.meta)}):this._worker.on(e,function(){n.delay(t,arguments,r)}),this},resume:function(){return n.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(e){if(n.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new c(this,{objectMode:"nodebuffer"!==this._outputType},e)}},t.exports=u},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(e,t,r){if(r.base64=!0,r.array=!0,r.string=!0,r.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,r.nodebuffer="undefined"!=typeof Buffer,r.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)r.blob=!1;else{var n=new ArrayBuffer(0);try{r.blob=0===new Blob([n],{type:"application/zip"}).size}catch(s){try{var a=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);a.append(n),r.blob=0===a.getBlob("application/zip").size}catch(i){r.blob=!1}}}try{r.nodestream=!!e("readable-stream").Readable}catch(s){r.nodestream=!1}},{"readable-stream":16}],31:[function(e,t,r){for(var n=e("./utils"),a=e("./support"),s=e("./nodejsUtils"),i=e("./stream/GenericWorker"),o=new Array(256),l=0;l<256;l++)o[l]=252<=l?6:248<=l?5:240<=l?4:224<=l?3:192<=l?2:1;function c(){i.call(this,"utf-8 decode"),this.leftOver=null}function h(){i.call(this,"utf-8 encode")}o[254]=o[254]=1,r.utf8encode=function(e){return a.nodebuffer?s.newBufferFrom(e,"utf-8"):function(e){var t,r,n,s,i,o=e.length,l=0;for(s=0;s<o;s++)55296==(64512&(r=e.charCodeAt(s)))&&s+1<o&&56320==(64512&(n=e.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(n-56320),s++),l+=r<128?1:r<2048?2:r<65536?3:4;for(t=a.uint8array?new Uint8Array(l):new Array(l),s=i=0;i<l;s++)55296==(64512&(r=e.charCodeAt(s)))&&s+1<o&&56320==(64512&(n=e.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(n-56320),s++),r<128?t[i++]=r:(r<2048?t[i++]=192|r>>>6:(r<65536?t[i++]=224|r>>>12:(t[i++]=240|r>>>18,t[i++]=128|r>>>12&63),t[i++]=128|r>>>6&63),t[i++]=128|63&r);return t}(e)},r.utf8decode=function(e){return a.nodebuffer?n.transformTo("nodebuffer",e).toString("utf-8"):function(e){var t,r,a,s,i=e.length,l=new Array(2*i);for(t=r=0;t<i;)if((a=e[t++])<128)l[r++]=a;else if(4<(s=o[a]))l[r++]=65533,t+=s-1;else{for(a&=2===s?31:3===s?15:7;1<s&&t<i;)a=a<<6|63&e[t++],s--;1<s?l[r++]=65533:a<65536?l[r++]=a:(a-=65536,l[r++]=55296|a>>10&1023,l[r++]=56320|1023&a)}return l.length!==r&&(l.subarray?l=l.subarray(0,r):l.length=r),n.applyFromCharCode(l)}(e=n.transformTo(a.uint8array?"uint8array":"array",e))},n.inherits(c,i),c.prototype.processChunk=function(e){var t=n.transformTo(a.uint8array?"uint8array":"array",e.data);if(this.leftOver&&this.leftOver.length){if(a.uint8array){var s=t;(t=new Uint8Array(s.length+this.leftOver.length)).set(this.leftOver,0),t.set(s,this.leftOver.length)}else t=this.leftOver.concat(t);this.leftOver=null}var i=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;0<=r&&128==(192&e[r]);)r--;return r<0||0===r?t:r+o[e[r]]>t?r:t}(t),l=t;i!==t.length&&(a.uint8array?(l=t.subarray(0,i),this.leftOver=t.subarray(i,t.length)):(l=t.slice(0,i),this.leftOver=t.slice(i,t.length))),this.push({data:r.utf8decode(l),meta:e.meta})},c.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:r.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},r.Utf8DecodeWorker=c,n.inherits(h,i),h.prototype.processChunk=function(e){this.push({data:r.utf8encode(e.data),meta:e.meta})},r.Utf8EncodeWorker=h},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(e,t,r){var n=e("./support"),a=e("./base64"),s=e("./nodejsUtils"),i=e("./external");function o(e){return e}function l(e,t){for(var r=0;r<e.length;++r)t[r]=255&e.charCodeAt(r);return t}e("setimmediate"),r.newBlob=function(e,t){r.checkSupport("blob");try{return new Blob([e],{type:t})}catch(a){try{var n=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return n.append(e),n.getBlob(t)}catch(s){throw new Error("Bug : can't construct the Blob.")}}};var c={stringifyByChunk:function(e,t,r){var n=[],a=0,s=e.length;if(s<=r)return String.fromCharCode.apply(null,e);for(;a<s;)"array"===t||"nodebuffer"===t?n.push(String.fromCharCode.apply(null,e.slice(a,Math.min(a+r,s)))):n.push(String.fromCharCode.apply(null,e.subarray(a,Math.min(a+r,s)))),a+=r;return n.join("")},stringifyByChar:function(e){for(var t="",r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t},applyCanBeUsed:{uint8array:function(){try{return n.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(e){return!1}}(),nodebuffer:function(){try{return n.nodebuffer&&1===String.fromCharCode.apply(null,s.allocBuffer(1)).length}catch(e){return!1}}()}};function h(e){var t=65536,n=r.getTypeOf(e),a=!0;if("uint8array"===n?a=c.applyCanBeUsed.uint8array:"nodebuffer"===n&&(a=c.applyCanBeUsed.nodebuffer),a)for(;1<t;)try{return c.stringifyByChunk(e,n,t)}catch(s){t=Math.floor(t/2)}return c.stringifyByChar(e)}function u(e,t){for(var r=0;r<e.length;r++)t[r]=e[r];return t}r.applyFromCharCode=h;var d={};d.string={string:o,array:function(e){return l(e,new Array(e.length))},arraybuffer:function(e){return d.string.uint8array(e).buffer},uint8array:function(e){return l(e,new Uint8Array(e.length))},nodebuffer:function(e){return l(e,s.allocBuffer(e.length))}},d.array={string:h,array:o,arraybuffer:function(e){return new Uint8Array(e).buffer},uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return s.newBufferFrom(e)}},d.arraybuffer={string:function(e){return h(new Uint8Array(e))},array:function(e){return u(new Uint8Array(e),new Array(e.byteLength))},arraybuffer:o,uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return s.newBufferFrom(new Uint8Array(e))}},d.uint8array={string:h,array:function(e){return u(e,new Array(e.length))},arraybuffer:function(e){return e.buffer},uint8array:o,nodebuffer:function(e){return s.newBufferFrom(e)}},d.nodebuffer={string:h,array:function(e){return u(e,new Array(e.length))},arraybuffer:function(e){return d.nodebuffer.uint8array(e).buffer},uint8array:function(e){return u(e,new Uint8Array(e.length))},nodebuffer:o},r.transformTo=function(e,t){if(t=t||"",!e)return t;r.checkSupport(e);var n=r.getTypeOf(t);return d[n][e](t)},r.resolve=function(e){for(var t=e.split("/"),r=[],n=0;n<t.length;n++){var a=t[n];"."===a||""===a&&0!==n&&n!==t.length-1||(".."===a?r.pop():r.push(a))}return r.join("/")},r.getTypeOf=function(e){return"string"==typeof e?"string":"[object Array]"===Object.prototype.toString.call(e)?"array":n.nodebuffer&&s.isBuffer(e)?"nodebuffer":n.uint8array&&e instanceof Uint8Array?"uint8array":n.arraybuffer&&e instanceof ArrayBuffer?"arraybuffer":void 0},r.checkSupport=function(e){if(!n[e.toLowerCase()])throw new Error(e+" is not supported by this platform")},r.MAX_VALUE_16BITS=65535,r.MAX_VALUE_32BITS=-1,r.pretty=function(e){var t,r,n="";for(r=0;r<(e||"").length;r++)n+="\\x"+((t=e.charCodeAt(r))<16?"0":"")+t.toString(16).toUpperCase();return n},r.delay=function(e,t,r){setImmediate(function(){e.apply(r||null,t||[])})},r.inherits=function(e,t){function r(){}r.prototype=t.prototype,e.prototype=new r},r.extend=function(){var e,t,r={};for(e=0;e<arguments.length;e++)for(t in arguments[e])Object.prototype.hasOwnProperty.call(arguments[e],t)&&void 0===r[t]&&(r[t]=arguments[e][t]);return r},r.prepareContent=function(e,t,s,o,c){return i.Promise.resolve(t).then(function(e){return n.blob&&(e instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(e)))&&"undefined"!=typeof FileReader?new i.Promise(function(t,r){var n=new FileReader;n.onload=function(e){t(e.target.result)},n.onerror=function(e){r(e.target.error)},n.readAsArrayBuffer(e)}):e}).then(function(t){var h,u=r.getTypeOf(t);return u?("arraybuffer"===u?t=r.transformTo("uint8array",t):"string"===u&&(c?t=a.decode(t):s&&!0!==o&&(t=l(h=t,n.uint8array?new Uint8Array(h.length):new Array(h.length)))),t):i.Promise.reject(new Error("Can't read the data of '"+e+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))})}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,setimmediate:54}],33:[function(e,t,r){var n=e("./reader/readerFor"),a=e("./utils"),s=e("./signature"),i=e("./zipEntry"),o=e("./support");function l(e){this.files=[],this.loadOptions=e}l.prototype={checkSignature:function(e){if(!this.reader.readAndCheckSignature(e)){this.reader.index-=4;var t=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+a.pretty(t)+", expected "+a.pretty(e)+")")}},isSignature:function(e,t){var r=this.reader.index;this.reader.setIndex(e);var n=this.reader.readString(4)===t;return this.reader.setIndex(r),n},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var e=this.reader.readData(this.zipCommentLength),t=o.uint8array?"uint8array":"array",r=a.transformTo(t,e);this.zipComment=this.loadOptions.decodeFileName(r)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var e,t,r,n=this.zip64EndOfCentralSize-44;0<n;)e=this.reader.readInt(2),t=this.reader.readInt(4),r=this.reader.readData(t),this.zip64ExtensibleData[e]={id:e,length:t,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var e,t;for(e=0;e<this.files.length;e++)t=this.files[e],this.reader.setIndex(t.localHeaderOffset),this.checkSignature(s.LOCAL_FILE_HEADER),t.readLocalPart(this.reader),t.handleUTF8(),t.processAttributes()},readCentralDir:function(){var e;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(s.CENTRAL_FILE_HEADER);)(e=new i({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(e);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var e=this.reader.lastIndexOfSignature(s.CENTRAL_DIRECTORY_END);if(e<0)throw this.isSignature(0,s.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(e);var t=e;if(this.checkSignature(s.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===a.MAX_VALUE_16BITS||this.diskWithCentralDirStart===a.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===a.MAX_VALUE_16BITS||this.centralDirRecords===a.MAX_VALUE_16BITS||this.centralDirSize===a.MAX_VALUE_32BITS||this.centralDirOffset===a.MAX_VALUE_32BITS){if(this.zip64=!0,(e=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(e),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,s.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var r=this.centralDirOffset+this.centralDirSize;this.zip64&&(r+=20,r+=12+this.zip64EndOfCentralSize);var n=t-r;if(0<n)this.isSignature(t,s.CENTRAL_FILE_HEADER)||(this.reader.zero=n);else if(n<0)throw new Error("Corrupted zip: missing "+Math.abs(n)+" bytes.")},prepareReader:function(e){this.reader=n(e)},load:function(e){this.prepareReader(e),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},t.exports=l},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utils":32,"./zipEntry":34}],34:[function(e,t,r){var n=e("./reader/readerFor"),a=e("./utils"),s=e("./compressedObject"),i=e("./crc32"),o=e("./utf8"),l=e("./compressions"),c=e("./support");function h(e,t){this.options=e,this.loadOptions=t}h.prototype={isEncrypted:function(){return!(1&~this.bitFlag)},useUTF8:function(){return!(2048&~this.bitFlag)},readLocalPart:function(e){var t,r;if(e.skip(22),this.fileNameLength=e.readInt(2),r=e.readInt(2),this.fileName=e.readData(this.fileNameLength),e.skip(r),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(t=function(e){for(var t in l)if(Object.prototype.hasOwnProperty.call(l,t)&&l[t].magic===e)return l[t];return null}(this.compressionMethod)))throw new Error("Corrupted zip : compression "+a.pretty(this.compressionMethod)+" unknown (inner file : "+a.transformTo("string",this.fileName)+")");this.decompressed=new s(this.compressedSize,this.uncompressedSize,this.crc32,t,e.readData(this.compressedSize))},readCentralPart:function(e){this.versionMadeBy=e.readInt(2),e.skip(2),this.bitFlag=e.readInt(2),this.compressionMethod=e.readString(2),this.date=e.readDate(),this.crc32=e.readInt(4),this.compressedSize=e.readInt(4),this.uncompressedSize=e.readInt(4);var t=e.readInt(2);if(this.extraFieldsLength=e.readInt(2),this.fileCommentLength=e.readInt(2),this.diskNumberStart=e.readInt(2),this.internalFileAttributes=e.readInt(2),this.externalFileAttributes=e.readInt(4),this.localHeaderOffset=e.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");e.skip(t),this.readExtraFields(e),this.parseZIP64ExtraField(e),this.fileComment=e.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var e=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0==e&&(this.dosPermissions=63&this.externalFileAttributes),3==e&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var e=n(this.extraFields[1].value);this.uncompressedSize===a.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===a.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===a.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===a.MAX_VALUE_32BITS&&(this.diskNumberStart=e.readInt(4))}},readExtraFields:function(e){var t,r,n,a=e.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});e.index+4<a;)t=e.readInt(2),r=e.readInt(2),n=e.readData(r),this.extraFields[t]={id:t,length:r,value:n};e.setIndex(a)},handleUTF8:function(){var e=c.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=o.utf8decode(this.fileName),this.fileCommentStr=o.utf8decode(this.fileComment);else{var t=this.findExtraFieldUnicodePath();if(null!==t)this.fileNameStr=t;else{var r=a.transformTo(e,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(r)}var n=this.findExtraFieldUnicodeComment();if(null!==n)this.fileCommentStr=n;else{var s=a.transformTo(e,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(s)}}},findExtraFieldUnicodePath:function(){var e=this.extraFields[28789];if(e){var t=n(e.value);return 1!==t.readInt(1)||i(this.fileName)!==t.readInt(4)?null:o.utf8decode(t.readData(e.length-5))}return null},findExtraFieldUnicodeComment:function(){var e=this.extraFields[25461];if(e){var t=n(e.value);return 1!==t.readInt(1)||i(this.fileComment)!==t.readInt(4)?null:o.utf8decode(t.readData(e.length-5))}return null}},t.exports=h},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(e,t,r){function n(e,t,r){this.name=e,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=t,this._dataBinary=r.binary,this.options={compression:r.compression,compressionOptions:r.compressionOptions}}var a=e("./stream/StreamHelper"),s=e("./stream/DataWorker"),i=e("./utf8"),o=e("./compressedObject"),l=e("./stream/GenericWorker");n.prototype={internalStream:function(e){var t=null,r="string";try{if(!e)throw new Error("No output type specified.");var n="string"===(r=e.toLowerCase())||"text"===r;"binarystring"!==r&&"text"!==r||(r="string"),t=this._decompressWorker();var s=!this._dataBinary;s&&!n&&(t=t.pipe(new i.Utf8EncodeWorker)),!s&&n&&(t=t.pipe(new i.Utf8DecodeWorker))}catch(o){(t=new l("error")).error(o)}return new a(t,r,"")},async:function(e,t){return this.internalStream(e).accumulate(t)},nodeStream:function(e,t){return this.internalStream(e||"nodebuffer").toNodejsStream(t)},_compressWorker:function(e,t){if(this._data instanceof o&&this._data.compression.magic===e.magic)return this._data.getCompressedWorker();var r=this._decompressWorker();return this._dataBinary||(r=r.pipe(new i.Utf8EncodeWorker)),o.createWorkerFrom(r,e,t)},_decompressWorker:function(){return this._data instanceof o?this._data.getContentWorker():this._data instanceof l?this._data:new s(this._data)}};for(var c=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],h=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},u=0;u<c.length;u++)n.prototype[c[u]]=h;t.exports=n},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(t,r,n){(function(e){var t,n,a=e.MutationObserver||e.WebKitMutationObserver;if(a){var s=0,i=new a(h),o=e.document.createTextNode("");i.observe(o,{characterData:!0}),t=function(){o.data=s=++s%2}}else if(e.setImmediate||void 0===e.MessageChannel)t="document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var t=e.document.createElement("script");t.onreadystatechange=function(){h(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t)}:function(){setTimeout(h,0)};else{var l=new e.MessageChannel;l.port1.onmessage=h,t=function(){l.port2.postMessage(0)}}var c=[];function h(){var e,t;n=!0;for(var r=c.length;r;){for(t=c,c=[],e=-1;++e<r;)t[e]();r=c.length}n=!1}r.exports=function(e){1!==c.push(e)||n||t()}}).call(this,void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],37:[function(e,t,r){var n=e("immediate");function a(){}var s={},i=["REJECTED"],o=["FULFILLED"],l=["PENDING"];function c(e){if("function"!=typeof e)throw new TypeError("resolver must be a function");this.state=l,this.queue=[],this.outcome=void 0,e!==a&&f(this,e)}function h(e,t,r){this.promise=e,"function"==typeof t&&(this.onFulfilled=t,this.callFulfilled=this.otherCallFulfilled),"function"==typeof r&&(this.onRejected=r,this.callRejected=this.otherCallRejected)}function u(e,t,r){n(function(){var n;try{n=t(r)}catch(a){return s.reject(e,a)}n===e?s.reject(e,new TypeError("Cannot resolve promise with itself")):s.resolve(e,n)})}function d(e){var t=e&&e.then;if(e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof t)return function(){t.apply(e,arguments)}}function f(e,t){var r=!1;function n(t){r||(r=!0,s.reject(e,t))}function a(t){r||(r=!0,s.resolve(e,t))}var i=p(function(){t(a,n)});"error"===i.status&&n(i.value)}function p(e,t){var r={};try{r.value=e(t),r.status="success"}catch(n){r.status="error",r.value=n}return r}(t.exports=c).prototype.finally=function(e){if("function"!=typeof e)return this;var t=this.constructor;return this.then(function(r){return t.resolve(e()).then(function(){return r})},function(r){return t.resolve(e()).then(function(){throw r})})},c.prototype.catch=function(e){return this.then(null,e)},c.prototype.then=function(e,t){if("function"!=typeof e&&this.state===o||"function"!=typeof t&&this.state===i)return this;var r=new this.constructor(a);return this.state!==l?u(r,this.state===o?e:t,this.outcome):this.queue.push(new h(r,e,t)),r},h.prototype.callFulfilled=function(e){s.resolve(this.promise,e)},h.prototype.otherCallFulfilled=function(e){u(this.promise,this.onFulfilled,e)},h.prototype.callRejected=function(e){s.reject(this.promise,e)},h.prototype.otherCallRejected=function(e){u(this.promise,this.onRejected,e)},s.resolve=function(e,t){var r=p(d,t);if("error"===r.status)return s.reject(e,r.value);var n=r.value;if(n)f(e,n);else{e.state=o,e.outcome=t;for(var a=-1,i=e.queue.length;++a<i;)e.queue[a].callFulfilled(t)}return e},s.reject=function(e,t){e.state=i,e.outcome=t;for(var r=-1,n=e.queue.length;++r<n;)e.queue[r].callRejected(t);return e},c.resolve=function(e){return e instanceof this?e:s.resolve(new this(a),e)},c.reject=function(e){var t=new this(a);return s.reject(t,e)},c.all=function(e){var t=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var r=e.length,n=!1;if(!r)return this.resolve([]);for(var i=new Array(r),o=0,l=-1,c=new this(a);++l<r;)h(e[l],l);return c;function h(e,a){t.resolve(e).then(function(e){i[a]=e,++o!==r||n||(n=!0,s.resolve(c,i))},function(e){n||(n=!0,s.reject(c,e))})}},c.race=function(e){var t=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var r=e.length,n=!1;if(!r)return this.resolve([]);for(var i,o=-1,l=new this(a);++o<r;)i=e[o],t.resolve(i).then(function(e){n||(n=!0,s.resolve(l,e))},function(e){n||(n=!0,s.reject(l,e))});return l}},{immediate:36}],38:[function(e,t,r){var n={};(0,e("./lib/utils/common").assign)(n,e("./lib/deflate"),e("./lib/inflate"),e("./lib/zlib/constants")),t.exports=n},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(e,t,r){var n=e("./zlib/deflate"),a=e("./utils/common"),s=e("./utils/strings"),i=e("./zlib/messages"),o=e("./zlib/zstream"),l=Object.prototype.toString,c=0,h=-1,u=0,d=8;function f(e){if(!(this instanceof f))return new f(e);this.options=a.assign({level:h,method:d,chunkSize:16384,windowBits:15,memLevel:8,strategy:u,to:""},e||{});var t=this.options;t.raw&&0<t.windowBits?t.windowBits=-t.windowBits:t.gzip&&0<t.windowBits&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new o,this.strm.avail_out=0;var r=n.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(r!==c)throw new Error(i[r]);if(t.header&&n.deflateSetHeader(this.strm,t.header),t.dictionary){var p;if(p="string"==typeof t.dictionary?s.string2buf(t.dictionary):"[object ArrayBuffer]"===l.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(r=n.deflateSetDictionary(this.strm,p))!==c)throw new Error(i[r]);this._dict_set=!0}}function p(e,t){var r=new f(t);if(r.push(e,!0),r.err)throw r.msg||i[r.err];return r.result}f.prototype.push=function(e,t){var r,i,o=this.strm,h=this.options.chunkSize;if(this.ended)return!1;i=t===~~t?t:!0===t?4:0,"string"==typeof e?o.input=s.string2buf(e):"[object ArrayBuffer]"===l.call(e)?o.input=new Uint8Array(e):o.input=e,o.next_in=0,o.avail_in=o.input.length;do{if(0===o.avail_out&&(o.output=new a.Buf8(h),o.next_out=0,o.avail_out=h),1!==(r=n.deflate(o,i))&&r!==c)return this.onEnd(r),!(this.ended=!0);0!==o.avail_out&&(0!==o.avail_in||4!==i&&2!==i)||("string"===this.options.to?this.onData(s.buf2binstring(a.shrinkBuf(o.output,o.next_out))):this.onData(a.shrinkBuf(o.output,o.next_out)))}while((0<o.avail_in||0===o.avail_out)&&1!==r);return 4===i?(r=n.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===c):2!==i||(this.onEnd(c),!(o.avail_out=0))},f.prototype.onData=function(e){this.chunks.push(e)},f.prototype.onEnd=function(e){e===c&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=a.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Deflate=f,r.deflate=p,r.deflateRaw=function(e,t){return(t=t||{}).raw=!0,p(e,t)},r.gzip=function(e,t){return(t=t||{}).gzip=!0,p(e,t)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(e,t,r){var n=e("./zlib/inflate"),a=e("./utils/common"),s=e("./utils/strings"),i=e("./zlib/constants"),o=e("./zlib/messages"),l=e("./zlib/zstream"),c=e("./zlib/gzheader"),h=Object.prototype.toString;function u(e){if(!(this instanceof u))return new u(e);this.options=a.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&0<=t.windowBits&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(0<=t.windowBits&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),15<t.windowBits&&t.windowBits<48&&!(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var r=n.inflateInit2(this.strm,t.windowBits);if(r!==i.Z_OK)throw new Error(o[r]);this.header=new c,n.inflateGetHeader(this.strm,this.header)}function d(e,t){var r=new u(t);if(r.push(e,!0),r.err)throw r.msg||o[r.err];return r.result}u.prototype.push=function(e,t){var r,o,l,c,u,d,f=this.strm,p=this.options.chunkSize,m=this.options.dictionary,g=!1;if(this.ended)return!1;o=t===~~t?t:!0===t?i.Z_FINISH:i.Z_NO_FLUSH,"string"==typeof e?f.input=s.binstring2buf(e):"[object ArrayBuffer]"===h.call(e)?f.input=new Uint8Array(e):f.input=e,f.next_in=0,f.avail_in=f.input.length;do{if(0===f.avail_out&&(f.output=new a.Buf8(p),f.next_out=0,f.avail_out=p),(r=n.inflate(f,i.Z_NO_FLUSH))===i.Z_NEED_DICT&&m&&(d="string"==typeof m?s.string2buf(m):"[object ArrayBuffer]"===h.call(m)?new Uint8Array(m):m,r=n.inflateSetDictionary(this.strm,d)),r===i.Z_BUF_ERROR&&!0===g&&(r=i.Z_OK,g=!1),r!==i.Z_STREAM_END&&r!==i.Z_OK)return this.onEnd(r),!(this.ended=!0);f.next_out&&(0!==f.avail_out&&r!==i.Z_STREAM_END&&(0!==f.avail_in||o!==i.Z_FINISH&&o!==i.Z_SYNC_FLUSH)||("string"===this.options.to?(l=s.utf8border(f.output,f.next_out),c=f.next_out-l,u=s.buf2string(f.output,l),f.next_out=c,f.avail_out=p-c,c&&a.arraySet(f.output,f.output,l,c,0),this.onData(u)):this.onData(a.shrinkBuf(f.output,f.next_out)))),0===f.avail_in&&0===f.avail_out&&(g=!0)}while((0<f.avail_in||0===f.avail_out)&&r!==i.Z_STREAM_END);return r===i.Z_STREAM_END&&(o=i.Z_FINISH),o===i.Z_FINISH?(r=n.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===i.Z_OK):o!==i.Z_SYNC_FLUSH||(this.onEnd(i.Z_OK),!(f.avail_out=0))},u.prototype.onData=function(e){this.chunks.push(e)},u.prototype.onEnd=function(e){e===i.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=a.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Inflate=u,r.inflate=d,r.inflateRaw=function(e,t){return(t=t||{}).raw=!0,d(e,t)},r.ungzip=d},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(e,t,r){var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;r.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var n in r)r.hasOwnProperty(n)&&(e[n]=r[n])}}return e},r.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var a={arraySet:function(e,t,r,n,a){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),a);else for(var s=0;s<n;s++)e[a+s]=t[r+s]},flattenChunks:function(e){var t,r,n,a,s,i;for(t=n=0,r=e.length;t<r;t++)n+=e[t].length;for(i=new Uint8Array(n),t=a=0,r=e.length;t<r;t++)s=e[t],i.set(s,a),a+=s.length;return i}},s={arraySet:function(e,t,r,n,a){for(var s=0;s<n;s++)e[a+s]=t[r+s]},flattenChunks:function(e){return[].concat.apply([],e)}};r.setTyped=function(e){e?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,a)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,s))},r.setTyped(n)},{}],42:[function(e,t,r){var n=e("./common"),a=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(c){a=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(c){s=!1}for(var i=new n.Buf8(256),o=0;o<256;o++)i[o]=252<=o?6:248<=o?5:240<=o?4:224<=o?3:192<=o?2:1;function l(e,t){if(t<65537&&(e.subarray&&s||!e.subarray&&a))return String.fromCharCode.apply(null,n.shrinkBuf(e,t));for(var r="",i=0;i<t;i++)r+=String.fromCharCode(e[i]);return r}i[254]=i[254]=1,r.string2buf=function(e){var t,r,a,s,i,o=e.length,l=0;for(s=0;s<o;s++)55296==(64512&(r=e.charCodeAt(s)))&&s+1<o&&56320==(64512&(a=e.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(a-56320),s++),l+=r<128?1:r<2048?2:r<65536?3:4;for(t=new n.Buf8(l),s=i=0;i<l;s++)55296==(64512&(r=e.charCodeAt(s)))&&s+1<o&&56320==(64512&(a=e.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(a-56320),s++),r<128?t[i++]=r:(r<2048?t[i++]=192|r>>>6:(r<65536?t[i++]=224|r>>>12:(t[i++]=240|r>>>18,t[i++]=128|r>>>12&63),t[i++]=128|r>>>6&63),t[i++]=128|63&r);return t},r.buf2binstring=function(e){return l(e,e.length)},r.binstring2buf=function(e){for(var t=new n.Buf8(e.length),r=0,a=t.length;r<a;r++)t[r]=e.charCodeAt(r);return t},r.buf2string=function(e,t){var r,n,a,s,o=t||e.length,c=new Array(2*o);for(r=n=0;r<o;)if((a=e[r++])<128)c[n++]=a;else if(4<(s=i[a]))c[n++]=65533,r+=s-1;else{for(a&=2===s?31:3===s?15:7;1<s&&r<o;)a=a<<6|63&e[r++],s--;1<s?c[n++]=65533:a<65536?c[n++]=a:(a-=65536,c[n++]=55296|a>>10&1023,c[n++]=56320|1023&a)}return l(c,n)},r.utf8border=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;0<=r&&128==(192&e[r]);)r--;return r<0||0===r?t:r+i[e[r]]>t?r:t}},{"./common":41}],43:[function(e,t,r){t.exports=function(e,t,r,n){for(var a=65535&e,s=e>>>16&65535,i=0;0!==r;){for(r-=i=2e3<r?2e3:r;s=s+(a=a+t[n++]|0)|0,--i;);a%=65521,s%=65521}return a|s<<16}},{}],44:[function(e,t,r){t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(e,t,r){var n=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t,r,a){var s=n,i=a+r;e^=-1;for(var o=a;o<i;o++)e=e>>>8^s[255&(e^t[o])];return-1^e}},{}],46:[function(e,t,r){var n,a=e("../utils/common"),s=e("./trees"),i=e("./adler32"),o=e("./crc32"),l=e("./messages"),c=0,h=4,u=0,d=-2,f=-1,p=4,m=2,g=8,b=9,y=286,v=30,k=19,_=2*y+1,w=15,S=3,x=258,C=x+S+1,E=42,A=113,P=1,N=2,T=3,z=4;function B(e,t){return e.msg=l[t],t}function R(e){return(e<<1)-(4<e?9:0)}function D(e){for(var t=e.length;0<=--t;)e[t]=0}function I(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(a.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function M(e,t){s._tr_flush_block(e,0<=e.block_start?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,I(e.strm)}function O(e,t){e.pending_buf[e.pending++]=t}function F(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function L(e,t){var r,n,a=e.max_chain_length,s=e.strstart,i=e.prev_length,o=e.nice_match,l=e.strstart>e.w_size-C?e.strstart-(e.w_size-C):0,c=e.window,h=e.w_mask,u=e.prev,d=e.strstart+x,f=c[s+i-1],p=c[s+i];e.prev_length>=e.good_match&&(a>>=2),o>e.lookahead&&(o=e.lookahead);do{if(c[(r=t)+i]===p&&c[r+i-1]===f&&c[r]===c[s]&&c[++r]===c[s+1]){s+=2,r++;do{}while(c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&s<d);if(n=x-(d-s),s=d-x,i<n){if(e.match_start=t,o<=(i=n))break;f=c[s+i-1],p=c[s+i]}}}while((t=u[t&h])>l&&0!=--a);return i<=e.lookahead?i:e.lookahead}function j(e){var t,r,n,s,l,c,h,u,d,f,p=e.w_size;do{if(s=e.window_size-e.lookahead-e.strstart,e.strstart>=p+(p-C)){for(a.arraySet(e.window,e.window,p,p,0),e.match_start-=p,e.strstart-=p,e.block_start-=p,t=r=e.hash_size;n=e.head[--t],e.head[t]=p<=n?n-p:0,--r;);for(t=r=p;n=e.prev[--t],e.prev[t]=p<=n?n-p:0,--r;);s+=p}if(0===e.strm.avail_in)break;if(c=e.strm,h=e.window,u=e.strstart+e.lookahead,f=void 0,(d=s)<(f=c.avail_in)&&(f=d),r=0===f?0:(c.avail_in-=f,a.arraySet(h,c.input,c.next_in,f,u),1===c.state.wrap?c.adler=i(c.adler,h,f,u):2===c.state.wrap&&(c.adler=o(c.adler,h,f,u)),c.next_in+=f,c.total_in+=f,f),e.lookahead+=r,e.lookahead+e.insert>=S)for(l=e.strstart-e.insert,e.ins_h=e.window[l],e.ins_h=(e.ins_h<<e.hash_shift^e.window[l+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[l+S-1])&e.hash_mask,e.prev[l&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=l,l++,e.insert--,!(e.lookahead+e.insert<S)););}while(e.lookahead<C&&0!==e.strm.avail_in)}function U(e,t){for(var r,n;;){if(e.lookahead<C){if(j(e),e.lookahead<C&&t===c)return P;if(0===e.lookahead)break}if(r=0,e.lookahead>=S&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+S-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-C&&(e.match_length=L(e,r)),e.match_length>=S)if(n=s._tr_tally(e,e.strstart-e.match_start,e.match_length-S),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=S){for(e.match_length--;e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+S-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart,0!=--e.match_length;);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else n=s._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(M(e,!1),0===e.strm.avail_out))return P}return e.insert=e.strstart<S-1?e.strstart:S-1,t===h?(M(e,!0),0===e.strm.avail_out?T:z):e.last_lit&&(M(e,!1),0===e.strm.avail_out)?P:N}function $(e,t){for(var r,n,a;;){if(e.lookahead<C){if(j(e),e.lookahead<C&&t===c)return P;if(0===e.lookahead)break}if(r=0,e.lookahead>=S&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+S-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=S-1,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-C&&(e.match_length=L(e,r),e.match_length<=5&&(1===e.strategy||e.match_length===S&&4096<e.strstart-e.match_start)&&(e.match_length=S-1)),e.prev_length>=S&&e.match_length<=e.prev_length){for(a=e.strstart+e.lookahead-S,n=s._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-S),e.lookahead-=e.prev_length-1,e.prev_length-=2;++e.strstart<=a&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+S-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!=--e.prev_length;);if(e.match_available=0,e.match_length=S-1,e.strstart++,n&&(M(e,!1),0===e.strm.avail_out))return P}else if(e.match_available){if((n=s._tr_tally(e,0,e.window[e.strstart-1]))&&M(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return P}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(n=s._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<S-1?e.strstart:S-1,t===h?(M(e,!0),0===e.strm.avail_out?T:z):e.last_lit&&(M(e,!1),0===e.strm.avail_out)?P:N}function W(e,t,r,n,a){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=a}function H(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=g,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new a.Buf16(2*_),this.dyn_dtree=new a.Buf16(2*(2*v+1)),this.bl_tree=new a.Buf16(2*(2*k+1)),D(this.dyn_ltree),D(this.dyn_dtree),D(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new a.Buf16(w+1),this.heap=new a.Buf16(2*y+1),D(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new a.Buf16(2*y+1),D(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function Z(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=m,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?E:A,e.adler=2===t.wrap?0:1,t.last_flush=c,s._tr_init(t),u):B(e,d)}function V(e){var t,r=Z(e);return r===u&&((t=e.state).window_size=2*t.w_size,D(t.head),t.max_lazy_match=n[t.level].max_lazy,t.good_match=n[t.level].good_length,t.nice_match=n[t.level].nice_length,t.max_chain_length=n[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=S-1,t.match_available=0,t.ins_h=0),r}function G(e,t,r,n,s,i){if(!e)return d;var o=1;if(t===f&&(t=6),n<0?(o=0,n=-n):15<n&&(o=2,n-=16),s<1||b<s||r!==g||n<8||15<n||t<0||9<t||i<0||p<i)return B(e,d);8===n&&(n=9);var l=new H;return(e.state=l).strm=e,l.wrap=o,l.gzhead=null,l.w_bits=n,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=s+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+S-1)/S),l.window=new a.Buf8(2*l.w_size),l.head=new a.Buf16(l.hash_size),l.prev=new a.Buf16(l.w_size),l.lit_bufsize=1<<s+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new a.Buf8(l.pending_buf_size),l.d_buf=1*l.lit_bufsize,l.l_buf=3*l.lit_bufsize,l.level=t,l.strategy=i,l.method=r,V(e)}n=[new W(0,0,0,0,function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(j(e),0===e.lookahead&&t===c)return P;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,M(e,!1),0===e.strm.avail_out))return P;if(e.strstart-e.block_start>=e.w_size-C&&(M(e,!1),0===e.strm.avail_out))return P}return e.insert=0,t===h?(M(e,!0),0===e.strm.avail_out?T:z):(e.strstart>e.block_start&&(M(e,!1),e.strm.avail_out),P)}),new W(4,4,8,4,U),new W(4,5,16,8,U),new W(4,6,32,32,U),new W(4,4,16,16,$),new W(8,16,32,32,$),new W(8,16,128,128,$),new W(8,32,128,256,$),new W(32,128,258,1024,$),new W(32,258,258,4096,$)],r.deflateInit=function(e,t){return G(e,t,g,15,8,0)},r.deflateInit2=G,r.deflateReset=V,r.deflateResetKeep=Z,r.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?d:(e.state.gzhead=t,u):d},r.deflate=function(e,t){var r,a,i,l;if(!e||!e.state||5<t||t<0)return e?B(e,d):d;if(a=e.state,!e.output||!e.input&&0!==e.avail_in||666===a.status&&t!==h)return B(e,0===e.avail_out?-5:d);if(a.strm=e,r=a.last_flush,a.last_flush=t,a.status===E)if(2===a.wrap)e.adler=0,O(a,31),O(a,139),O(a,8),a.gzhead?(O(a,(a.gzhead.text?1:0)+(a.gzhead.hcrc?2:0)+(a.gzhead.extra?4:0)+(a.gzhead.name?8:0)+(a.gzhead.comment?16:0)),O(a,255&a.gzhead.time),O(a,a.gzhead.time>>8&255),O(a,a.gzhead.time>>16&255),O(a,a.gzhead.time>>24&255),O(a,9===a.level?2:2<=a.strategy||a.level<2?4:0),O(a,255&a.gzhead.os),a.gzhead.extra&&a.gzhead.extra.length&&(O(a,255&a.gzhead.extra.length),O(a,a.gzhead.extra.length>>8&255)),a.gzhead.hcrc&&(e.adler=o(e.adler,a.pending_buf,a.pending,0)),a.gzindex=0,a.status=69):(O(a,0),O(a,0),O(a,0),O(a,0),O(a,0),O(a,9===a.level?2:2<=a.strategy||a.level<2?4:0),O(a,3),a.status=A);else{var f=g+(a.w_bits-8<<4)<<8;f|=(2<=a.strategy||a.level<2?0:a.level<6?1:6===a.level?2:3)<<6,0!==a.strstart&&(f|=32),f+=31-f%31,a.status=A,F(a,f),0!==a.strstart&&(F(a,e.adler>>>16),F(a,65535&e.adler)),e.adler=1}if(69===a.status)if(a.gzhead.extra){for(i=a.pending;a.gzindex<(65535&a.gzhead.extra.length)&&(a.pending!==a.pending_buf_size||(a.gzhead.hcrc&&a.pending>i&&(e.adler=o(e.adler,a.pending_buf,a.pending-i,i)),I(e),i=a.pending,a.pending!==a.pending_buf_size));)O(a,255&a.gzhead.extra[a.gzindex]),a.gzindex++;a.gzhead.hcrc&&a.pending>i&&(e.adler=o(e.adler,a.pending_buf,a.pending-i,i)),a.gzindex===a.gzhead.extra.length&&(a.gzindex=0,a.status=73)}else a.status=73;if(73===a.status)if(a.gzhead.name){i=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>i&&(e.adler=o(e.adler,a.pending_buf,a.pending-i,i)),I(e),i=a.pending,a.pending===a.pending_buf_size)){l=1;break}l=a.gzindex<a.gzhead.name.length?255&a.gzhead.name.charCodeAt(a.gzindex++):0,O(a,l)}while(0!==l);a.gzhead.hcrc&&a.pending>i&&(e.adler=o(e.adler,a.pending_buf,a.pending-i,i)),0===l&&(a.gzindex=0,a.status=91)}else a.status=91;if(91===a.status)if(a.gzhead.comment){i=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>i&&(e.adler=o(e.adler,a.pending_buf,a.pending-i,i)),I(e),i=a.pending,a.pending===a.pending_buf_size)){l=1;break}l=a.gzindex<a.gzhead.comment.length?255&a.gzhead.comment.charCodeAt(a.gzindex++):0,O(a,l)}while(0!==l);a.gzhead.hcrc&&a.pending>i&&(e.adler=o(e.adler,a.pending_buf,a.pending-i,i)),0===l&&(a.status=103)}else a.status=103;if(103===a.status&&(a.gzhead.hcrc?(a.pending+2>a.pending_buf_size&&I(e),a.pending+2<=a.pending_buf_size&&(O(a,255&e.adler),O(a,e.adler>>8&255),e.adler=0,a.status=A)):a.status=A),0!==a.pending){if(I(e),0===e.avail_out)return a.last_flush=-1,u}else if(0===e.avail_in&&R(t)<=R(r)&&t!==h)return B(e,-5);if(666===a.status&&0!==e.avail_in)return B(e,-5);if(0!==e.avail_in||0!==a.lookahead||t!==c&&666!==a.status){var p=2===a.strategy?function(e,t){for(var r;;){if(0===e.lookahead&&(j(e),0===e.lookahead)){if(t===c)return P;break}if(e.match_length=0,r=s._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(M(e,!1),0===e.strm.avail_out))return P}return e.insert=0,t===h?(M(e,!0),0===e.strm.avail_out?T:z):e.last_lit&&(M(e,!1),0===e.strm.avail_out)?P:N}(a,t):3===a.strategy?function(e,t){for(var r,n,a,i,o=e.window;;){if(e.lookahead<=x){if(j(e),e.lookahead<=x&&t===c)return P;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=S&&0<e.strstart&&(n=o[a=e.strstart-1])===o[++a]&&n===o[++a]&&n===o[++a]){i=e.strstart+x;do{}while(n===o[++a]&&n===o[++a]&&n===o[++a]&&n===o[++a]&&n===o[++a]&&n===o[++a]&&n===o[++a]&&n===o[++a]&&a<i);e.match_length=x-(i-a),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=S?(r=s._tr_tally(e,1,e.match_length-S),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=s._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(M(e,!1),0===e.strm.avail_out))return P}return e.insert=0,t===h?(M(e,!0),0===e.strm.avail_out?T:z):e.last_lit&&(M(e,!1),0===e.strm.avail_out)?P:N}(a,t):n[a.level].func(a,t);if(p!==T&&p!==z||(a.status=666),p===P||p===T)return 0===e.avail_out&&(a.last_flush=-1),u;if(p===N&&(1===t?s._tr_align(a):5!==t&&(s._tr_stored_block(a,0,0,!1),3===t&&(D(a.head),0===a.lookahead&&(a.strstart=0,a.block_start=0,a.insert=0))),I(e),0===e.avail_out))return a.last_flush=-1,u}return t!==h?u:a.wrap<=0?1:(2===a.wrap?(O(a,255&e.adler),O(a,e.adler>>8&255),O(a,e.adler>>16&255),O(a,e.adler>>24&255),O(a,255&e.total_in),O(a,e.total_in>>8&255),O(a,e.total_in>>16&255),O(a,e.total_in>>24&255)):(F(a,e.adler>>>16),F(a,65535&e.adler)),I(e),0<a.wrap&&(a.wrap=-a.wrap),0!==a.pending?u:1)},r.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==E&&69!==t&&73!==t&&91!==t&&103!==t&&t!==A&&666!==t?B(e,d):(e.state=null,t===A?B(e,-3):u):d},r.deflateSetDictionary=function(e,t){var r,n,s,o,l,c,h,f,p=t.length;if(!e||!e.state)return d;if(2===(o=(r=e.state).wrap)||1===o&&r.status!==E||r.lookahead)return d;for(1===o&&(e.adler=i(e.adler,t,p,0)),r.wrap=0,p>=r.w_size&&(0===o&&(D(r.head),r.strstart=0,r.block_start=0,r.insert=0),f=new a.Buf8(r.w_size),a.arraySet(f,t,p-r.w_size,r.w_size,0),t=f,p=r.w_size),l=e.avail_in,c=e.next_in,h=e.input,e.avail_in=p,e.next_in=0,e.input=t,j(r);r.lookahead>=S;){for(n=r.strstart,s=r.lookahead-(S-1);r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+S-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++,--s;);r.strstart=n,r.lookahead=S-1,j(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=S-1,r.match_available=0,e.next_in=c,e.input=h,e.avail_in=l,r.wrap=o,u},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(e,t,r){t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(e,t,r){t.exports=function(e,t){var r,n,a,s,i,o,l,c,h,u,d,f,p,m,g,b,y,v,k,_,w,S,x,C,E;r=e.state,n=e.next_in,C=e.input,a=n+(e.avail_in-5),s=e.next_out,E=e.output,i=s-(t-e.avail_out),o=s+(e.avail_out-257),l=r.dmax,c=r.wsize,h=r.whave,u=r.wnext,d=r.window,f=r.hold,p=r.bits,m=r.lencode,g=r.distcode,b=(1<<r.lenbits)-1,y=(1<<r.distbits)-1;e:do{p<15&&(f+=C[n++]<<p,p+=8,f+=C[n++]<<p,p+=8),v=m[f&b];t:for(;;){if(f>>>=k=v>>>24,p-=k,0==(k=v>>>16&255))E[s++]=65535&v;else{if(!(16&k)){if(!(64&k)){v=m[(65535&v)+(f&(1<<k)-1)];continue t}if(32&k){r.mode=12;break e}e.msg="invalid literal/length code",r.mode=30;break e}_=65535&v,(k&=15)&&(p<k&&(f+=C[n++]<<p,p+=8),_+=f&(1<<k)-1,f>>>=k,p-=k),p<15&&(f+=C[n++]<<p,p+=8,f+=C[n++]<<p,p+=8),v=g[f&y];r:for(;;){if(f>>>=k=v>>>24,p-=k,!(16&(k=v>>>16&255))){if(!(64&k)){v=g[(65535&v)+(f&(1<<k)-1)];continue r}e.msg="invalid distance code",r.mode=30;break e}if(w=65535&v,p<(k&=15)&&(f+=C[n++]<<p,(p+=8)<k&&(f+=C[n++]<<p,p+=8)),l<(w+=f&(1<<k)-1)){e.msg="invalid distance too far back",r.mode=30;break e}if(f>>>=k,p-=k,(k=s-i)<w){if(h<(k=w-k)&&r.sane){e.msg="invalid distance too far back",r.mode=30;break e}if(x=d,(S=0)===u){if(S+=c-k,k<_){for(_-=k;E[s++]=d[S++],--k;);S=s-w,x=E}}else if(u<k){if(S+=c+u-k,(k-=u)<_){for(_-=k;E[s++]=d[S++],--k;);if(S=0,u<_){for(_-=k=u;E[s++]=d[S++],--k;);S=s-w,x=E}}}else if(S+=u-k,k<_){for(_-=k;E[s++]=d[S++],--k;);S=s-w,x=E}for(;2<_;)E[s++]=x[S++],E[s++]=x[S++],E[s++]=x[S++],_-=3;_&&(E[s++]=x[S++],1<_&&(E[s++]=x[S++]))}else{for(S=s-w;E[s++]=E[S++],E[s++]=E[S++],E[s++]=E[S++],2<(_-=3););_&&(E[s++]=E[S++],1<_&&(E[s++]=E[S++]))}break}}break}}while(n<a&&s<o);n-=_=p>>3,f&=(1<<(p-=_<<3))-1,e.next_in=n,e.next_out=s,e.avail_in=n<a?a-n+5:5-(n-a),e.avail_out=s<o?o-s+257:257-(s-o),r.hold=f,r.bits=p}},{}],49:[function(e,t,r){var n=e("../utils/common"),a=e("./adler32"),s=e("./crc32"),i=e("./inffast"),o=e("./inftrees"),l=1,c=2,h=0,u=-2,d=1,f=852,p=592;function m(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function g(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new n.Buf16(320),this.work=new n.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function b(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=d,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new n.Buf32(f),t.distcode=t.distdyn=new n.Buf32(p),t.sane=1,t.back=-1,h):u}function y(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,b(e)):u}function v(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||15<t)?u:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,y(e))):u}function k(e,t){var r,n;return e?(n=new g,(e.state=n).window=null,(r=v(e,t))!==h&&(e.state=null),r):u}var _,w,S=!0;function x(e){if(S){var t;for(_=new n.Buf32(512),w=new n.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(o(l,e.lens,0,288,_,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;o(c,e.lens,0,32,w,0,e.work,{bits:5}),S=!1}e.lencode=_,e.lenbits=9,e.distcode=w,e.distbits=5}function C(e,t,r,a){var s,i=e.state;return null===i.window&&(i.wsize=1<<i.wbits,i.wnext=0,i.whave=0,i.window=new n.Buf8(i.wsize)),a>=i.wsize?(n.arraySet(i.window,t,r-i.wsize,i.wsize,0),i.wnext=0,i.whave=i.wsize):(a<(s=i.wsize-i.wnext)&&(s=a),n.arraySet(i.window,t,r-a,s,i.wnext),(a-=s)?(n.arraySet(i.window,t,r-a,a,0),i.wnext=a,i.whave=i.wsize):(i.wnext+=s,i.wnext===i.wsize&&(i.wnext=0),i.whave<i.wsize&&(i.whave+=s))),0}r.inflateReset=y,r.inflateReset2=v,r.inflateResetKeep=b,r.inflateInit=function(e){return k(e,15)},r.inflateInit2=k,r.inflate=function(e,t){var r,f,p,g,b,y,v,k,_,w,S,E,A,P,N,T,z,B,R,D,I,M,O,F,L=0,j=new n.Buf8(4),U=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return u;12===(r=e.state).mode&&(r.mode=13),b=e.next_out,p=e.output,v=e.avail_out,g=e.next_in,f=e.input,y=e.avail_in,k=r.hold,_=r.bits,w=y,S=v,M=h;e:for(;;)switch(r.mode){case d:if(0===r.wrap){r.mode=13;break}for(;_<16;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}if(2&r.wrap&&35615===k){j[r.check=0]=255&k,j[1]=k>>>8&255,r.check=s(r.check,j,2,0),_=k=0,r.mode=2;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&k)<<8)+(k>>8))%31){e.msg="incorrect header check",r.mode=30;break}if(8!=(15&k)){e.msg="unknown compression method",r.mode=30;break}if(_-=4,I=8+(15&(k>>>=4)),0===r.wbits)r.wbits=I;else if(I>r.wbits){e.msg="invalid window size",r.mode=30;break}r.dmax=1<<I,e.adler=r.check=1,r.mode=512&k?10:12,_=k=0;break;case 2:for(;_<16;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}if(r.flags=k,8!=(255&r.flags)){e.msg="unknown compression method",r.mode=30;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=30;break}r.head&&(r.head.text=k>>8&1),512&r.flags&&(j[0]=255&k,j[1]=k>>>8&255,r.check=s(r.check,j,2,0)),_=k=0,r.mode=3;case 3:for(;_<32;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}r.head&&(r.head.time=k),512&r.flags&&(j[0]=255&k,j[1]=k>>>8&255,j[2]=k>>>16&255,j[3]=k>>>24&255,r.check=s(r.check,j,4,0)),_=k=0,r.mode=4;case 4:for(;_<16;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}r.head&&(r.head.xflags=255&k,r.head.os=k>>8),512&r.flags&&(j[0]=255&k,j[1]=k>>>8&255,r.check=s(r.check,j,2,0)),_=k=0,r.mode=5;case 5:if(1024&r.flags){for(;_<16;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}r.length=k,r.head&&(r.head.extra_len=k),512&r.flags&&(j[0]=255&k,j[1]=k>>>8&255,r.check=s(r.check,j,2,0)),_=k=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&(y<(E=r.length)&&(E=y),E&&(r.head&&(I=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),n.arraySet(r.head.extra,f,g,E,I)),512&r.flags&&(r.check=s(r.check,f,E,g)),y-=E,g+=E,r.length-=E),r.length))break e;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===y)break e;for(E=0;I=f[g+E++],r.head&&I&&r.length<65536&&(r.head.name+=String.fromCharCode(I)),I&&E<y;);if(512&r.flags&&(r.check=s(r.check,f,E,g)),y-=E,g+=E,I)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===y)break e;for(E=0;I=f[g+E++],r.head&&I&&r.length<65536&&(r.head.comment+=String.fromCharCode(I)),I&&E<y;);if(512&r.flags&&(r.check=s(r.check,f,E,g)),y-=E,g+=E,I)break e}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;_<16;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}if(k!==(65535&r.check)){e.msg="header crc mismatch",r.mode=30;break}_=k=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=12;break;case 10:for(;_<32;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}e.adler=r.check=m(k),_=k=0,r.mode=11;case 11:if(0===r.havedict)return e.next_out=b,e.avail_out=v,e.next_in=g,e.avail_in=y,r.hold=k,r.bits=_,2;e.adler=r.check=1,r.mode=12;case 12:if(5===t||6===t)break e;case 13:if(r.last){k>>>=7&_,_-=7&_,r.mode=27;break}for(;_<3;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}switch(r.last=1&k,_-=1,3&(k>>>=1)){case 0:r.mode=14;break;case 1:if(x(r),r.mode=20,6!==t)break;k>>>=2,_-=2;break e;case 2:r.mode=17;break;case 3:e.msg="invalid block type",r.mode=30}k>>>=2,_-=2;break;case 14:for(k>>>=7&_,_-=7&_;_<32;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}if((65535&k)!=(k>>>16^65535)){e.msg="invalid stored block lengths",r.mode=30;break}if(r.length=65535&k,_=k=0,r.mode=15,6===t)break e;case 15:r.mode=16;case 16:if(E=r.length){if(y<E&&(E=y),v<E&&(E=v),0===E)break e;n.arraySet(p,f,g,E,b),y-=E,g+=E,v-=E,b+=E,r.length-=E;break}r.mode=12;break;case 17:for(;_<14;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}if(r.nlen=257+(31&k),k>>>=5,_-=5,r.ndist=1+(31&k),k>>>=5,_-=5,r.ncode=4+(15&k),k>>>=4,_-=4,286<r.nlen||30<r.ndist){e.msg="too many length or distance symbols",r.mode=30;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;_<3;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}r.lens[U[r.have++]]=7&k,k>>>=3,_-=3}for(;r.have<19;)r.lens[U[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,O={bits:r.lenbits},M=o(0,r.lens,0,19,r.lencode,0,r.work,O),r.lenbits=O.bits,M){e.msg="invalid code lengths set",r.mode=30;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;T=(L=r.lencode[k&(1<<r.lenbits)-1])>>>16&255,z=65535&L,!((N=L>>>24)<=_);){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}if(z<16)k>>>=N,_-=N,r.lens[r.have++]=z;else{if(16===z){for(F=N+2;_<F;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}if(k>>>=N,_-=N,0===r.have){e.msg="invalid bit length repeat",r.mode=30;break}I=r.lens[r.have-1],E=3+(3&k),k>>>=2,_-=2}else if(17===z){for(F=N+3;_<F;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}_-=N,I=0,E=3+(7&(k>>>=N)),k>>>=3,_-=3}else{for(F=N+7;_<F;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}_-=N,I=0,E=11+(127&(k>>>=N)),k>>>=7,_-=7}if(r.have+E>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=30;break}for(;E--;)r.lens[r.have++]=I}}if(30===r.mode)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=30;break}if(r.lenbits=9,O={bits:r.lenbits},M=o(l,r.lens,0,r.nlen,r.lencode,0,r.work,O),r.lenbits=O.bits,M){e.msg="invalid literal/lengths set",r.mode=30;break}if(r.distbits=6,r.distcode=r.distdyn,O={bits:r.distbits},M=o(c,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,O),r.distbits=O.bits,M){e.msg="invalid distances set",r.mode=30;break}if(r.mode=20,6===t)break e;case 20:r.mode=21;case 21:if(6<=y&&258<=v){e.next_out=b,e.avail_out=v,e.next_in=g,e.avail_in=y,r.hold=k,r.bits=_,i(e,S),b=e.next_out,p=e.output,v=e.avail_out,g=e.next_in,f=e.input,y=e.avail_in,k=r.hold,_=r.bits,12===r.mode&&(r.back=-1);break}for(r.back=0;T=(L=r.lencode[k&(1<<r.lenbits)-1])>>>16&255,z=65535&L,!((N=L>>>24)<=_);){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}if(T&&!(240&T)){for(B=N,R=T,D=z;T=(L=r.lencode[D+((k&(1<<B+R)-1)>>B)])>>>16&255,z=65535&L,!(B+(N=L>>>24)<=_);){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}k>>>=B,_-=B,r.back+=B}if(k>>>=N,_-=N,r.back+=N,r.length=z,0===T){r.mode=26;break}if(32&T){r.back=-1,r.mode=12;break}if(64&T){e.msg="invalid literal/length code",r.mode=30;break}r.extra=15&T,r.mode=22;case 22:if(r.extra){for(F=r.extra;_<F;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}r.length+=k&(1<<r.extra)-1,k>>>=r.extra,_-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;T=(L=r.distcode[k&(1<<r.distbits)-1])>>>16&255,z=65535&L,!((N=L>>>24)<=_);){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}if(!(240&T)){for(B=N,R=T,D=z;T=(L=r.distcode[D+((k&(1<<B+R)-1)>>B)])>>>16&255,z=65535&L,!(B+(N=L>>>24)<=_);){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}k>>>=B,_-=B,r.back+=B}if(k>>>=N,_-=N,r.back+=N,64&T){e.msg="invalid distance code",r.mode=30;break}r.offset=z,r.extra=15&T,r.mode=24;case 24:if(r.extra){for(F=r.extra;_<F;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}r.offset+=k&(1<<r.extra)-1,k>>>=r.extra,_-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=30;break}r.mode=25;case 25:if(0===v)break e;if(E=S-v,r.offset>E){if((E=r.offset-E)>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=30;break}A=E>r.wnext?(E-=r.wnext,r.wsize-E):r.wnext-E,E>r.length&&(E=r.length),P=r.window}else P=p,A=b-r.offset,E=r.length;for(v<E&&(E=v),v-=E,r.length-=E;p[b++]=P[A++],--E;);0===r.length&&(r.mode=21);break;case 26:if(0===v)break e;p[b++]=r.length,v--,r.mode=21;break;case 27:if(r.wrap){for(;_<32;){if(0===y)break e;y--,k|=f[g++]<<_,_+=8}if(S-=v,e.total_out+=S,r.total+=S,S&&(e.adler=r.check=r.flags?s(r.check,p,S,b-S):a(r.check,p,S,b-S)),S=v,(r.flags?k:m(k))!==r.check){e.msg="incorrect data check",r.mode=30;break}_=k=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;_<32;){if(0===y)break e;y--,k+=f[g++]<<_,_+=8}if(k!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=30;break}_=k=0}r.mode=29;case 29:M=1;break e;case 30:M=-3;break e;case 31:return-4;default:return u}return e.next_out=b,e.avail_out=v,e.next_in=g,e.avail_in=y,r.hold=k,r.bits=_,(r.wsize||S!==e.avail_out&&r.mode<30&&(r.mode<27||4!==t))&&C(e,e.output,e.next_out,S-e.avail_out)?(r.mode=31,-4):(w-=e.avail_in,S-=e.avail_out,e.total_in+=w,e.total_out+=S,r.total+=S,r.wrap&&S&&(e.adler=r.check=r.flags?s(r.check,p,S,e.next_out-S):a(r.check,p,S,e.next_out-S)),e.data_type=r.bits+(r.last?64:0)+(12===r.mode?128:0)+(20===r.mode||15===r.mode?256:0),(0==w&&0===S||4===t)&&M===h&&(M=-5),M)},r.inflateEnd=function(e){if(!e||!e.state)return u;var t=e.state;return t.window&&(t.window=null),e.state=null,h},r.inflateGetHeader=function(e,t){var r;return e&&e.state&&2&(r=e.state).wrap?((r.head=t).done=!1,h):u},r.inflateSetDictionary=function(e,t){var r,n=t.length;return e&&e.state?0!==(r=e.state).wrap&&11!==r.mode?u:11===r.mode&&a(1,t,n,0)!==r.check?-3:C(e,t,n,n)?(r.mode=31,-4):(r.havedict=1,h):u},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(e,t,r){var n=e("../utils/common"),a=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],s=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],i=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],o=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,r,l,c,h,u,d){var f,p,m,g,b,y,v,k,_,w=d.bits,S=0,x=0,C=0,E=0,A=0,P=0,N=0,T=0,z=0,B=0,R=null,D=0,I=new n.Buf16(16),M=new n.Buf16(16),O=null,F=0;for(S=0;S<=15;S++)I[S]=0;for(x=0;x<l;x++)I[t[r+x]]++;for(A=w,E=15;1<=E&&0===I[E];E--);if(E<A&&(A=E),0===E)return c[h++]=20971520,c[h++]=20971520,d.bits=1,0;for(C=1;C<E&&0===I[C];C++);for(A<C&&(A=C),S=T=1;S<=15;S++)if(T<<=1,(T-=I[S])<0)return-1;if(0<T&&(0===e||1!==E))return-1;for(M[1]=0,S=1;S<15;S++)M[S+1]=M[S]+I[S];for(x=0;x<l;x++)0!==t[r+x]&&(u[M[t[r+x]]++]=x);if(y=0===e?(R=O=u,19):1===e?(R=a,D-=257,O=s,F-=257,256):(R=i,O=o,-1),S=C,b=h,N=x=B=0,m=-1,g=(z=1<<(P=A))-1,1===e&&852<z||2===e&&592<z)return 1;for(;;){for(v=S-N,_=u[x]<y?(k=0,u[x]):u[x]>y?(k=O[F+u[x]],R[D+u[x]]):(k=96,0),f=1<<S-N,C=p=1<<P;c[b+(B>>N)+(p-=f)]=v<<24|k<<16|_,0!==p;);for(f=1<<S-1;B&f;)f>>=1;if(0!==f?(B&=f-1,B+=f):B=0,x++,0==--I[S]){if(S===E)break;S=t[r+u[x]]}if(A<S&&(B&g)!==m){for(0===N&&(N=A),b+=C,T=1<<(P=S-N);P+N<E&&!((T-=I[P+N])<=0);)P++,T<<=1;if(z+=1<<P,1===e&&852<z||2===e&&592<z)return 1;c[m=B&g]=A<<24|P<<16|b-h}}return 0!==B&&(c[b+B]=S-N<<24|64<<16),d.bits=A,0}},{"../utils/common":41}],51:[function(e,t,r){t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(e,t,r){var n=e("../utils/common"),a=0,s=1;function i(e){for(var t=e.length;0<=--t;)e[t]=0}var o=0,l=29,c=256,h=c+1+l,u=30,d=19,f=2*h+1,p=15,m=16,g=7,b=256,y=16,v=17,k=18,_=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],w=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],S=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],x=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],C=new Array(2*(h+2));i(C);var E=new Array(2*u);i(E);var A=new Array(512);i(A);var P=new Array(256);i(P);var N=new Array(l);i(N);var T,z,B,R=new Array(u);function D(e,t,r,n,a){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=a,this.has_stree=e&&e.length}function I(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function M(e){return e<256?A[e]:A[256+(e>>>7)]}function O(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function F(e,t,r){e.bi_valid>m-r?(e.bi_buf|=t<<e.bi_valid&65535,O(e,e.bi_buf),e.bi_buf=t>>m-e.bi_valid,e.bi_valid+=r-m):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function L(e,t,r){F(e,r[2*t],r[2*t+1])}function j(e,t){for(var r=0;r|=1&e,e>>>=1,r<<=1,0<--t;);return r>>>1}function U(e,t,r){var n,a,s=new Array(p+1),i=0;for(n=1;n<=p;n++)s[n]=i=i+r[n-1]<<1;for(a=0;a<=t;a++){var o=e[2*a+1];0!==o&&(e[2*a]=j(s[o]++,o))}}function $(e){var t;for(t=0;t<h;t++)e.dyn_ltree[2*t]=0;for(t=0;t<u;t++)e.dyn_dtree[2*t]=0;for(t=0;t<d;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*b]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function W(e){8<e.bi_valid?O(e,e.bi_buf):0<e.bi_valid&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function H(e,t,r,n){var a=2*t,s=2*r;return e[a]<e[s]||e[a]===e[s]&&n[t]<=n[r]}function Z(e,t,r){for(var n=e.heap[r],a=r<<1;a<=e.heap_len&&(a<e.heap_len&&H(t,e.heap[a+1],e.heap[a],e.depth)&&a++,!H(t,n,e.heap[a],e.depth));)e.heap[r]=e.heap[a],r=a,a<<=1;e.heap[r]=n}function V(e,t,r){var n,a,s,i,o=0;if(0!==e.last_lit)for(;n=e.pending_buf[e.d_buf+2*o]<<8|e.pending_buf[e.d_buf+2*o+1],a=e.pending_buf[e.l_buf+o],o++,0===n?L(e,a,t):(L(e,(s=P[a])+c+1,t),0!==(i=_[s])&&F(e,a-=N[s],i),L(e,s=M(--n),r),0!==(i=w[s])&&F(e,n-=R[s],i)),o<e.last_lit;);L(e,b,t)}function G(e,t){var r,n,a,s=t.dyn_tree,i=t.stat_desc.static_tree,o=t.stat_desc.has_stree,l=t.stat_desc.elems,c=-1;for(e.heap_len=0,e.heap_max=f,r=0;r<l;r++)0!==s[2*r]?(e.heap[++e.heap_len]=c=r,e.depth[r]=0):s[2*r+1]=0;for(;e.heap_len<2;)s[2*(a=e.heap[++e.heap_len]=c<2?++c:0)]=1,e.depth[a]=0,e.opt_len--,o&&(e.static_len-=i[2*a+1]);for(t.max_code=c,r=e.heap_len>>1;1<=r;r--)Z(e,s,r);for(a=l;r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],Z(e,s,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,s[2*a]=s[2*r]+s[2*n],e.depth[a]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,s[2*r+1]=s[2*n+1]=a,e.heap[1]=a++,Z(e,s,1),2<=e.heap_len;);e.heap[--e.heap_max]=e.heap[1],function(e,t){var r,n,a,s,i,o,l=t.dyn_tree,c=t.max_code,h=t.stat_desc.static_tree,u=t.stat_desc.has_stree,d=t.stat_desc.extra_bits,m=t.stat_desc.extra_base,g=t.stat_desc.max_length,b=0;for(s=0;s<=p;s++)e.bl_count[s]=0;for(l[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<f;r++)g<(s=l[2*l[2*(n=e.heap[r])+1]+1]+1)&&(s=g,b++),l[2*n+1]=s,c<n||(e.bl_count[s]++,i=0,m<=n&&(i=d[n-m]),o=l[2*n],e.opt_len+=o*(s+i),u&&(e.static_len+=o*(h[2*n+1]+i)));if(0!==b){do{for(s=g-1;0===e.bl_count[s];)s--;e.bl_count[s]--,e.bl_count[s+1]+=2,e.bl_count[g]--,b-=2}while(0<b);for(s=g;0!==s;s--)for(n=e.bl_count[s];0!==n;)c<(a=e.heap[--r])||(l[2*a+1]!==s&&(e.opt_len+=(s-l[2*a+1])*l[2*a],l[2*a+1]=s),n--)}}(e,t),U(s,c,e.bl_count)}function X(e,t,r){var n,a,s=-1,i=t[1],o=0,l=7,c=4;for(0===i&&(l=138,c=3),t[2*(r+1)+1]=65535,n=0;n<=r;n++)a=i,i=t[2*(n+1)+1],++o<l&&a===i||(o<c?e.bl_tree[2*a]+=o:0!==a?(a!==s&&e.bl_tree[2*a]++,e.bl_tree[2*y]++):o<=10?e.bl_tree[2*v]++:e.bl_tree[2*k]++,s=a,c=(o=0)===i?(l=138,3):a===i?(l=6,3):(l=7,4))}function K(e,t,r){var n,a,s=-1,i=t[1],o=0,l=7,c=4;for(0===i&&(l=138,c=3),n=0;n<=r;n++)if(a=i,i=t[2*(n+1)+1],!(++o<l&&a===i)){if(o<c)for(;L(e,a,e.bl_tree),0!=--o;);else 0!==a?(a!==s&&(L(e,a,e.bl_tree),o--),L(e,y,e.bl_tree),F(e,o-3,2)):o<=10?(L(e,v,e.bl_tree),F(e,o-3,3)):(L(e,k,e.bl_tree),F(e,o-11,7));s=a,c=(o=0)===i?(l=138,3):a===i?(l=6,3):(l=7,4)}}i(R);var q=!1;function Y(e,t,r,a){var s,i,l;F(e,(o<<1)+(a?1:0),3),i=t,l=r,W(s=e),O(s,l),O(s,~l),n.arraySet(s.pending_buf,s.window,i,l,s.pending),s.pending+=l}r._tr_init=function(e){q||(function(){var e,t,r,n,a,s=new Array(p+1);for(n=r=0;n<l-1;n++)for(N[n]=r,e=0;e<1<<_[n];e++)P[r++]=n;for(P[r-1]=n,n=a=0;n<16;n++)for(R[n]=a,e=0;e<1<<w[n];e++)A[a++]=n;for(a>>=7;n<u;n++)for(R[n]=a<<7,e=0;e<1<<w[n]-7;e++)A[256+a++]=n;for(t=0;t<=p;t++)s[t]=0;for(e=0;e<=143;)C[2*e+1]=8,e++,s[8]++;for(;e<=255;)C[2*e+1]=9,e++,s[9]++;for(;e<=279;)C[2*e+1]=7,e++,s[7]++;for(;e<=287;)C[2*e+1]=8,e++,s[8]++;for(U(C,h+1,s),e=0;e<u;e++)E[2*e+1]=5,E[2*e]=j(e,5);T=new D(C,_,c+1,h,p),z=new D(E,w,0,u,p),B=new D(new Array(0),S,0,d,g)}(),q=!0),e.l_desc=new I(e.dyn_ltree,T),e.d_desc=new I(e.dyn_dtree,z),e.bl_desc=new I(e.bl_tree,B),e.bi_buf=0,e.bi_valid=0,$(e)},r._tr_stored_block=Y,r._tr_flush_block=function(e,t,r,n){var i,o,l=0;0<e.level?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return a;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return s;for(t=32;t<c;t++)if(0!==e.dyn_ltree[2*t])return s;return a}(e)),G(e,e.l_desc),G(e,e.d_desc),l=function(e){var t;for(X(e,e.dyn_ltree,e.l_desc.max_code),X(e,e.dyn_dtree,e.d_desc.max_code),G(e,e.bl_desc),t=d-1;3<=t&&0===e.bl_tree[2*x[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(o=e.static_len+3+7>>>3)<=i&&(i=o)):i=o=r+5,r+4<=i&&-1!==t?Y(e,t,r,n):4===e.strategy||o===i?(F(e,2+(n?1:0),3),V(e,C,E)):(F(e,4+(n?1:0),3),function(e,t,r,n){var a;for(F(e,t-257,5),F(e,r-1,5),F(e,n-4,4),a=0;a<n;a++)F(e,e.bl_tree[2*x[a]+1],3);K(e,e.dyn_ltree,t-1),K(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,l+1),V(e,e.dyn_ltree,e.dyn_dtree)),$(e),n&&W(e)},r._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(P[r]+c+1)]++,e.dyn_dtree[2*M(t)]++),e.last_lit===e.lit_bufsize-1},r._tr_align=function(e){var t;F(e,2,3),L(e,b,C),16===(t=e).bi_valid?(O(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):8<=t.bi_valid&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}},{"../utils/common":41}],53:[function(e,t,r){t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(t,r,n){(function(e){!function(e,t){if(!e.setImmediate){var r,n,a,s,i=1,o={},l=!1,c=e.document,h=Object.getPrototypeOf&&Object.getPrototypeOf(e);h=h&&h.setTimeout?h:e,r="[object process]"==={}.toString.call(e.process)?function(e){process.nextTick(function(){d(e)})}:function(){if(e.postMessage&&!e.importScripts){var t=!0,r=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=r,t}}()?(s="setImmediate$"+Math.random()+"$",e.addEventListener?e.addEventListener("message",f,!1):e.attachEvent("onmessage",f),function(t){e.postMessage(s+t,"*")}):e.MessageChannel?((a=new MessageChannel).port1.onmessage=function(e){d(e.data)},function(e){a.port2.postMessage(e)}):c&&"onreadystatechange"in c.createElement("script")?(n=c.documentElement,function(e){var t=c.createElement("script");t.onreadystatechange=function(){d(e),t.onreadystatechange=null,n.removeChild(t),t=null},n.appendChild(t)}):function(e){setTimeout(d,0,e)},h.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var a={callback:e,args:t};return o[i]=a,r(i),i++},h.clearImmediate=u}function u(e){delete o[e]}function d(e){if(l)setTimeout(d,0,e);else{var r=o[e];if(r){l=!0;try{!function(e){var r=e.callback,n=e.args;switch(n.length){case 0:r();break;case 1:r(n[0]);break;case 2:r(n[0],n[1]);break;case 3:r(n[0],n[1],n[2]);break;default:r.apply(t,n)}}(r)}finally{u(e),l=!1}}}}function f(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(s)&&d(+t.data.slice(s.length))}}("undefined"==typeof self?void 0===e?this:e:self)}).call(this,void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[10])(10)),b.exports));var v,k;function _(e){return/^[^"'].*\s.*[^"']$/.test(e)?`'${e}'`:e}function w(e){let t=e.lastIndexOf("/")+1;return[0==t?"":e.substring(0,t),0==t?e:e.substring(t)]}function S(e,t){try{const r="http://docx/";return new URL(e,r+t).toString().substring(r.length)}catch{return`${t}${e}`}}function x(e,t){return e.reduce((e,r)=>(e[t(r)]=r,e),{})}function C(e){return e&&"object"==typeof e&&!Array.isArray(e)}function E(e,...t){if(!t.length)return e;const r=t.shift();if(C(e)&&C(r))for(const n in r)if(C(r[n])){E(e[n]??(e[n]={}),r[n])}else e[n]=r[n];return E(e,...t)}function A(e){return Array.isArray(e)?e:[e]}(k=v||(v={})).OfficeDocument="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",k.FontTable="http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable",k.Image="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",k.Numbering="http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering",k.Styles="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",k.StylesWithEffects="http://schemas.microsoft.com/office/2007/relationships/stylesWithEffects",k.Theme="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",k.Settings="http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings",k.WebSettings="http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings",k.Hyperlink="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",k.Footnotes="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes",k.Endnotes="http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes",k.Footer="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer",k.Header="http://schemas.openxmlformats.org/officeDocument/2006/relationships/header",k.ExtendedProperties="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",k.CoreProperties="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",k.CustomProperties="http://schemas.openxmlformats.org/package/2006/relationships/metadata/custom-properties",k.Comments="http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",k.CommentsExtended="http://schemas.microsoft.com/office/2011/relationships/commentsExtended",k.AltChunk="http://schemas.openxmlformats.org/officeDocument/2006/relationships/aFChunk";const P="http://schemas.openxmlformats.org/wordprocessingml/2006/main",N={mul:.05,unit:"pt"},T={mul:1/12700,unit:"pt"},z={mul:.5,unit:"pt"},B={mul:.125,unit:"pt",min:.25,max:12},R={mul:1,unit:"pt"},D={mul:.02,unit:"%"};function I(e,t=N){if(null==e||/.+(p[xt]|[%])$/.test(e))return e;var r=parseInt(e)*t.mul;return t.min&&t.max&&(r=function(e,t,r){return t>e?t:r<e?r:e}(r,t.min,t.max)),`${r.toFixed(2)}${t.unit}`}function M(e,t,r){if(e.namespaceURI!=P)return!1;switch(e.localName){case"color":t.color=r.attr(e,"val");break;case"sz":t.fontSize=r.lengthAttr(e,"val",z);break;default:return!1}return!0}class O{elements(e,t=null){const r=[];for(let n=0,a=e.childNodes.length;n<a;n++){let a=e.childNodes.item(n);1!=a.nodeType||null!=t&&a.localName!=t||r.push(a)}return r}element(e,t){for(let r=0,n=e.childNodes.length;r<n;r++){let n=e.childNodes.item(r);if(1==n.nodeType&&n.localName==t)return n}return null}elementAttr(e,t,r){var n=this.element(e,t);return n?this.attr(n,r):void 0}attrs(e){return Array.from(e.attributes)}attr(e,t){for(let r=0,n=e.attributes.length;r<n;r++){let n=e.attributes.item(r);if(n.localName==t)return n.value}return null}intAttr(e,t,r=null){var n=this.attr(e,t);return n?parseInt(n):r}hexAttr(e,t,r=null){var n=this.attr(e,t);return n?parseInt(n,16):r}floatAttr(e,t,r=null){var n=this.attr(e,t);return n?parseFloat(n):r}boolAttr(e,t,r=null){return function(e,t=!1){switch(e){case"1":case"on":case"true":return!0;case"0":case"off":case"false":return!1;default:return t}}(this.attr(e,t),r)}lengthAttr(e,t,r=N){return I(this.attr(e,t),r)}}const F=new O;class L{constructor(e,t){this._package=e,this.path=t}async load(){this.rels=await this._package.loadRelationships(this.path);const e=await this._package.load(this.path),t=this._package.parseXmlDocument(e);this._package.options.keepOrigin&&(this._xmlDocument=t),this.parseXml(t.firstElementChild)}save(){var e;this._package.update(this.path,(e=this._xmlDocument,(new XMLSerializer).serializeToString(e)))}parseXml(e){}}const j={embedRegular:"regular",embedBold:"bold",embedItalic:"italic",embedBoldItalic:"boldItalic"};function U(e,t){return t.elements(e).map(e=>function(e,t){let r={name:t.attr(e,"name"),embedFontRefs:[]};for(let n of t.elements(e))switch(n.localName){case"family":r.family=t.attr(n,"val");break;case"altName":r.altName=t.attr(n,"val");break;case"embedRegular":case"embedBold":case"embedItalic":case"embedBoldItalic":r.embedFontRefs.push($(n,t))}return r}(e,t))}function $(e,t){return{id:t.attr(e,"id"),key:t.attr(e,"fontKey"),type:j[e.localName]}}class W extends L{parseXml(e){this.fonts=U(e,this._package.xmlParser)}}class H{constructor(e,t){this._zip=e,this.options=t,this.xmlParser=new O}get(e){const t=function(e){return e.startsWith("/")?e.substr(1):e}(e);return this._zip.files[t]??this._zip.files[t.replace(/\//g,"\\")]}update(e,t){this._zip.file(e,t)}static async load(e,t){const r=await y.loadAsync(e);return new H(r,t)}save(e="blob"){return this._zip.generateAsync({type:e})}load(e,t="string"){var r;return(null==(r=this.get(e))?void 0:r.async(t))??Promise.resolve(null)}async loadRelationships(e=null){let t="_rels/.rels";if(null!=e){const[r,n]=w(e);t=`${r}_rels/${n}.rels`}const r=await this.load(t);return r?(n=this.parseXmlDocument(r).firstElementChild,(a=this.xmlParser).elements(n).map(e=>({id:a.attr(e,"Id"),type:a.attr(e,"Type"),target:a.attr(e,"Target"),targetMode:a.attr(e,"TargetMode")}))):null;var n,a}parseXmlDocument(e){return function(e,t=!1){var r;t&&(e=e.replace(/<[?].*[?]>/,"")),e=65279===(r=e).charCodeAt(0)?r.substring(1):r;const n=(new DOMParser).parseFromString(e,"application/xml"),a=null==(s=n.getElementsByTagName("parsererror")[0])?void 0:s.textContent;var s;if(a)throw new Error(a);return n}(e,this.options.trimXmlDeclaration)}}class Z extends L{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.body=this._documentParser.parseDocumentFile(e)}}function V(e,t){return{type:t.attr(e,"val"),color:t.attr(e,"color"),size:t.lengthAttr(e,"sz",B),offset:t.lengthAttr(e,"space",R),frame:t.boolAttr(e,"frame"),shadow:t.boolAttr(e,"shadow")}}function G(e,t){var r={};for(let n of t.elements(e))switch(n.localName){case"left":r.left=V(n,t);break;case"top":r.top=V(n,t);break;case"right":r.right=V(n,t);break;case"bottom":r.bottom=V(n,t)}return r}var X,K,q,Y;function J(e,t=F){var r={};for(let n of t.elements(e))switch(n.localName){case"pgSz":r.pageSize={width:t.lengthAttr(n,"w"),height:t.lengthAttr(n,"h"),orientation:t.attr(n,"orient")};break;case"type":r.type=t.attr(n,"val");break;case"pgMar":r.pageMargins={left:t.lengthAttr(n,"left"),right:t.lengthAttr(n,"right"),top:t.lengthAttr(n,"top"),bottom:t.lengthAttr(n,"bottom"),header:t.lengthAttr(n,"header"),footer:t.lengthAttr(n,"footer"),gutter:t.lengthAttr(n,"gutter")};break;case"cols":r.columns=Q(n,t);break;case"headerReference":(r.headerRefs??(r.headerRefs=[])).push(te(n,t));break;case"footerReference":(r.footerRefs??(r.footerRefs=[])).push(te(n,t));break;case"titlePg":r.titlePage=t.boolAttr(n,"val",!0);break;case"pgBorders":r.pageBorders=G(n,t);break;case"pgNumType":r.pageNumber=ee(n,t)}return r}function Q(e,t){return{numberOfColumns:t.intAttr(e,"num"),space:t.lengthAttr(e,"space"),separator:t.boolAttr(e,"sep"),equalWidth:t.boolAttr(e,"equalWidth",!0),columns:t.elements(e,"col").map(e=>({width:t.lengthAttr(e,"w"),space:t.lengthAttr(e,"space")}))}}function ee(e,t){return{chapSep:t.attr(e,"chapSep"),chapStyle:t.attr(e,"chapStyle"),format:t.attr(e,"fmt"),start:t.intAttr(e,"start")}}function te(e,t){return{id:t.attr(e,"id"),type:t.attr(e,"type")}}function re(e,t){let r={};for(let n of t.elements(e))ne(n,r,t);return r}function ne(e,t,r){return!!M(e,t,r)}function ae(e,t){let r={};for(let n of t.elements(e))se(n,r,t);return r}function se(e,t,r){if(e.namespaceURI!=P)return!1;if(M(e,t,r))return!0;switch(e.localName){case"tabs":t.tabs=function(e,t){return t.elements(e,"tab").map(e=>({position:t.lengthAttr(e,"pos"),leader:t.attr(e,"leader"),style:t.attr(e,"val")}))}(e,r);break;case"sectPr":t.sectionProps=J(e,r);break;case"numPr":t.numbering=function(e,t){var r={};for(let n of t.elements(e))switch(n.localName){case"numId":r.id=t.attr(n,"val");break;case"ilvl":r.level=t.intAttr(n,"val")}return r}(e,r);break;case"spacing":return t.lineSpacing=function(e,t){return{before:t.lengthAttr(e,"before"),after:t.lengthAttr(e,"after"),line:t.intAttr(e,"line"),lineRule:t.attr(e,"lineRule")}}(e,r),!1;case"textAlignment":return t.textAlignment=r.attr(e,"val"),!1;case"keepLines":t.keepLines=r.boolAttr(e,"val",!0);break;case"keepNext":t.keepNext=r.boolAttr(e,"val",!0);break;case"pageBreakBefore":t.pageBreakBefore=r.boolAttr(e,"val",!0);break;case"outlineLvl":t.outlineLevel=r.intAttr(e,"val");break;case"pStyle":t.styleName=r.attr(e,"val");break;case"rPr":t.runProps=re(e,r);break;default:return!1}return!0}function ie(e,t){let r={id:t.attr(e,"numId"),overrides:[]};for(let n of t.elements(e))switch(n.localName){case"abstractNumId":r.abstractId=t.attr(n,"val");break;case"lvlOverride":r.overrides.push(ce(n,t))}return r}function oe(e,t){let r={id:t.attr(e,"abstractNumId"),levels:[]};for(let n of t.elements(e))switch(n.localName){case"name":r.name=t.attr(n,"val");break;case"multiLevelType":r.multiLevelType=t.attr(n,"val");break;case"numStyleLink":r.numberingStyleLink=t.attr(n,"val");break;case"styleLink":r.styleLink=t.attr(n,"val");break;case"lvl":r.levels.push(le(n,t))}return r}function le(e,t){let r={level:t.intAttr(e,"ilvl")};for(let n of t.elements(e))switch(n.localName){case"start":r.start=t.attr(n,"val");break;case"lvlRestart":r.restart=t.intAttr(n,"val");break;case"numFmt":r.format=t.attr(n,"val");break;case"lvlText":r.text=t.attr(n,"val");break;case"lvlJc":r.justification=t.attr(n,"val");break;case"lvlPicBulletId":r.bulletPictureId=t.attr(n,"val");break;case"pStyle":r.paragraphStyle=t.attr(n,"val");break;case"pPr":r.paragraphProps=ae(n,t);break;case"rPr":r.runProps=re(n,t)}return r}function ce(e,t){let r={level:t.intAttr(e,"ilvl")};for(let n of t.elements(e))switch(n.localName){case"startOverride":r.start=t.intAttr(n,"val");break;case"lvl":r.numberingLevel=le(n,t)}return r}function he(e,t){var r=t.element(e,"pict"),n=r&&t.element(r,"shape"),a=n&&t.element(n,"imagedata");return a?{id:t.attr(e,"numPicBulletId"),referenceId:t.attr(a,"id"),style:t.attr(n,"style")}:null}(K=X||(X={})).Continuous="continuous",K.NextPage="nextPage",K.NextColumn="nextColumn",K.EvenPage="evenPage",K.OddPage="oddPage";class ue extends L{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){Object.assign(this,function(e,t){let r={numberings:[],abstractNumberings:[],bulletPictures:[]};for(let n of t.elements(e))switch(n.localName){case"num":r.numberings.push(ie(n,t));break;case"abstractNum":r.abstractNumberings.push(oe(n,t));break;case"numPicBullet":r.bulletPictures.push(he(n,t))}return r}(e,this._package.xmlParser)),this.domNumberings=this._documentParser.parseNumberingFile(e)}}class de extends L{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.styles=this._documentParser.parseStylesFile(e)}}(Y=q||(q={})).Document="document",Y.Paragraph="paragraph",Y.Run="run",Y.Break="break",Y.NoBreakHyphen="noBreakHyphen",Y.Table="table",Y.Row="row",Y.Cell="cell",Y.Hyperlink="hyperlink",Y.SmartTag="smartTag",Y.Drawing="drawing",Y.Image="image",Y.Text="text",Y.Tab="tab",Y.Symbol="symbol",Y.BookmarkStart="bookmarkStart",Y.BookmarkEnd="bookmarkEnd",Y.Footer="footer",Y.Header="header",Y.FootnoteReference="footnoteReference",Y.EndnoteReference="endnoteReference",Y.Footnote="footnote",Y.Endnote="endnote",Y.SimpleField="simpleField",Y.ComplexField="complexField",Y.Instruction="instruction",Y.VmlPicture="vmlPicture",Y.MmlMath="mmlMath",Y.MmlMathParagraph="mmlMathParagraph",Y.MmlFraction="mmlFraction",Y.MmlFunction="mmlFunction",Y.MmlFunctionName="mmlFunctionName",Y.MmlNumerator="mmlNumerator",Y.MmlDenominator="mmlDenominator",Y.MmlRadical="mmlRadical",Y.MmlBase="mmlBase",Y.MmlDegree="mmlDegree",Y.MmlSuperscript="mmlSuperscript",Y.MmlSubscript="mmlSubscript",Y.MmlPreSubSuper="mmlPreSubSuper",Y.MmlSubArgument="mmlSubArgument",Y.MmlSuperArgument="mmlSuperArgument",Y.MmlNary="mmlNary",Y.MmlDelimiter="mmlDelimiter",Y.MmlRun="mmlRun",Y.MmlEquationArray="mmlEquationArray",Y.MmlLimit="mmlLimit",Y.MmlLimitLower="mmlLimitLower",Y.MmlMatrix="mmlMatrix",Y.MmlMatrixRow="mmlMatrixRow",Y.MmlBox="mmlBox",Y.MmlBar="mmlBar",Y.MmlGroupChar="mmlGroupChar",Y.VmlElement="vmlElement",Y.Inserted="inserted",Y.Deleted="deleted",Y.DeletedText="deletedText",Y.Comment="comment",Y.CommentReference="commentReference",Y.CommentRangeStart="commentRangeStart",Y.CommentRangeEnd="commentRangeEnd",Y.AltChunk="altChunk";class fe{constructor(){this.children=[],this.cssStyle={}}}class pe extends fe{constructor(){super(...arguments),this.type=q.Header}}class me extends fe{constructor(){super(...arguments),this.type=q.Footer}}class ge extends L{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.rootElement=this.createRootElement(),this.rootElement.children=this._documentParser.parseBodyElements(e)}}class be extends ge{createRootElement(){return new pe}}class ye extends ge{createRootElement(){return new me}}function ve(e){if(void 0!==e)return parseInt(e)}class ke extends L{parseXml(e){this.props=function(e,t){const r={};for(let n of t.elements(e))switch(n.localName){case"Template":r.template=n.textContent;break;case"Pages":r.pages=ve(n.textContent);break;case"Words":r.words=ve(n.textContent);break;case"Characters":r.characters=ve(n.textContent);break;case"Application":r.application=n.textContent;break;case"Lines":r.lines=ve(n.textContent);break;case"Paragraphs":r.paragraphs=ve(n.textContent);break;case"Company":r.company=n.textContent;break;case"AppVersion":r.appVersion=n.textContent}return r}(e,this._package.xmlParser)}}class _e extends L{parseXml(e){this.props=function(e,t){const r={};for(let n of t.elements(e))switch(n.localName){case"title":r.title=n.textContent;break;case"description":r.description=n.textContent;break;case"subject":r.subject=n.textContent;break;case"creator":r.creator=n.textContent;break;case"keywords":r.keywords=n.textContent;break;case"language":r.language=n.textContent;break;case"lastModifiedBy":r.lastModifiedBy=n.textContent;break;case"revision":n.textContent&&(r.revision=parseInt(n.textContent))}return r}(e,this._package.xmlParser)}}class we{}function Se(e,t){var r={name:t.attr(e,"name"),colors:{}};for(let s of t.elements(e)){var n=t.element(s,"srgbClr"),a=t.element(s,"sysClr");n?r.colors[s.localName]=t.attr(n,"val"):a&&(r.colors[s.localName]=t.attr(a,"lastClr"))}return r}function xe(e,t){var r={name:t.attr(e,"name")};for(let n of t.elements(e))switch(n.localName){case"majorFont":r.majorFont=Ce(n,t);break;case"minorFont":r.minorFont=Ce(n,t)}return r}function Ce(e,t){return{latinTypeface:t.elementAttr(e,"latin","typeface"),eaTypeface:t.elementAttr(e,"ea","typeface"),csTypeface:t.elementAttr(e,"cs","typeface")}}class Ee extends L{constructor(e,t){super(e,t)}parseXml(e){this.theme=function(e,t){var r=new we,n=t.element(e,"themeElements");for(let a of t.elements(n))switch(a.localName){case"clrScheme":r.colorScheme=Se(a,t);break;case"fontScheme":r.fontScheme=xe(a,t)}return r}(e,this._package.xmlParser)}}class Ae{}class Pe extends Ae{constructor(){super(...arguments),this.type=q.Footnote}}class Ne extends Ae{constructor(){super(...arguments),this.type=q.Endnote}}class Te extends L{constructor(e,t,r){super(e,t),this._documentParser=r}}class ze extends Te{constructor(e,t,r){super(e,t,r)}parseXml(e){this.notes=this._documentParser.parseNotes(e,"footnote",Pe)}}class Be extends Te{constructor(e,t,r){super(e,t,r)}parseXml(e){this.notes=this._documentParser.parseNotes(e,"endnote",Ne)}}function Re(e,t){var r={defaultNoteIds:[]};for(let n of t.elements(e))switch(n.localName){case"numFmt":r.nummeringFormat=t.attr(n,"val");break;case"footnote":case"endnote":r.defaultNoteIds.push(t.attr(n,"id"))}return r}class De extends L{constructor(e,t){super(e,t)}parseXml(e){this.settings=function(e,t){var r={};for(let n of t.elements(e))switch(n.localName){case"defaultTabStop":r.defaultTabStop=t.lengthAttr(n,"val");break;case"footnotePr":r.footnoteProps=Re(n,t);break;case"endnotePr":r.endnoteProps=Re(n,t);break;case"autoHyphenation":r.autoHyphenation=t.boolAttr(n,"val")}return r}(e,this._package.xmlParser)}}class Ie extends L{parseXml(e){this.props=function(e,t){return t.elements(e,"property").map(e=>{const r=e.firstChild;return{formatId:t.attr(e,"fmtid"),name:t.attr(e,"name"),type:r.nodeName,value:r.textContent}})}(e,this._package.xmlParser)}}class Me extends L{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.comments=this._documentParser.parseComments(e),this.commentMap=x(this.comments,e=>e.id)}}class Oe extends L{constructor(e,t){super(e,t),this.comments=[]}parseXml(e){const t=this._package.xmlParser;for(let r of t.elements(e,"commentEx"))this.comments.push({paraId:t.attr(r,"paraId"),paraIdParent:t.attr(r,"paraIdParent"),done:t.boolAttr(r,"done")});this.commentMap=x(this.comments,e=>e.paraId)}}const Fe=[{type:v.OfficeDocument,target:"word/document.xml"},{type:v.ExtendedProperties,target:"docProps/app.xml"},{type:v.CoreProperties,target:"docProps/core.xml"},{type:v.CustomProperties,target:"docProps/custom.xml"}];class Le{constructor(){this.parts=[],this.partsMap={}}static async load(e,t,r){var n=new Le;return n._options=r,n._parser=t,n._package=await H.load(e,r),n.rels=await n._package.loadRelationships(),await Promise.all(Fe.map(e=>{const t=n.rels.find(t=>t.type===e.type)??e;return n.loadRelationshipPart(t.target,t.type)})),n}save(e="blob"){return this._package.save(e)}async loadRelationshipPart(e,t){var r;if(this.partsMap[e])return this.partsMap[e];if(!this._package.get(e))return null;let n=null;switch(t){case v.OfficeDocument:this.documentPart=n=new Z(this._package,e,this._parser);break;case v.FontTable:this.fontTablePart=n=new W(this._package,e);break;case v.Numbering:this.numberingPart=n=new ue(this._package,e,this._parser);break;case v.Styles:this.stylesPart=n=new de(this._package,e,this._parser);break;case v.Theme:this.themePart=n=new Ee(this._package,e);break;case v.Footnotes:this.footnotesPart=n=new ze(this._package,e,this._parser);break;case v.Endnotes:this.endnotesPart=n=new Be(this._package,e,this._parser);break;case v.Footer:n=new ye(this._package,e,this._parser);break;case v.Header:n=new be(this._package,e,this._parser);break;case v.CoreProperties:this.corePropsPart=n=new _e(this._package,e);break;case v.ExtendedProperties:this.extendedPropsPart=n=new ke(this._package,e);break;case v.CustomProperties:n=new Ie(this._package,e);break;case v.Settings:this.settingsPart=n=new De(this._package,e);break;case v.Comments:this.commentsPart=n=new Me(this._package,e,this._parser);break;case v.CommentsExtended:this.commentsExtendedPart=n=new Oe(this._package,e)}if(null==n)return Promise.resolve(null);if(this.partsMap[e]=n,this.parts.push(n),await n.load(),(null==(r=n.rels)?void 0:r.length)>0){const[e]=w(n.path);await Promise.all(n.rels.map(t=>this.loadRelationshipPart(S(t.target,e),t.type)))}return n}async loadDocumentImage(e,t){const r=await this.loadResource(t??this.documentPart,e,"blob");return this.blobToURL(r)}async loadNumberingImage(e){const t=await this.loadResource(this.numberingPart,e,"blob");return this.blobToURL(t)}async loadFont(e,t){const r=await this.loadResource(this.fontTablePart,e,"uint8array");return r?this.blobToURL(new Blob([je(r,t)])):r}async loadAltChunk(e,t){return await this.loadResource(t??this.documentPart,e,"string")}blobToURL(e){return e?this._options.useBase64URL?function(e){return new Promise((t,r)=>{const n=new FileReader;n.onloadend=()=>t(n.result),n.onerror=()=>r(),n.readAsDataURL(e)})}(e):URL.createObjectURL(e):null}findPartByRelId(e,t=null){var r=(t.rels??this.rels).find(t=>t.id==e);const n=t?w(t.path)[0]:"";return r?this.partsMap[S(r.target,n)]:null}getPathById(e,t){const r=e.rels.find(e=>e.id==t),[n]=w(e.path);return r?S(r.target,n):null}loadResource(e,t,r){const n=this.getPathById(e,t);return n?this._package.load(n,r):Promise.resolve(null)}}function je(e,t){const r=t.replace(/{|}|-/g,""),n=new Array(16);for(let a=0;a<16;a++)n[16-a-1]=parseInt(r.substr(2*a,2),16);for(let a=0;a<32;a++)e[a]=e[a]^n[a%16];return e}function Ue(e,t){return{type:q.BookmarkStart,id:t.attr(e,"id"),name:t.attr(e,"name"),colFirst:t.intAttr(e,"colFirst"),colLast:t.intAttr(e,"colLast")}}function $e(e,t){return{type:q.BookmarkEnd,id:t.attr(e,"id")}}class We extends fe{constructor(){super(...arguments),this.type=q.VmlElement,this.attrs={}}}function He(e,t){var r=new We;switch(e.localName){case"rect":r.tagName="rect",Object.assign(r.attrs,{width:"100%",height:"100%"});break;case"oval":r.tagName="ellipse",Object.assign(r.attrs,{cx:"50%",cy:"50%",rx:"50%",ry:"50%"});break;case"line":r.tagName="line";break;case"shape":r.tagName="g";break;case"textbox":r.tagName="foreignObject",Object.assign(r.attrs,{width:"100%",height:"100%"});break;default:return null}for(const n of F.attrs(e))switch(n.localName){case"style":r.cssStyleText=n.value;break;case"fillcolor":r.attrs.fill=n.value;break;case"from":const[e,t]=Ge(n.value);Object.assign(r.attrs,{x1:e,y1:t});break;case"to":const[a,s]=Ge(n.value);Object.assign(r.attrs,{x2:a,y2:s})}for(const n of F.elements(e))switch(n.localName){case"stroke":Object.assign(r.attrs,Ze(n));break;case"fill":Object.assign(r.attrs,Ve());break;case"imagedata":r.tagName="image",Object.assign(r.attrs,{width:"100%",height:"100%"}),r.imageHref={id:F.attr(n,"id"),title:F.attr(n,"title")};break;case"txbxContent":r.children.push(...t.parseBodyElements(n));break;default:const e=He(n,t);e&&r.children.push(e)}return r}function Ze(e){return{stroke:F.attr(e,"color"),"stroke-width":F.lengthAttr(e,"weight",T)??"1px"}}function Ve(e){return{}}function Ge(e){return e.split(",")}class Xe extends fe{constructor(){super(...arguments),this.type=q.Comment}}class Ke extends fe{constructor(e){super(),this.id=e,this.type=q.CommentReference}}class qe extends fe{constructor(e){super(),this.id=e,this.type=q.CommentRangeStart}}class Ye extends fe{constructor(e){super(),this.id=e,this.type=q.CommentRangeEnd}}var Je="inherit",Qe="black",et="black",tt="transparent";const rt=[],nt={oMath:q.MmlMath,oMathPara:q.MmlMathParagraph,f:q.MmlFraction,func:q.MmlFunction,fName:q.MmlFunctionName,num:q.MmlNumerator,den:q.MmlDenominator,rad:q.MmlRadical,deg:q.MmlDegree,e:q.MmlBase,sSup:q.MmlSuperscript,sSub:q.MmlSubscript,sPre:q.MmlPreSubSuper,sup:q.MmlSuperArgument,sub:q.MmlSubArgument,d:q.MmlDelimiter,nary:q.MmlNary,eqArr:q.MmlEquationArray,lim:q.MmlLimit,limLow:q.MmlLimitLower,m:q.MmlMatrix,mr:q.MmlMatrixRow,box:q.MmlBox,bar:q.MmlBar,groupChr:q.MmlGroupChar};class at{constructor(e){this.options={ignoreWidth:!1,debug:!1,...e}}parseNotes(e,t,r){var n=[];for(let a of F.elements(e,t)){const e=new r;e.id=F.attr(a,"id"),e.noteType=F.attr(a,"type"),e.children=this.parseBodyElements(a),n.push(e)}return n}parseComments(e){var t=[];for(let r of F.elements(e,"comment")){const e=new Xe;e.id=F.attr(r,"id"),e.author=F.attr(r,"author"),e.initials=F.attr(r,"initials"),e.date=F.attr(r,"date"),e.children=this.parseBodyElements(r),t.push(e)}return t}parseDocumentFile(e){var t=F.element(e,"body"),r=F.element(e,"background"),n=F.element(t,"sectPr");return{type:q.Document,children:this.parseBodyElements(t),props:n?J(n,F):{},cssStyle:r?this.parseBackground(r):{}}}parseBackground(e){var t={},r=it.colorAttr(e,"color");return r&&(t["background-color"]=r),t}parseBodyElements(e){var t=[];for(let r of F.elements(e))switch(r.localName){case"p":t.push(this.parseParagraph(r));break;case"altChunk":t.push(this.parseAltChunk(r));break;case"tbl":t.push(this.parseTable(r));break;case"sdt":t.push(...this.parseSdt(r,e=>this.parseBodyElements(e)))}return t}parseStylesFile(e){var t=[];return it.foreach(e,e=>{switch(e.localName){case"style":t.push(this.parseStyle(e));break;case"docDefaults":t.push(this.parseDefaultStyles(e))}}),t}parseDefaultStyles(e){var t={id:null,name:null,target:null,basedOn:null,styles:[]};return it.foreach(e,e=>{switch(e.localName){case"rPrDefault":var r=F.element(e,"rPr");r&&t.styles.push({target:"span",values:this.parseDefaultProperties(r,{})});break;case"pPrDefault":var n=F.element(e,"pPr");n&&t.styles.push({target:"p",values:this.parseDefaultProperties(n,{})})}}),t}parseStyle(e){var t={id:F.attr(e,"styleId"),isDefault:F.boolAttr(e,"default"),name:null,target:null,basedOn:null,styles:[],linked:null};switch(F.attr(e,"type")){case"paragraph":t.target="p";break;case"table":t.target="table";break;case"character":t.target="span"}return it.foreach(e,e=>{switch(e.localName){case"basedOn":t.basedOn=F.attr(e,"val");break;case"name":t.name=F.attr(e,"val");break;case"link":t.linked=F.attr(e,"val");break;case"next":t.next=F.attr(e,"val");break;case"aliases":t.aliases=F.attr(e,"val").split(",");break;case"pPr":t.styles.push({target:"p",values:this.parseDefaultProperties(e,{})}),t.paragraphProps=ae(e,F);break;case"rPr":t.styles.push({target:"span",values:this.parseDefaultProperties(e,{})}),t.runProps=re(e,F);break;case"tblPr":case"tcPr":t.styles.push({target:"td",values:this.parseDefaultProperties(e,{})});break;case"tblStylePr":for(let r of this.parseTableStyle(e))t.styles.push(r);break;case"rsid":case"qFormat":case"hidden":case"semiHidden":case"unhideWhenUsed":case"autoRedefine":case"uiPriority":break;default:this.options.debug}}),t}parseTableStyle(e){var t=[],r=F.attr(e,"type"),n="",a="";switch(r){case"firstRow":a=".first-row",n="tr.first-row td";break;case"lastRow":a=".last-row",n="tr.last-row td";break;case"firstCol":a=".first-col",n="td.first-col";break;case"lastCol":a=".last-col",n="td.last-col";break;case"band1Vert":a=":not(.no-vband)",n="td.odd-col";break;case"band2Vert":a=":not(.no-vband)",n="td.even-col";break;case"band1Horz":a=":not(.no-hband)",n="tr.odd-row";break;case"band2Horz":a=":not(.no-hband)",n="tr.even-row";break;default:return[]}return it.foreach(e,e=>{switch(e.localName){case"pPr":t.push({target:`${n} p`,mod:a,values:this.parseDefaultProperties(e,{})});break;case"rPr":t.push({target:`${n} span`,mod:a,values:this.parseDefaultProperties(e,{})});break;case"tblPr":case"tcPr":t.push({target:n,mod:a,values:this.parseDefaultProperties(e,{})})}}),t}parseNumberingFile(e){var t=[],r={},n=[];return it.foreach(e,e=>{switch(e.localName){case"abstractNum":this.parseAbstractNumbering(e,n).forEach(e=>t.push(e));break;case"numPicBullet":n.push(this.parseNumberingPicBullet(e));break;case"num":var a=F.attr(e,"numId"),s=F.elementAttr(e,"abstractNumId","val");r[s]=a}}),t.forEach(e=>e.id=r[e.id]),t}parseNumberingPicBullet(e){var t=F.element(e,"pict"),r=t&&F.element(t,"shape"),n=r&&F.element(r,"imagedata");return n?{id:F.intAttr(e,"numPicBulletId"),src:F.attr(n,"id"),style:F.attr(r,"style")}:null}parseAbstractNumbering(e,t){var r=[],n=F.attr(e,"abstractNumId");return it.foreach(e,e=>{if("lvl"===e.localName)r.push(this.parseNumberingLevel(n,e,t))}),r}parseNumberingLevel(e,t,r){var n={id:e,level:F.intAttr(t,"ilvl"),start:1,pStyleName:void 0,pStyle:{},rStyle:{},suff:"tab"};return it.foreach(t,e=>{switch(e.localName){case"start":n.start=F.intAttr(e,"val");break;case"pPr":this.parseDefaultProperties(e,n.pStyle);break;case"rPr":this.parseDefaultProperties(e,n.rStyle);break;case"lvlPicBulletId":var t=F.intAttr(e,"val");n.bullet=r.find(e=>(null==e?void 0:e.id)==t);break;case"lvlText":n.levelText=F.attr(e,"val");break;case"pStyle":n.pStyleName=F.attr(e,"val");break;case"numFmt":n.format=F.attr(e,"val");break;case"suff":n.suff=F.attr(e,"val")}}),n}parseSdt(e,t){const r=F.element(e,"sdtContent");return r?t(r):[]}parseInserted(e,t){var r;return{type:q.Inserted,children:(null==(r=t(e))?void 0:r.children)??[]}}parseDeleted(e,t){var r;return{type:q.Deleted,children:(null==(r=t(e))?void 0:r.children)??[]}}parseAltChunk(e){return{type:q.AltChunk,children:[],id:F.attr(e,"id")}}parseParagraph(e){var t={type:q.Paragraph,children:[]};for(let r of F.elements(e))switch(r.localName){case"pPr":this.parseParagraphProperties(r,t);break;case"r":t.children.push(this.parseRun(r,t));break;case"hyperlink":t.children.push(this.parseHyperlink(r,t));break;case"smartTag":t.children.push(this.parseSmartTag(r,t));break;case"bookmarkStart":t.children.push(Ue(r,F));break;case"bookmarkEnd":t.children.push($e(r,F));break;case"commentRangeStart":t.children.push(new qe(F.attr(r,"id")));break;case"commentRangeEnd":t.children.push(new Ye(F.attr(r,"id")));break;case"oMath":case"oMathPara":t.children.push(this.parseMathElement(r));break;case"sdt":t.children.push(...this.parseSdt(r,e=>this.parseParagraph(e).children));break;case"ins":t.children.push(this.parseInserted(r,e=>this.parseParagraph(e)));break;case"del":t.children.push(this.parseDeleted(r,e=>this.parseParagraph(e)))}return t}parseParagraphProperties(e,t){this.parseDefaultProperties(e,t.cssStyle={},null,e=>{if(se(e,t,F))return!0;switch(e.localName){case"pStyle":t.styleName=F.attr(e,"val");break;case"cnfStyle":t.className=ot.classNameOfCnfStyle(e);break;case"framePr":this.parseFrame(e,t);break;case"rPr":break;default:return!1}return!0})}parseFrame(e,t){"drop"==F.attr(e,"dropCap")&&(t.cssStyle.float="left")}parseHyperlink(e,t){var r={type:q.Hyperlink,parent:t,children:[]};return r.anchor=F.attr(e,"anchor"),r.id=F.attr(e,"id"),it.foreach(e,e=>{if("r"===e.localName)r.children.push(this.parseRun(e,r))}),r}parseSmartTag(e,t){var r={type:q.SmartTag,parent:t,children:[]},n=F.attr(e,"uri"),a=F.attr(e,"element");return n&&(r.uri=n),a&&(r.element=a),it.foreach(e,e=>{if("r"===e.localName)r.children.push(this.parseRun(e,r))}),r}parseRun(e,t){var r={type:q.Run,parent:t,children:[]};return it.foreach(e,e=>{switch((e=this.checkAlternateContent(e)).localName){case"t":r.children.push({type:q.Text,text:e.textContent});break;case"delText":r.children.push({type:q.DeletedText,text:e.textContent});break;case"commentReference":r.children.push(new Ke(F.attr(e,"id")));break;case"fldSimple":r.children.push({type:q.SimpleField,instruction:F.attr(e,"instr"),lock:F.boolAttr(e,"lock",!1),dirty:F.boolAttr(e,"dirty",!1)});break;case"instrText":r.fieldRun=!0,r.children.push({type:q.Instruction,text:e.textContent});break;case"fldChar":r.fieldRun=!0,r.children.push({type:q.ComplexField,charType:F.attr(e,"fldCharType"),lock:F.boolAttr(e,"lock",!1),dirty:F.boolAttr(e,"dirty",!1)});break;case"noBreakHyphen":r.children.push({type:q.NoBreakHyphen});break;case"br":r.children.push({type:q.Break,break:F.attr(e,"type")||"textWrapping"});break;case"lastRenderedPageBreak":r.children.push({type:q.Break,break:"lastRenderedPageBreak"});break;case"sym":r.children.push({type:q.Symbol,font:_(F.attr(e,"font")),char:F.attr(e,"char")});break;case"tab":r.children.push({type:q.Tab});break;case"footnoteReference":r.children.push({type:q.FootnoteReference,id:F.attr(e,"id")});break;case"endnoteReference":r.children.push({type:q.EndnoteReference,id:F.attr(e,"id")});break;case"drawing":let t=this.parseDrawing(e);t&&(r.children=[t]);break;case"pict":r.children.push(this.parseVmlPicture(e));break;case"rPr":this.parseRunProperties(e,r)}}),r}parseMathElement(e){const t=`${e.localName}Pr`,r={type:nt[e.localName],children:[]};for(const a of F.elements(e)){if(nt[a.localName])r.children.push(this.parseMathElement(a));else if("r"==a.localName){var n=this.parseRun(a);n.type=q.MmlRun,r.children.push(n)}else a.localName==t&&(r.props=this.parseMathProperies(a))}return r}parseMathProperies(e){const t={};for(const r of F.elements(e))switch(r.localName){case"chr":t.char=F.attr(r,"val");break;case"vertJc":t.verticalJustification=F.attr(r,"val");break;case"pos":t.position=F.attr(r,"val");break;case"degHide":t.hideDegree=F.boolAttr(r,"val");break;case"begChr":t.beginChar=F.attr(r,"val");break;case"endChr":t.endChar=F.attr(r,"val")}return t}parseRunProperties(e,t){this.parseDefaultProperties(e,t.cssStyle={},null,e=>{switch(e.localName){case"rStyle":t.styleName=F.attr(e,"val");break;case"vertAlign":t.verticalAlign=ot.valueOfVertAlign(e,!0);break;default:return!1}return!0})}parseVmlPicture(e){const t={type:q.VmlPicture,children:[]};for(const r of F.elements(e)){const e=He(r,this);e&&t.children.push(e)}return t}checkAlternateContent(e){var t;if("AlternateContent"!=e.localName)return e;var r=F.element(e,"Choice");if(r){var n=F.attr(r,"Requires"),a=e.lookupNamespaceURI(n);if(rt.includes(a))return r.firstElementChild}return null==(t=F.element(e,"Fallback"))?void 0:t.firstElementChild}parseDrawing(e){for(var t of F.elements(e))switch(t.localName){case"inline":case"anchor":return this.parseDrawingWrapper(t)}}parseDrawingWrapper(e){var t={type:q.Drawing,children:[],cssStyle:{}},r="anchor"==e.localName;let n=null,a=F.boolAttr(e,"simplePos");F.boolAttr(e,"behindDoc");let s={relative:"page",align:"left",offset:"0"},i={relative:"page",align:"top",offset:"0"};for(var o of F.elements(e))switch(o.localName){case"simplePos":a&&(s.offset=F.lengthAttr(o,"x",T),i.offset=F.lengthAttr(o,"y",T));break;case"extent":t.cssStyle.width=F.lengthAttr(o,"cx",T),t.cssStyle.height=F.lengthAttr(o,"cy",T);break;case"positionH":case"positionV":if(!a){let e="positionH"==o.localName?s:i;var l=F.element(o,"align"),c=F.element(o,"posOffset");e.relative=F.attr(o,"relativeFrom")??e.relative,l&&(e.align=l.textContent),c&&(e.offset=it.sizeValue(c,T))}break;case"wrapTopAndBottom":n="wrapTopAndBottom";break;case"wrapNone":n="wrapNone";break;case"graphic":var h=this.parseGraphic(o);h&&t.children.push(h)}return"wrapTopAndBottom"==n?(t.cssStyle.display="block",s.align&&(t.cssStyle["text-align"]=s.align,t.cssStyle.width="100%")):"wrapNone"==n?(t.cssStyle.display="block",t.cssStyle.position="relative",t.cssStyle.width="0px",t.cssStyle.height="0px",s.offset&&(t.cssStyle.left=s.offset),i.offset&&(t.cssStyle.top=i.offset)):!r||"left"!=s.align&&"right"!=s.align||(t.cssStyle.float=s.align),t}parseGraphic(e){var t=F.element(e,"graphicData");for(let r of F.elements(t))if("pic"===r.localName)return this.parsePicture(r);return null}parsePicture(e){var t={type:q.Image,src:"",cssStyle:{}},r=F.element(e,"blipFill"),n=F.element(r,"blip");t.src=F.attr(n,"embed");var a=F.element(e,"spPr"),s=F.element(a,"xfrm");for(var i of(t.cssStyle.position="relative",F.elements(s)))switch(i.localName){case"ext":t.cssStyle.width=F.lengthAttr(i,"cx",T),t.cssStyle.height=F.lengthAttr(i,"cy",T);break;case"off":t.cssStyle.left=F.lengthAttr(i,"x",T),t.cssStyle.top=F.lengthAttr(i,"y",T)}return t}parseTable(e){var t={type:q.Table,children:[]};return it.foreach(e,e=>{switch(e.localName){case"tr":t.children.push(this.parseTableRow(e));break;case"tblGrid":t.columns=this.parseTableColumns(e);break;case"tblPr":this.parseTableProperties(e,t)}}),t}parseTableColumns(e){var t=[];return it.foreach(e,e=>{if("gridCol"===e.localName)t.push({width:F.lengthAttr(e,"w")})}),t}parseTableProperties(e,t){switch(t.cssStyle={},t.cellStyle={},this.parseDefaultProperties(e,t.cssStyle,t.cellStyle,e=>{switch(e.localName){case"tblStyle":t.styleName=F.attr(e,"val");break;case"tblLook":t.className=ot.classNameOftblLook(e);break;case"tblpPr":this.parseTablePosition(e,t);break;case"tblStyleColBandSize":t.colBandSize=F.intAttr(e,"val");break;case"tblStyleRowBandSize":t.rowBandSize=F.intAttr(e,"val");break;case"hidden":t.cssStyle.display="none";break;default:return!1}return!0}),t.cssStyle["text-align"]){case"center":delete t.cssStyle["text-align"],t.cssStyle["margin-left"]="auto",t.cssStyle["margin-right"]="auto";break;case"right":delete t.cssStyle["text-align"],t.cssStyle["margin-left"]="auto"}}parseTablePosition(e,t){var r=F.lengthAttr(e,"topFromText"),n=F.lengthAttr(e,"bottomFromText"),a=F.lengthAttr(e,"rightFromText"),s=F.lengthAttr(e,"leftFromText");t.cssStyle.float="left",t.cssStyle["margin-bottom"]=ot.addSize(t.cssStyle["margin-bottom"],n),t.cssStyle["margin-left"]=ot.addSize(t.cssStyle["margin-left"],s),t.cssStyle["margin-right"]=ot.addSize(t.cssStyle["margin-right"],a),t.cssStyle["margin-top"]=ot.addSize(t.cssStyle["margin-top"],r)}parseTableRow(e){var t={type:q.Row,children:[]};return it.foreach(e,e=>{switch(e.localName){case"tc":t.children.push(this.parseTableCell(e));break;case"trPr":this.parseTableRowProperties(e,t)}}),t}parseTableRowProperties(e,t){t.cssStyle=this.parseDefaultProperties(e,{},null,e=>{switch(e.localName){case"cnfStyle":t.className=ot.classNameOfCnfStyle(e);break;case"tblHeader":t.isHeader=F.boolAttr(e,"val");break;case"gridBefore":t.gridBefore=F.intAttr(e,"val");break;case"gridAfter":t.gridAfter=F.intAttr(e,"val");break;default:return!1}return!0})}parseTableCell(e){var t={type:q.Cell,children:[]};return it.foreach(e,e=>{switch(e.localName){case"tbl":t.children.push(this.parseTable(e));break;case"p":t.children.push(this.parseParagraph(e));break;case"tcPr":this.parseTableCellProperties(e,t)}}),t}parseTableCellProperties(e,t){t.cssStyle=this.parseDefaultProperties(e,{},null,e=>{switch(e.localName){case"gridSpan":t.span=F.intAttr(e,"val",null);break;case"vMerge":t.verticalMerge=F.attr(e,"val")??"continue";break;case"cnfStyle":t.className=ot.classNameOfCnfStyle(e);break;default:return!1}return!0}),this.parseTableCellVerticalText(e,t)}parseTableCellVerticalText(e,t){const r={btLr:{writingMode:"vertical-rl",transform:"rotate(180deg)"},lrTb:{writingMode:"vertical-lr",transform:"none"},tbRl:{writingMode:"vertical-rl",transform:"none"}};it.foreach(e,e=>{if("textDirection"===e.localName){const n=F.attr(e,"val"),a=r[n]||{writingMode:"horizontal-tb"};t.cssStyle["writing-mode"]=a.writingMode,t.cssStyle.transform=a.transform}})}parseDefaultProperties(e,t=null,r=null,n=null){return t=t||{},it.foreach(e,a=>{if(!(null==n?void 0:n(a)))switch(a.localName){case"jc":t["text-align"]=ot.valueOfJc(a);break;case"textAlignment":t["vertical-align"]=ot.valueOfTextAlignment(a);break;case"color":t.color=it.colorAttr(a,"val",null,Qe);break;case"sz":t["font-size"]=t["min-height"]=F.lengthAttr(a,"val",z);break;case"shd":t["background-color"]=it.colorAttr(a,"fill",null,Je);break;case"highlight":t["background-color"]=it.colorAttr(a,"val",null,tt);break;case"vertAlign":break;case"position":t.verticalAlign=F.lengthAttr(a,"val",z);break;case"tcW":if(this.options.ignoreWidth)break;case"tblW":t.width=ot.valueOfSize(a,"w");break;case"trHeight":this.parseTrHeight(a,t);break;case"strike":t["text-decoration"]=F.boolAttr(a,"val",!0)?"line-through":"none";break;case"b":t["font-weight"]=F.boolAttr(a,"val",!0)?"bold":"normal";break;case"i":t["font-style"]=F.boolAttr(a,"val",!0)?"italic":"normal";break;case"caps":t["text-transform"]=F.boolAttr(a,"val",!0)?"uppercase":"none";break;case"smallCaps":t["font-variant"]=F.boolAttr(a,"val",!0)?"small-caps":"none";break;case"u":this.parseUnderline(a,t);break;case"ind":case"tblInd":this.parseIndentation(a,t);break;case"rFonts":this.parseFont(a,t);break;case"tblBorders":this.parseBorderProperties(a,r||t);break;case"tblCellSpacing":t["border-spacing"]=ot.valueOfMargin(a),t["border-collapse"]="separate";break;case"pBdr":this.parseBorderProperties(a,t);break;case"bdr":t.border=ot.valueOfBorder(a);break;case"tcBorders":this.parseBorderProperties(a,t);break;case"vanish":F.boolAttr(a,"val",!0)&&(t.display="none");break;case"kern":case"noWrap":break;case"tblCellMar":case"tcMar":this.parseMarginProperties(a,r||t);break;case"tblLayout":t["table-layout"]=ot.valueOfTblLayout(a);break;case"vAlign":t["vertical-align"]=ot.valueOfTextAlignment(a);break;case"spacing":"pPr"==e.localName&&this.parseSpacing(a,t);break;case"wordWrap":F.boolAttr(a,"val")&&(t["overflow-wrap"]="break-word");break;case"suppressAutoHyphens":t.hyphens=F.boolAttr(a,"val",!0)?"none":"auto";break;case"lang":t.$lang=F.attr(a,"val");break;case"rtl":case"bidi":F.boolAttr(a,"val",!0)&&(t.direction="rtl");break;case"bCs":case"iCs":case"szCs":case"tabs":case"outlineLvl":case"contextualSpacing":case"tblStyleColBandSize":case"tblStyleRowBandSize":case"webHidden":case"pageBreakBefore":case"suppressLineNumbers":case"keepLines":case"keepNext":case"widowControl":case"bidi":case"rtl":case"noProof":break;default:this.options.debug}}),t}parseUnderline(e,t){var r=F.attr(e,"val");if(null!=r){switch(r){case"dash":case"dashDotDotHeavy":case"dashDotHeavy":case"dashedHeavy":case"dashLong":case"dashLongHeavy":case"dotDash":case"dotDotDash":t["text-decoration"]="underline dashed";break;case"dotted":case"dottedHeavy":t["text-decoration"]="underline dotted";break;case"double":t["text-decoration"]="underline double";break;case"single":case"thick":case"words":t["text-decoration"]="underline";break;case"wave":case"wavyDouble":case"wavyHeavy":t["text-decoration"]="underline wavy";break;case"none":t["text-decoration"]="none"}var n=it.colorAttr(e,"color");n&&(t["text-decoration-color"]=n)}}parseFont(e,t){var r=[F.attr(e,"ascii"),ot.themeValue(e,"asciiTheme"),F.attr(e,"eastAsia")].filter(e=>e).map(e=>_(e));r.length>0&&(t["font-family"]=[...new Set(r)].join(", "))}parseIndentation(e,t){var r=F.lengthAttr(e,"firstLine"),n=F.lengthAttr(e,"hanging"),a=F.lengthAttr(e,"left"),s=F.lengthAttr(e,"start"),i=F.lengthAttr(e,"right"),o=F.lengthAttr(e,"end");r&&(t["text-indent"]=r),n&&(t["text-indent"]=`-${n}`),(a||s)&&(t["margin-inline-start"]=a||s),(i||o)&&(t["margin-inline-end"]=i||o)}parseSpacing(e,t){var r=F.lengthAttr(e,"before"),n=F.lengthAttr(e,"after"),a=F.intAttr(e,"line",null),s=F.attr(e,"lineRule");if(r&&(t["margin-top"]=r),n&&(t["margin-bottom"]=n),null!==a)switch(s){case"auto":t["line-height"]=`${(a/240).toFixed(2)}`;break;case"atLeast":t["line-height"]=`calc(100% + ${a/20}pt)`;break;default:t["line-height"]=t["min-height"]=a/20+"pt"}}parseMarginProperties(e,t){it.foreach(e,e=>{switch(e.localName){case"left":t["padding-left"]=ot.valueOfMargin(e);break;case"right":t["padding-right"]=ot.valueOfMargin(e);break;case"top":t["padding-top"]=ot.valueOfMargin(e);break;case"bottom":t["padding-bottom"]=ot.valueOfMargin(e)}})}parseTrHeight(e,t){F.attr(e,"hRule"),t.height=F.lengthAttr(e,"val")}parseBorderProperties(e,t){it.foreach(e,e=>{switch(e.localName){case"start":case"left":t["border-left"]=ot.valueOfBorder(e);break;case"end":case"right":t["border-right"]=ot.valueOfBorder(e);break;case"top":t["border-top"]=ot.valueOfBorder(e);break;case"bottom":t["border-bottom"]=ot.valueOfBorder(e)}})}}const st=["black","blue","cyan","darkBlue","darkCyan","darkGray","darkGreen","darkMagenta","darkRed","darkYellow","green","lightGray","magenta","none","red","white","yellow"];class it{static foreach(e,t){for(var r=0;r<e.childNodes.length;r++){let n=e.childNodes[r];n.nodeType==Node.ELEMENT_NODE&&t(n)}}static colorAttr(e,t,r=null,n="black"){var a=F.attr(e,t);if(a)return"auto"==a?n:st.includes(a)?a:`#${a}`;var s=F.attr(e,"themeColor");return s?`var(--docx-${s}-color)`:r}static sizeValue(e,t=N){return I(e.textContent,t)}}class ot{static themeValue(e,t){var r=F.attr(e,t);return r?`var(--docx-${r}-font)`:null}static valueOfSize(e,t){var r=N;switch(F.attr(e,"type")){case"dxa":break;case"pct":r=D;break;case"auto":return"auto"}return F.lengthAttr(e,t,r)}static valueOfMargin(e){return F.lengthAttr(e,"w")}static valueOfBorder(e){var t=ot.parseBorderType(F.attr(e,"val"));if("none"==t)return"none";var r=it.colorAttr(e,"color");return`${F.lengthAttr(e,"sz",B)} ${t} ${"auto"==r?et:r}`}static parseBorderType(e){switch(e){case"single":case"dashDotStroked":case"thick":case"thickThinLargeGap":case"thickThinMediumGap":case"thickThinSmallGap":case"thinThickLargeGap":case"thinThickMediumGap":case"thinThickSmallGap":case"thinThickThinLargeGap":case"thinThickThinMediumGap":case"thinThickThinSmallGap":case"threeDEmboss":case"threeDEngrave":case"wave":return"solid";case"dashed":case"dashSmallGap":return"dashed";case"dotDash":case"dotDotDash":case"dotted":return"dotted";case"double":case"doubleWave":case"triple":return"double";case"inset":return"inset";case"nil":case"none":return"none";case"outset":return"outset"}return"solid"}static valueOfTblLayout(e){return"fixed"==F.attr(e,"val")?"fixed":"auto"}static classNameOfCnfStyle(e){const t=F.attr(e,"val");return["first-row","last-row","first-col","last-col","odd-col","even-col","odd-row","even-row","ne-cell","nw-cell","se-cell","sw-cell"].filter((e,r)=>"1"==t[r]).join(" ")}static valueOfJc(e){var t=F.attr(e,"val");switch(t){case"start":case"left":return"left";case"center":return"center";case"end":case"right":return"right";case"both":return"justify"}return t}static valueOfVertAlign(e,t=!1){var r=F.attr(e,"val");switch(r){case"subscript":return"sub";case"superscript":return t?"sup":"super"}return t?null:r}static valueOfTextAlignment(e){var t=F.attr(e,"val");switch(t){case"auto":case"baseline":return"baseline";case"top":return"top";case"center":return"middle";case"bottom":return"bottom"}return t}static addSize(e,t){return null==e?t:null==t?e:`calc(${e} + ${t})`}static classNameOftblLook(e){const t=F.hexAttr(e,"val",0);let r="";return(F.boolAttr(e,"firstRow")||32&t)&&(r+=" first-row"),(F.boolAttr(e,"lastRow")||64&t)&&(r+=" last-row"),(F.boolAttr(e,"firstColumn")||128&t)&&(r+=" first-col"),(F.boolAttr(e,"lastColumn")||256&t)&&(r+=" last-col"),(F.boolAttr(e,"noHBand")||512&t)&&(r+=" no-hband"),(F.boolAttr(e,"noVBand")||1024&t)&&(r+=" no-vband"),r.trim()}}const lt={pos:0,leader:"none",style:"left"};function ct(e,t,r,n=.75){const a=e.closest("p"),s=e.getBoundingClientRect(),i=a.getBoundingClientRect(),o=getComputedStyle(a),l=(null==t?void 0:t.length)>0?t.map(e=>({pos:ht(e.position),leader:e.leader,style:e.style})).sort((e,t)=>e.pos-t.pos):[lt],c=l[l.length-1],h=i.width*n,u=ht(r);let d=c.pos+u;if(d<h)for(;d<h&&l.length<50;d+=u)l.push({...lt,pos:d});const f=parseFloat(o.marginLeft),p=i.left+f,m=(s.left-p)*n,g=l.find(e=>"clear"!=e.style&&e.pos>m);if(null==g)return;let b=1;if("right"==g.style||"center"==g.style){const t=Array.from(a.querySelectorAll(`.${e.className}`)),r=t.indexOf(e)+1,s=document.createRange();s.setStart(e,1),r<t.length?s.setEndBefore(t[r]):s.setEndAfter(a);const o="center"==g.style?.5:1,l=s.getBoundingClientRect(),c=l.left+o*l.width-(i.left-f);b=g.pos-c*n}else b=g.pos-m;switch(e.innerHTML="&nbsp;",e.style.textDecoration="inherit",e.style.wordSpacing=`${b.toFixed(0)}pt`,g.leader){case"dot":case"middleDot":e.style.textDecoration="underline",e.style.textDecorationStyle="dotted";break;case"hyphen":case"heavy":case"underscore":e.style.textDecoration="underline"}}function ht(e){return parseFloat(e)}const ut="http://www.w3.org/2000/svg",dt="http://www.w3.org/1998/Math/MathML";class ft{constructor(e){this.htmlDocument=e,this.className="docx",this.styleMap={},this.currentPart=null,this.tableVerticalMerges=[],this.currentVerticalMerge=null,this.tableCellPositions=[],this.currentCellPosition=null,this.footnoteMap={},this.endnoteMap={},this.currentEndnoteIds=[],this.usedHederFooterParts=[],this.currentTabs=[],this.commentMap={},this.tasks=[],this.postRenderTasks=[]}async render(e,t,r=null,n){var a;this.document=e,this.options=n,this.className=n.className,this.rootSelector=n.inWrapper?`.${this.className}-wrapper`:":root",this.styleMap=null,this.tasks=[],this.options.renderComments&&globalThis.Highlight&&(this.commentHighlight=new Highlight),pt(r=r||t),pt(t),r.appendChild(this.createComment("docxjs library predefined styles")),r.appendChild(this.renderDefaultStyle()),e.themePart&&(r.appendChild(this.createComment("docxjs document theme values")),this.renderTheme(e.themePart,r)),null!=e.stylesPart&&(this.styleMap=this.processStyles(e.stylesPart.styles),r.appendChild(this.createComment("docxjs document styles")),r.appendChild(this.renderStyles(e.stylesPart.styles))),e.numberingPart&&(this.prodessNumberings(e.numberingPart.domNumberings),r.appendChild(this.createComment("docxjs document numbering styles")),r.appendChild(this.renderNumbering(e.numberingPart.domNumberings,r))),e.footnotesPart&&(this.footnoteMap=x(e.footnotesPart.notes,e=>e.id)),e.endnotesPart&&(this.endnoteMap=x(e.endnotesPart.notes,e=>e.id)),e.settingsPart&&(this.defaultTabSize=null==(a=e.settingsPart.settings)?void 0:a.defaultTabStop),!n.ignoreFonts&&e.fontTablePart&&this.renderFontTable(e.fontTablePart,r);var s=this.renderSections(e.documentPart.body);this.options.inWrapper?t.appendChild(this.renderWrapper(s)):mt(t,s),this.commentHighlight&&n.renderComments&&CSS.highlights.set(`${this.className}-comments`,this.commentHighlight),this.postRenderTasks.forEach(e=>e()),await Promise.allSettled(this.tasks),this.refreshTabStops()}renderTheme(e,t){var r,n;const a={},s=null==(r=e.theme)?void 0:r.fontScheme;s&&(s.majorFont&&(a["--docx-majorHAnsi-font"]=s.majorFont.latinTypeface),s.minorFont&&(a["--docx-minorHAnsi-font"]=s.minorFont.latinTypeface));const i=null==(n=e.theme)?void 0:n.colorScheme;if(i)for(let[l,c]of Object.entries(i.colors))a[`--docx-${l}-color`]=`#${c}`;const o=this.styleToString(`.${this.className}`,a);t.appendChild(this.createStyleElement(o))}renderFontTable(e,t){for(let r of e.fonts)for(let e of r.embedFontRefs)this.tasks.push(this.document.loadFont(e.id,e.key).then(n=>{const a={"font-family":_(r.name),src:`url(${n})`};"bold"!=e.type&&"boldItalic"!=e.type||(a["font-weight"]="bold"),"italic"!=e.type&&"boldItalic"!=e.type||(a["font-style"]="italic");const s=this.styleToString("@font-face",a);t.appendChild(this.createComment(`docxjs ${r.name} font`)),t.appendChild(this.createStyleElement(s))}))}processStyleName(e){return e?`${this.className}_${function(e){return null==e?void 0:e.replace(/[ .]+/g,"-").replace(/[&]+/g,"and").toLowerCase()}(e)}`:this.className}processStyles(e){const t=x(e.filter(e=>null!=e.id),e=>e.id);for(const n of e.filter(e=>e.basedOn)){var r=t[n.basedOn];if(r){n.paragraphProps=E(n.paragraphProps,r.paragraphProps),n.runProps=E(n.runProps,r.runProps);for(const e of r.styles){const t=n.styles.find(t=>t.target==e.target);t?this.copyStyleProperties(e.values,t.values):n.styles.push({...e,values:{...e.values}})}}else this.options.debug}for(let n of e)n.cssName=this.processStyleName(n.id);return t}prodessNumberings(e){var t;for(let r of e.filter(e=>e.pStyleName)){const e=this.findStyle(r.pStyleName);(null==(t=null==e?void 0:e.paragraphProps)?void 0:t.numbering)&&(e.paragraphProps.numbering.level=r.level)}}processElement(e){if(e.children)for(var t of e.children)t.parent=e,t.type==q.Table?this.processTable(t):this.processElement(t)}processTable(e){for(var t of e.children)for(var r of t.children)r.cssStyle=this.copyStyleProperties(e.cellStyle,r.cssStyle,["border-left","border-right","border-top","border-bottom","padding-left","padding-right","padding-top","padding-bottom"]),this.processElement(r)}copyStyleProperties(e,t,r=null){if(!e)return t;for(var n of(null==t&&(t={}),null==r&&(r=Object.getOwnPropertyNames(e)),r))e.hasOwnProperty(n)&&!t.hasOwnProperty(n)&&(t[n]=e[n]);return t}createPageElement(e,t){var r=this.createElement("section",{className:e});return t&&(t.pageMargins&&(r.style.paddingLeft=t.pageMargins.left,r.style.paddingRight=t.pageMargins.right,r.style.paddingTop=t.pageMargins.top,r.style.paddingBottom=t.pageMargins.bottom),t.pageSize&&(this.options.ignoreWidth||(r.style.width=t.pageSize.width),this.options.ignoreHeight||(r.style.minHeight=t.pageSize.height))),r}createSectionContent(e){var t=this.createElement("article");return e.columns&&e.columns.numberOfColumns&&(t.style.columnCount=`${e.columns.numberOfColumns}`,t.style.columnGap=e.columns.space,e.columns.separator&&(t.style.columnRule="1px solid black")),t}renderSections(e){const t=[];this.processElement(e);const r=this.splitBySection(e.children,e.props),n=this.groupByPageBreaks(r);let a=null;for(let i=0,o=n.length;i<o;i++){this.currentFootnoteIds=[];let r=n[i][0].sectProps;const l=this.createPageElement(this.className,r);this.renderStyleValues(e.cssStyle,l),this.options.renderHeaders&&this.renderHeaderFooter(r.headerRefs,r,t.length,a!=r,l);for(const e of n[i]){var s=this.createSectionContent(e.sectProps);this.renderElements(e.elements,s),l.appendChild(s),r=e.sectProps}this.options.renderFootnotes&&this.renderNotes(this.currentFootnoteIds,this.footnoteMap,l),this.options.renderEndnotes&&i==o-1&&this.renderNotes(this.currentEndnoteIds,this.endnoteMap,l),this.options.renderFooters&&this.renderHeaderFooter(r.footerRefs,r,t.length,a!=r,l),t.push(l),a=r}return t}renderHeaderFooter(e,t,r,n,a){if(e){var s=(t.titlePage&&n?e.find(e=>"first"==e.type):null)??(r%2==1?e.find(e=>"even"==e.type):null)??e.find(e=>"default"==e.type),i=s&&this.document.findPartByRelId(s.id,this.document.documentPart);if(i){this.currentPart=i,this.usedHederFooterParts.includes(i.path)||(this.processElement(i.rootElement),this.usedHederFooterParts.push(i.path));const[e]=this.renderElements([i.rootElement],a);(null==t?void 0:t.pageMargins)&&(i.rootElement.type===q.Header?(e.style.marginTop=`calc(${t.pageMargins.header} - ${t.pageMargins.top})`,e.style.minHeight=`calc(${t.pageMargins.top} - ${t.pageMargins.header})`):i.rootElement.type===q.Footer&&(e.style.marginBottom=`calc(${t.pageMargins.footer} - ${t.pageMargins.bottom})`,e.style.minHeight=`calc(${t.pageMargins.bottom} - ${t.pageMargins.footer})`)),this.currentPart=null}}}isPageBreakElement(e){return e.type==q.Break&&("lastRenderedPageBreak"==e.break?!this.options.ignoreLastRenderedPageBreak:"page"==e.break)}isPageBreakSection(e,t){var r,n,a,s,i,o;return!!e&&(!!t&&((null==(r=e.pageSize)?void 0:r.orientation)!=(null==(n=t.pageSize)?void 0:n.orientation)||(null==(a=e.pageSize)?void 0:a.width)!=(null==(s=t.pageSize)?void 0:s.width)||(null==(i=e.pageSize)?void 0:i.height)!=(null==(o=t.pageSize)?void 0:o.height)))}splitBySection(e,t){var r,n={sectProps:null,elements:[],pageBreak:!1},a=[n];for(let u of e){if(u.type==q.Paragraph){const e=this.findStyle(u.styleName);(null==(r=null==e?void 0:e.paragraphProps)?void 0:r.pageBreakBefore)&&(n.sectProps=s,n.pageBreak=!0,n={sectProps:null,elements:[],pageBreak:!1},a.push(n))}if(n.elements.push(u),u.type==q.Paragraph){const e=u;var s=e.sectionProps,i=-1,o=-1;if(this.options.breakPages&&e.children&&(i=e.children.findIndex(e=>{var t;return-1!=(o=(null==(t=e.children)?void 0:t.findIndex(this.isPageBreakElement.bind(this)))??-1)})),(s||-1!=i)&&(n.sectProps=s,n.pageBreak=-1!=i,n={sectProps:null,elements:[],pageBreak:!1},a.push(n)),-1!=i){let t=e.children[i],r=o<t.children.length-1;if(i<e.children.length-1||r){var l=u.children,c={...u,children:l.slice(i)};if(u.children=l.slice(0,i),n.elements.push(c),r){let e=t.children,r={...t,children:e.slice(0,o)};u.children.push(r),t.children=e.slice(o)}}}}}let h=null;for(let u=a.length-1;u>=0;u--)null==a[u].sectProps?a[u].sectProps=h??t:h=a[u].sectProps;return a}groupByPageBreaks(e){let t,r=[];const n=[r];for(let a of e)r.push(a),(this.options.ignoreLastRenderedPageBreak||a.pageBreak||this.isPageBreakSection(t,a.sectProps))&&n.push(r=[]),t=a.sectProps;return n.filter(e=>e.length>0)}renderWrapper(e){return this.createElement("div",{className:`${this.className}-wrapper`},e)}renderDefaultStyle(){var e=this.className,t=`\n.${e}-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } \n.${e}-wrapper>section.${e} { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }`;this.options.hideWrapperOnPrint&&(t=`@media not print { ${t} }`);var r=`${t}\n.${e} { color: black; hyphens: auto; text-underline-position: from-font; }\nsection.${e} { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }\nsection.${e}>article { margin-bottom: auto; z-index: 1; }\nsection.${e}>footer { z-index: 1; }\n.${e} table { border-collapse: collapse; }\n.${e} table td, .${e} table th { vertical-align: top; }\n.${e} p { margin: 0pt; min-height: 1em; }\n.${e} span { white-space: pre-wrap; overflow-wrap: break-word; }\n.${e} a { color: inherit; text-decoration: inherit; }\n.${e} svg { fill: transparent; }\n`;return this.options.renderComments&&(r+=`\n.${e}-comment-ref { cursor: default; }\n.${e}-comment-popover { display: none; z-index: 1000; padding: 0.5rem; background: white; position: absolute; box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.25); width: 30ch; }\n.${e}-comment-ref:hover~.${e}-comment-popover { display: block; }\n.${e}-comment-author,.${e}-comment-date { font-size: 0.875rem; color: #888; }\n`),this.createStyleElement(r)}renderNumbering(e,t){var r="",n=[];for(var a of e){var s=`p.${this.numberingClass(a.id,a.level)}`,i="none";if(a.bullet){let e=`--${this.className}-${a.bullet.src}`.toLowerCase();r+=this.styleToString(`${s}:before`,{content:"' '",display:"inline-block",background:`var(${e})`},a.bullet.style),this.tasks.push(this.document.loadNumberingImage(a.bullet.src).then(r=>{var n=`${this.rootSelector} { ${e}: url(${r}) }`;t.appendChild(this.createStyleElement(n))}))}else if(a.levelText){let e=this.numberingCounter(a.id,a.level);const t=e+" "+(a.start-1);a.level>0&&(r+=this.styleToString(`p.${this.numberingClass(a.id,a.level-1)}`,{"counter-set":t})),n.push(t),r+=this.styleToString(`${s}:before`,{content:this.levelTextToContent(a.levelText,a.suff,a.id,this.numFormatToCssValue(a.format)),"counter-increment":e,...a.rStyle})}else i=this.numFormatToCssValue(a.format);r+=this.styleToString(s,{display:"list-item","list-style-position":"inside","list-style-type":i,...a.pStyle})}return n.length>0&&(r+=this.styleToString(this.rootSelector,{"counter-reset":n.join(" ")})),this.createStyleElement(r)}renderStyles(e){var t="";const r=this.styleMap,n=x(e.filter(e=>e.isDefault),e=>e.target);for(const o of e){var a=o.styles;if(o.linked){var s=o.linked&&r[o.linked];s?a=a.concat(s.styles):this.options.debug}for(const e of a){var i=`${o.target??""}.${o.cssName}`;o.target!=e.target&&(i+=` ${e.target}`),n[o.target]==o&&(i=`.${this.className} ${o.target}, `+i),t+=this.styleToString(i,e.values)}}return this.createStyleElement(t)}renderNotes(e,t,r){var n=e.map(e=>t[e]).filter(e=>e);if(n.length>0){var a=this.createElement("ol",null,this.renderElements(n));r.appendChild(a)}}renderElement(e){switch(e.type){case q.Paragraph:return this.renderParagraph(e);case q.BookmarkStart:return this.renderBookmarkStart(e);case q.BookmarkEnd:return null;case q.Run:return this.renderRun(e);case q.Table:return this.renderTable(e);case q.Row:return this.renderTableRow(e);case q.Cell:return this.renderTableCell(e);case q.Hyperlink:return this.renderHyperlink(e);case q.SmartTag:return this.renderSmartTag(e);case q.Drawing:return this.renderDrawing(e);case q.Image:return this.renderImage(e);case q.Text:case q.Text:return this.renderText(e);case q.DeletedText:return this.renderDeletedText(e);case q.Tab:return this.renderTab(e);case q.Symbol:return this.renderSymbol(e);case q.Break:return this.renderBreak(e);case q.Footer:return this.renderContainer(e,"footer");case q.Header:return this.renderContainer(e,"header");case q.Footnote:case q.Endnote:return this.renderContainer(e,"li");case q.FootnoteReference:return this.renderFootnoteReference(e);case q.EndnoteReference:return this.renderEndnoteReference(e);case q.NoBreakHyphen:return this.createElement("wbr");case q.VmlPicture:return this.renderVmlPicture(e);case q.VmlElement:return this.renderVmlElement(e);case q.MmlMath:return this.renderContainerNS(e,dt,"math",{xmlns:dt});case q.MmlMathParagraph:return this.renderContainer(e,"span");case q.MmlFraction:return this.renderContainerNS(e,dt,"mfrac");case q.MmlBase:return this.renderContainerNS(e,dt,e.parent.type==q.MmlMatrixRow?"mtd":"mrow");case q.MmlNumerator:case q.MmlDenominator:case q.MmlFunction:case q.MmlLimit:case q.MmlBox:return this.renderContainerNS(e,dt,"mrow");case q.MmlGroupChar:return this.renderMmlGroupChar(e);case q.MmlLimitLower:return this.renderContainerNS(e,dt,"munder");case q.MmlMatrix:return this.renderContainerNS(e,dt,"mtable");case q.MmlMatrixRow:return this.renderContainerNS(e,dt,"mtr");case q.MmlRadical:return this.renderMmlRadical(e);case q.MmlSuperscript:return this.renderContainerNS(e,dt,"msup");case q.MmlSubscript:return this.renderContainerNS(e,dt,"msub");case q.MmlDegree:case q.MmlSuperArgument:case q.MmlSubArgument:return this.renderContainerNS(e,dt,"mn");case q.MmlFunctionName:return this.renderContainerNS(e,dt,"ms");case q.MmlDelimiter:return this.renderMmlDelimiter(e);case q.MmlRun:return this.renderMmlRun(e);case q.MmlNary:return this.renderMmlNary(e);case q.MmlPreSubSuper:return this.renderMmlPreSubSuper(e);case q.MmlBar:return this.renderMmlBar(e);case q.MmlEquationArray:return this.renderMllList(e);case q.Inserted:return this.renderInserted(e);case q.Deleted:return this.renderDeleted(e);case q.CommentRangeStart:return this.renderCommentRangeStart(e);case q.CommentRangeEnd:return this.renderCommentRangeEnd(e);case q.CommentReference:return this.renderCommentReference(e);case q.AltChunk:return this.renderAltChunk(e)}return null}renderElements(e,t){if(null==e)return null;var r=e.flatMap(e=>this.renderElement(e)).filter(e=>null!=e);return t&&mt(t,r),r}renderContainer(e,t,r){return this.createElement(t,r,this.renderElements(e.children))}renderContainerNS(e,t,r,n){return this.createElementNS(t,r,n,this.renderElements(e.children))}renderParagraph(e){var t,r,n=this.renderContainer(e,"p");const a=this.findStyle(e.styleName);e.tabs??(e.tabs=null==(t=null==a?void 0:a.paragraphProps)?void 0:t.tabs),this.renderClass(e,n),this.renderStyleValues(e.cssStyle,n),this.renderCommonProperties(n.style,e);const s=e.numbering??(null==(r=null==a?void 0:a.paragraphProps)?void 0:r.numbering);return s&&n.classList.add(this.numberingClass(s.id,s.level)),n}renderRunProperties(e,t){this.renderCommonProperties(e,t)}renderCommonProperties(e,t){null!=t&&(t.color&&(e.color=t.color),t.fontSize&&(e["font-size"]=t.fontSize))}renderHyperlink(e){var t=this.renderContainer(e,"a");this.renderStyleValues(e.cssStyle,t);let r="";if(e.id){const t=this.document.documentPart.rels.find(t=>t.id==e.id&&"External"===t.targetMode);r=(null==t?void 0:t.target)??r}return e.anchor&&(r+=`#${e.anchor}`),t.href=r,t}renderSmartTag(e){return this.renderContainer(e,"span")}renderCommentRangeStart(e){var t;if(!this.options.renderComments)return null;const r=new Range;null==(t=this.commentHighlight)||t.add(r);const n=this.htmlDocument.createComment(`start of comment #${e.id}`);return this.later(()=>r.setStart(n,0)),this.commentMap[e.id]=r,n}renderCommentRangeEnd(e){if(!this.options.renderComments)return null;const t=this.commentMap[e.id],r=this.htmlDocument.createComment(`end of comment #${e.id}`);return this.later(()=>null==t?void 0:t.setEnd(r,0)),r}renderCommentReference(e){var t;if(!this.options.renderComments)return null;var r=null==(t=this.document.commentsPart)?void 0:t.commentMap[e.id];if(!r)return null;const n=new DocumentFragment,a=this.createElement("span",{className:`${this.className}-comment-ref`},["💬"]),s=this.createElement("div",{className:`${this.className}-comment-popover`});return this.renderCommentContent(r,s),n.appendChild(this.htmlDocument.createComment(`comment #${r.id} by ${r.author} on ${r.date}`)),n.appendChild(a),n.appendChild(s),n}renderAltChunk(e){if(!this.options.renderAltChunks)return null;var t=this.createElement("iframe");return this.tasks.push(this.document.loadAltChunk(e.id,this.currentPart).then(e=>{t.srcdoc=e})),t}renderCommentContent(e,t){t.appendChild(this.createElement("div",{className:`${this.className}-comment-author`},[e.author])),t.appendChild(this.createElement("div",{className:`${this.className}-comment-date`},[new Date(e.date).toLocaleString()])),this.renderElements(e.children,t)}renderDrawing(e){var t=this.renderContainer(e,"div");return t.style.display="inline-block",t.style.position="relative",t.style.textIndent="0px",this.renderStyleValues(e.cssStyle,t),t}renderImage(e){let t=this.createElement("img");return this.renderStyleValues(e.cssStyle,t),this.document&&this.tasks.push(this.document.loadDocumentImage(e.src,this.currentPart).then(e=>{t.src=e})),t}renderText(e){return this.htmlDocument.createTextNode(e.text)}renderDeletedText(e){return this.options.renderEndnotes?this.htmlDocument.createTextNode(e.text):null}renderBreak(e){return"textWrapping"==e.break?this.createElement("br"):null}renderInserted(e){return this.options.renderChanges?this.renderContainer(e,"ins"):this.renderElements(e.children)}renderDeleted(e){return this.options.renderChanges?this.renderContainer(e,"del"):null}renderSymbol(e){var t=this.createElement("span");return t.style.fontFamily=e.font,t.innerHTML=`&#x${e.char};`,t}renderFootnoteReference(e){var t=this.createElement("sup");return this.currentFootnoteIds.push(e.id),t.textContent=`${this.currentFootnoteIds.length}`,t}renderEndnoteReference(e){var t=this.createElement("sup");return this.currentEndnoteIds.push(e.id),t.textContent=`${this.currentEndnoteIds.length}`,t}renderTab(e){var t,r=this.createElement("span");if(r.innerHTML="&emsp;",this.options.experimental){r.className=this.tabStopClass();var n=null==(t=function(e,t){var r=e.parent;for(;null!=r&&r.type!=t;)r=r.parent;return r}(e,q.Paragraph))?void 0:t.tabs;this.currentTabs.push({stops:n,span:r})}return r}renderBookmarkStart(e){return this.createElement("span",{id:e.name})}renderRun(e){if(e.fieldRun)return null;const t=this.createElement("span");if(e.id&&(t.id=e.id),this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),e.verticalAlign){const r=this.createElement(e.verticalAlign);this.renderElements(e.children,r),t.appendChild(r)}else this.renderElements(e.children,t);return t}renderTable(e){let t=this.createElement("table");return this.tableCellPositions.push(this.currentCellPosition),this.tableVerticalMerges.push(this.currentVerticalMerge),this.currentVerticalMerge={},this.currentCellPosition={col:0,row:0},e.columns&&t.appendChild(this.renderTableColumns(e.columns)),this.renderClass(e,t),this.renderElements(e.children,t),this.renderStyleValues(e.cssStyle,t),this.currentVerticalMerge=this.tableVerticalMerges.pop(),this.currentCellPosition=this.tableCellPositions.pop(),t}renderTableColumns(e){let t=this.createElement("colgroup");for(let r of e){let e=this.createElement("col");r.width&&(e.style.width=r.width),t.appendChild(e)}return t}renderTableRow(e){let t=this.createElement("tr");return this.currentCellPosition.col=0,e.gridBefore&&t.appendChild(this.renderTableCellPlaceholder(e.gridBefore)),this.renderClass(e,t),this.renderElements(e.children,t),this.renderStyleValues(e.cssStyle,t),e.gridAfter&&t.appendChild(this.renderTableCellPlaceholder(e.gridAfter)),this.currentCellPosition.row++,t}renderTableCellPlaceholder(e){const t=this.createElement("td",{colSpan:e});return t.style.border="none",t}renderTableCell(e){let t=this.renderContainer(e,"td");const r=this.currentCellPosition.col;return e.verticalMerge?"restart"==e.verticalMerge?(this.currentVerticalMerge[r]=t,t.rowSpan=1):this.currentVerticalMerge[r]&&(this.currentVerticalMerge[r].rowSpan+=1,t.style.display="none"):this.currentVerticalMerge[r]=null,this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),e.span&&(t.colSpan=e.span),this.currentCellPosition.col+=t.colSpan,t}renderVmlPicture(e){return this.renderContainer(e,"div")}renderVmlElement(e){var t,r,n=this.createSvgElement("svg");n.setAttribute("style",e.cssStyleText);const a=this.renderVmlChildElement(e);return(null==(t=e.imageHref)?void 0:t.id)&&this.tasks.push(null==(r=this.document)?void 0:r.loadDocumentImage(e.imageHref.id,this.currentPart).then(e=>a.setAttribute("href",e))),n.appendChild(a),requestAnimationFrame(()=>{const e=n.firstElementChild.getBBox();n.setAttribute("width",`${Math.ceil(e.x+e.width)}`),n.setAttribute("height",`${Math.ceil(e.y+e.height)}`)}),n}renderVmlChildElement(e){const t=this.createSvgElement(e.tagName);Object.entries(e.attrs).forEach(([e,r])=>t.setAttribute(e,r));for(let r of e.children)r.type==q.VmlElement?t.appendChild(this.renderVmlChildElement(r)):t.appendChild(...A(this.renderElement(r)));return t}renderMmlRadical(e){var t;const r=e.children.find(e=>e.type==q.MmlBase);if(null==(t=e.props)?void 0:t.hideDegree)return this.createElementNS(dt,"msqrt",null,this.renderElements([r]));const n=e.children.find(e=>e.type==q.MmlDegree);return this.createElementNS(dt,"mroot",null,this.renderElements([r,n]))}renderMmlDelimiter(e){const t=[];return t.push(this.createElementNS(dt,"mo",null,[e.props.beginChar??"("])),t.push(...this.renderElements(e.children)),t.push(this.createElementNS(dt,"mo",null,[e.props.endChar??")"])),this.createElementNS(dt,"mrow",null,t)}renderMmlNary(e){var t;const r=[],n=x(e.children,e=>e.type),a=n[q.MmlSuperArgument],s=n[q.MmlSubArgument],i=a?this.createElementNS(dt,"mo",null,A(this.renderElement(a))):null,o=s?this.createElementNS(dt,"mo",null,A(this.renderElement(s))):null,l=this.createElementNS(dt,"mo",null,[(null==(t=e.props)?void 0:t.char)??"∫"]);return i||o?r.push(this.createElementNS(dt,"munderover",null,[l,o,i])):i?r.push(this.createElementNS(dt,"mover",null,[l,i])):o?r.push(this.createElementNS(dt,"munder",null,[l,o])):r.push(l),r.push(...this.renderElements(n[q.MmlBase].children)),this.createElementNS(dt,"mrow",null,r)}renderMmlPreSubSuper(e){const t=[],r=x(e.children,e=>e.type),n=r[q.MmlSuperArgument],a=r[q.MmlSubArgument],s=n?this.createElementNS(dt,"mo",null,A(this.renderElement(n))):null,i=a?this.createElementNS(dt,"mo",null,A(this.renderElement(a))):null,o=this.createElementNS(dt,"mo",null);return t.push(this.createElementNS(dt,"msubsup",null,[o,i,s])),t.push(...this.renderElements(r[q.MmlBase].children)),this.createElementNS(dt,"mrow",null,t)}renderMmlGroupChar(e){const t="bot"===e.props.verticalJustification?"mover":"munder",r=this.renderContainerNS(e,dt,t);return e.props.char&&r.appendChild(this.createElementNS(dt,"mo",null,[e.props.char])),r}renderMmlBar(e){const t=this.renderContainerNS(e,dt,"mrow");switch(e.props.position){case"top":t.style.textDecoration="overline";break;case"bottom":t.style.textDecoration="underline"}return t}renderMmlRun(e){const t=this.createElementNS(dt,"ms",null,this.renderElements(e.children));return this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),t}renderMllList(e){const t=this.createElementNS(dt,"mtable");this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t);for(let r of this.renderElements(e.children))t.appendChild(this.createElementNS(dt,"mtr",null,[this.createElementNS(dt,"mtd",null,[r])]));return t}renderStyleValues(e,t){for(let r in e)r.startsWith("$")?t.setAttribute(r.slice(1),e[r]):t.style[r]=e[r]}renderClass(e,t){e.className&&(t.className=e.className),e.styleName&&t.classList.add(this.processStyleName(e.styleName))}findStyle(e){var t;return e&&(null==(t=this.styleMap)?void 0:t[e])}numberingClass(e,t){return`${this.className}-num-${e}-${t}`}tabStopClass(){return`${this.className}-tab-stop`}styleToString(e,t,r=null){let n=`${e} {\r\n`;for(const a in t)a.startsWith("$")||(n+=`  ${a}: ${t[a]};\r\n`);return r&&(n+=r),n+"}\r\n"}numberingCounter(e,t){return`${this.className}-num-${e}-${t}`}levelTextToContent(e,t,r,n){return`"${e.replace(/%\d*/g,e=>{let t=parseInt(e.substring(1),10)-1;return`"counter(${this.numberingCounter(r,t)}, ${n})"`})}${{tab:"\\9",space:"\\a0"}[t]??""}"`}numFormatToCssValue(e){return{none:"none",bullet:"disc",decimal:"decimal",lowerLetter:"lower-alpha",upperLetter:"upper-alpha",lowerRoman:"lower-roman",upperRoman:"upper-roman",decimalZero:"decimal-leading-zero",aiueo:"katakana",aiueoFullWidth:"katakana",chineseCounting:"simp-chinese-informal",chineseCountingThousand:"simp-chinese-informal",chineseLegalSimplified:"simp-chinese-formal",chosung:"hangul-consonant",ideographDigital:"cjk-ideographic",ideographTraditional:"cjk-heavenly-stem",ideographLegalTraditional:"trad-chinese-formal",ideographZodiac:"cjk-earthly-branch",iroha:"katakana-iroha",irohaFullWidth:"katakana-iroha",japaneseCounting:"japanese-informal",japaneseDigitalTenThousand:"cjk-decimal",japaneseLegal:"japanese-formal",thaiNumbers:"thai",koreanCounting:"korean-hangul-formal",koreanDigital:"korean-hangul-formal",koreanDigital2:"korean-hanja-informal",hebrew1:"hebrew",hebrew2:"hebrew",hindiNumbers:"devanagari",ganada:"hangul",taiwaneseCounting:"cjk-ideographic",taiwaneseCountingThousand:"cjk-ideographic",taiwaneseDigital:"cjk-decimal"}[e]??e}refreshTabStops(){this.options.experimental&&setTimeout(()=>{const e=function(e=document.body){const t=document.createElement("div");t.style.width="100pt",e.appendChild(t);const r=100/t.offsetWidth;return e.removeChild(t),r}();for(let t of this.currentTabs)ct(t.span,t.stops,this.defaultTabSize,e)},500)}createElementNS(e,t,r,n){var a=e?this.htmlDocument.createElementNS(e,t):this.htmlDocument.createElement(t);return Object.assign(a,r),n&&mt(a,n),a}createElement(e,t,r){return this.createElementNS(void 0,e,t,r)}createSvgElement(e,t,r){return this.createElementNS(ut,e,t,r)}createStyleElement(e){return this.createElement("style",{innerHTML:e})}createComment(e){return this.htmlDocument.createComment(e)}later(e){this.postRenderTasks.push(e)}}function pt(e){e.innerHTML=""}function mt(e,t){t.forEach(t=>{return e.appendChild("string"==typeof(r=t)||r instanceof String?document.createTextNode(t):t);var r})}const gt={ignoreHeight:!1,ignoreWidth:!1,ignoreFonts:!1,breakPages:!0,debug:!1,experimental:!1,className:"docx",inWrapper:!0,hideWrapperOnPrint:!1,trimXmlDeclaration:!0,ignoreLastRenderedPageBreak:!0,renderHeaders:!0,renderFooters:!0,renderFootnotes:!0,renderEndnotes:!0,useBase64URL:!1,renderChanges:!1,renderComments:!1,renderAltChunks:!0};async function bt(e,t,r,n){const a=await function(e,t){const r={...gt,...t};return Le.load(e,new at(r),r)}(e,n);return await async function(e,t,r,n){const a={...gt,...n},s=new ft(window.document);return await s.render(e,t,r,a)}(a,t,r,n),a}const yt=m(r({__name:"PreviewExpResult",props:n(["expFile"],{modelValue:{type:Boolean,required:!0,default:!1},modelModifiers:{}}),emits:n(["stopPreview"],["update:modelValue"]),setup(e,{emit:t}){let r=e;const n=a(),p=s(e,"modelValue"),m=t,g=()=>{m("stopPreview")};return i(()=>{o(()=>{bt(r.expFile,n.value)})}),(e,t)=>{const r=f;return c(),l("div",null,[h(r,{modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=e=>p.value=e),"close-on-click-modal":!1,"close-on-press-escape":!1,fullscreen:!0,"append-to-body":"","destroy-on-close":"",title:"文件预览",onBeforeClose:g},{default:u(()=>[d("div",null,[d("div",{ref_key:"docxContainer",ref:n,class:"docx-preview"},null,512)])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-7bf6fa28"]]);export{yt as default};
