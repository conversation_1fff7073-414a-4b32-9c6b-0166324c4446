import{t as e,v as o,ba as l,z as a,B as n,bE as t,r as s,bL as u,c as r,J as c,I as d,o as i,bM as p,aa as f,H as y,a2 as v}from"./index.Ckm1SagX.js";import{t as m}from"./index.C0OsJ5su.js";import{U as C}from"./event.BwRzfsZt.js";import{u as b}from"./index.BjYFza3j.js";import{u as g}from"./index.Dh_vcBr5.js";const B=e({center:Boolean,alignCenter:Boolean,closeIcon:{type:o},draggable:Boolean,overflow:Boolean,fullscreen:Boolean,headerClass:String,bodyClass:String,footerClass:String,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),S={close:()=>!0},x=e({...B,appendToBody:Boolean,appendTo:{type:m.to.type,default:"body"},beforeClose:{type:a(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,headerClass:String,bodyClass:String,footerClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:Boolean,headerAriaLevel:{type:String,default:"2"}}),I={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[C]:e=>l(e),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},D=(e,o)=>{var l;const a=n().emit,{nextZIndex:m}=t();let B="";const S=g(),x=g(),I=s(!1),D=s(!1),h=s(!1),F=s(null!=(l=e.zIndex)?l:m());let z,A;const O=u("namespace",p),w=r(()=>{const o={},l=`--${O.value}-dialog`;return e.fullscreen||(e.top&&(o[`${l}-margin-top`]=e.top),e.width&&(o[`${l}-width`]=c(e.width))),o}),E=r(()=>e.alignCenter?{display:"flex"}:{});function L(){null==A||A(),null==z||z(),e.openDelay&&e.openDelay>0?({stop:z}=f(()=>M(),e.openDelay)):M()}function j(){null==z||z(),null==A||A(),e.closeDelay&&e.closeDelay>0?({stop:A}=f(()=>N(),e.closeDelay)):N()}function k(){e.beforeClose?e.beforeClose(function(e){e||(D.value=!0,I.value=!1)}):j()}function M(){y&&(I.value=!0)}function N(){I.value=!1}return e.lockScroll&&b(I),d(()=>e.zIndex,()=>{var o;F.value=null!=(o=e.zIndex)?o:m()}),d(()=>e.modelValue,l=>{var n;l?(D.value=!1,L(),h.value=!0,F.value=null!=(n=e.zIndex)?n:m(),v(()=>{a("open"),o.value&&(o.value.parentElement.scrollTop=0,o.value.parentElement.scrollLeft=0,o.value.scrollTop=0)})):I.value&&j()}),d(()=>e.fullscreen,e=>{o.value&&(e?(B=o.value.style.transform,o.value.style.transform=""):o.value.style.transform=B)}),i(()=>{e.modelValue&&(I.value=!0,h.value=!0,L())}),{afterEnter:function(){a("opened")},afterLeave:function(){a("closed"),a(C,!1),e.destroyOnClose&&(h.value=!1)},beforeLeave:function(){a("close")},handleClose:k,onModalClick:function(){e.closeOnClickModal&&k()},close:j,doClose:N,onOpenAutoFocus:function(){a("openAutoFocus")},onCloseAutoFocus:function(){a("closeAutoFocus")},onCloseRequested:function(){e.closeOnPressEscape&&k()},onFocusoutPrevented:function(e){var o;"pointer"===(null==(o=e.detail)?void 0:o.focusReason)&&e.preventDefault()},titleId:S,bodyId:x,closed:D,style:w,overlayDialogStyle:E,rendered:h,visible:I,zIndex:F}};export{x as a,S as b,B as c,I as d,D as u};
