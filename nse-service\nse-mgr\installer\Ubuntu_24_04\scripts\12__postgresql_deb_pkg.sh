#!/bin/bash

source "$(dirname "$0")/00__config.sh"

# ==================== 检查 PostgreSQL 是否已安装（包或服务）====================
is_postgresql_installed() {
    local pg_version="17"
    local package_name="postgresql-$pg_version"
    local service_name="postgresql@${pg_version}-main"
    local initdb_path="/usr/lib/postgresql/${pg_version}/bin/initdb"

    # 方法1：检查包是否安装
    if ! dpkg-query -W -f='${Status}' "$package_name" 2>/dev/null | grep -q "install ok installed"; then
        return 1
    fi

    # 方法2：检查关键二进制文件是否存在（防“残骸包”）
    if [ ! -x "$initdb_path" ]; then
        log "❌ PostgreSQL $pg_version 包已安装，但文件缺失: $initdb_path"
        log "💡 请运行: sudo apt install --reinstall postgresql-$pg_version"
        return 1
    fi

    # 方法3：检查服务是否存在（可选）
    if systemctl list-units --full --type=service 2>/dev/null | grep -q "$service_name"; then
        return 0
    fi

    # 所有检查通过
    return 0
}

# ==================== 安装 .deb 包（仅当未安装时）====================
install_postgresql_debs() {
    log "开始安装 PostgreSQL 17 离线 .deb 包..."

    local pkg_dir="$SOFTS_DIR/postgresql17-offline-complete-pkgs"
    if [ ! -d "$pkg_dir" ]; then
        error_exit "未找到 .deb 包目录: $pkg_dir"
    fi

    cd "$pkg_dir" || error_exit "无法进入目录: $pkg_dir"

    local ordered_debs=(
        "libc6_2.39-0ubuntu8.5_amd64.deb"
        "libgcc-s1_14.2.0-4ubuntu2~24.04_amd64.deb"
        "libcrypt1_1%3a4.4.36-4build1_amd64.deb"
        "libssl3t64_3.0.13-0ubuntu3.5_amd64.deb"
        "libreadline8t64_8.2-4build1_amd64.deb"
        "libtinfo6_6.4+20240113-1ubuntu2_amd64.deb"
        "liblzma5_5.6.1+really5.4.5-1ubuntu0.2_amd64.deb"
        "libzstd1_1.5.5+dfsg2-2build1.1_amd64.deb"
        "liblz4-1_1.9.4-1build1.1_amd64.deb"
        "libselinux1_3.5-2ubuntu2.1_amd64.deb"
        "libpcre2-8-0_10.42-4ubuntu2.1_amd64.deb"
        "libunistring5_1.1-2build1.1_amd64.deb"
        "libuuid1_2.39.3-9ubuntu6.3_amd64.deb"
        "libxml2_2.9.14+dfsg-1.3ubuntu3.4_amd64.deb"
        "libxslt1.1_1.1.39-0exp1ubuntu0.24.04.2_amd64.deb"
        "libicu74_74.2-1ubuntu3.1_amd64.deb"
        "libgdbm6t64_1.23-5.1build1_amd64.deb"
        "libgdbm-compat4t64_1.23-5.1build1_amd64.deb"
        "libdb5.3t64_5.3.28+dfsg2-7_amd64.deb"
        "libedit2_3.1-20230828-1build1_amd64.deb"
        "libperl5.38t64_5.38.2-3.2ubuntu0.2_amd64.deb"
        "perl-base_5.38.2-3.2ubuntu0.2_amd64.deb"
        "libllvm19_19.1.1-1ubuntu1~24.04.2_amd64.deb"
        "libio-pty-perl_1%3a1.20-1build2_amd64.deb"
        "libipc-run-perl_20231003.0-1_all.deb"
        "libjson-perl_4.10000-1_all.deb"
        "ssl-cert_1.1.2ubuntu1_all.deb"
        "libpq5_17.6-1.pgdg24.04+1_amd64.deb"
        "postgresql-client-common_281.pgdg24.04+1_all.deb"
        "postgresql-common-dev_281.pgdg24.04+1_all.deb"
        "postgresql-common_281.pgdg24.04+1_all.deb"
        "readline-common_8.2-4build1_all.deb"
        "netbase_6.4_all.deb"
        "sysvinit-utils_3.08-6ubuntu3_amd64.deb"
        "dpkg_1.22.6ubuntu6.1_amd64.deb"
        "tar_1.35+dfsg-3build1_amd64.deb"
        "adduser_3.137ubuntu1_all.deb"
        "passwd_1%3a4.13+dfsg1-4ubuntu3.2_amd64.deb"
        "libpam0g_1.5.3-5ubuntu5.4_amd64.deb"
        "libpam-modules_1.5.3-5ubuntu5.4_amd64.deb"
        "libpam-modules-bin_1.5.3-5ubuntu5.4_amd64.deb"
        "libaudit1_1%3a3.1.2-2.1build1.1_amd64.deb"
        "libaudit-common_1%3a3.1.2-2.1build1.1_all.deb"
        "libcap2_1%3a2.66-5ubuntu2.2_amd64.deb"
        "libcap-ng0_0.8.4-2build2_amd64.deb"
        "libcom-err2_1.47.0-2.4~exp1ubuntu4.1_amd64.deb"
        "libkeyutils1_1.6.3-3build1_amd64.deb"
        "libmd0_1.1.0-2build1.1_amd64.deb"
        "libsepol2_3.5-2build1_amd64.deb"
        "libsemanage2_3.5-1build5_amd64.deb"
        "libsemanage-common_3.5-1build5_all.deb"
        "libacl1_2.3.2-1build1.1_amd64.deb"
        "libbsd0_0.12.1-1build1.1_amd64.deb"
        "libsystemd0_255.4-1ubuntu8_amd64.deb"
        "init-system-helpers_1.66ubuntu1_all.deb"
        "m4_1.4.19-4build1_amd64.deb"
        "bison_3.8.2+dfsg-1build2_amd64.deb"
        "flex_2.6.4-6_amd64.deb"
        "libpkgconf3_1.8.1-2build1_amd64.deb"
        "pkgconf-bin_1.8.1-2build1_amd64.deb"
        "pkgconf_1.8.1-2build1_amd64.deb"
        "debconf_1.5.86ubuntu1_all.deb"
        "ucf_3.0043+nmu1_all.deb"
        "lsb-base_11.6_all.deb"
        "sensible-utils_0.0.22_all.deb"
        "tzdata_2025b-0ubuntu0.24.04.1_all.deb"
        "locales_2.39-0ubuntu8.5_all.deb"
        "locales-all_2.39-0ubuntu8.5_amd64.deb"
        "postgresql-client-17_17.6-1.pgdg24.04+1_amd64.deb"
        "postgresql-17_17.6-1.pgdg24.04+1_amd64.deb"
    )

    local installed_any=false

    for deb in "${ordered_debs[@]}"; do
        if [ ! -f "$deb" ]; then
            log "⚠️  文件不存在: $deb，跳过"
            continue
        fi

        package_name=$(dpkg-deb -f "$deb" Package 2>/dev/null || echo "unknown")
        if dpkg-query -W -f='${Status}' "$package_name" 2>/dev/null | grep -q "install ok installed"; then
            log "✅ 包已安装: $package_name，跳过"
            continue
        fi

        log "📦 安装: $package_name"
        if sudo dpkg -i "$deb"; then
            log "✅ 安装成功: $package_name"
        else
            log "❌ 安装失败: $package_name"
            sudo apt --fix-broken install -y
            if ! sudo dpkg -i "$deb"; then
                error_exit "多次尝试后仍无法安装: $package_name"
            fi
            installed_any=true
        fi
    done

    if [ "$installed_any" = true ]; then
        log "✅ 有新包安装，修复依赖..."
        sudo apt --fix-broken install -y
    else
        log "✅ 所有必需的 .deb 包已安装，跳过安装阶段"
    fi
}

# ==================== 主流程 ====================

log "开始安装PostgreSQL所需离线包依赖库..."

# 检查 PostgreSQL 是否已安装
#if is_postgresql_installed; then
#    log "✅ PostgreSQL 已安装，跳过 .deb 包安装。"
#else
#    log "📦 PostgreSQL 未安装，开始安装离线包..."
    install_postgresql_debs
#fi