/**
 * 分页查询参数
 */
export interface TeachingPageQuery {
  pageNumber: number;
  pageSize: number;
  /** 课程名称 */
  courseName?: string;
  /** 班级名称 */
  className?: string;
  /** 状态 */
  status?: string;
  /** 人数 */
  studentCntMin?: number;
  studentCntMax?: number;
  /** 实验个数 */
  expCntMin?: number;
  expCntMax?: number;
  /** 创建人 */
  createdBy: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}

/**
 * 课程表单
 */
export interface TeachingCourseForm {
  /** 课程ID */
  id?: string;
  /** 课程名称 */
  courseName: string;
  className: string;
  userIds: string[];
}

/**
 * 课程视图对象
 */
export interface TeachingVO {
  /** 课程ID */
  id: string;
  /** 课程名称 */
  courseName: string;
  /** 班级名称 */
  className?: string;
  /** 状态 */
  status: string;
  /** 人数 */
  studentCnt: number;
  /** 实验个数 */
  expCnt: number;
  /** 创建人 */
  createdBy: string;
  /** 创建时间 */
  createdDate: string;
}

export interface PageResult<T> {
  total: number;
  rows: T[];
}

/**
 * 学生管理分页查询参数
 */
export interface StudentPageQuery {
  pageNumber: number;
  pageSize: number;
  courseId: string;
  account?: string;
  className?: string;
  name?: string;
  /** 创建人 */
  createdBy?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}

export interface StudentPageResult<T> {
  courseName: string;
  className: string;
  teacherName: string;
  studentCnt: number;
  total: number;
  rows: T[];
}

export interface StudentVO {
  id: string;
  studentId: string;
  account: string;
  name: string;
  /** 班级名称 */
  className?: string;
  /** 创建人 */
  createdBy: string;
  /** 创建时间 */
  createdDate: string;
}

export interface CourseStartCheckResult {
  canStart: boolean;
  message: string;
}
