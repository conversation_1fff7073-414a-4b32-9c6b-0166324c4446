# 运行环境
- 操作系统：windows
- JAVA VERSION: 21.0.6
- JAVA_HOME: E:\Software\jdk-21.0.6_windows-x64_bin\jdk-21.0.6
- 后端java服务在idea中打开并运行调试

# 项目结构规则

1. nse-base：基础模块，包含通用工具类、常量定义和依赖管理 
   1. nse-common：通用工具类，包含各种工具类和常量定义
   2. nse-dependency：依赖管理，包含统一的依赖版本管理
2. nse-cloud：云端系统，包含云服务相关功能
   1. nse-cloud-common：云服务通用组件，包含云服务相关通用组件
   2. nse-cloud-launcher：云服务启动器，包含云服务启动相关代码
   3. nse-cloud-repository：云服务数据访问层，包含云服务相关数据访问代码
   4. nse-cloud-sys：云服务系统管理，包含云服务相关系统管理代码
3. nse-mgr：管理端（老师、学生端）系统，包含核心业务管理功能
   1. nse-mgr-common：管理系统通用组件，包含管理系统相关通用组件
   2. nse-mgr-launcher：管理系统启动器，包含管理系统启动相关代码
   3. nse-mgr-repository：管理系统数据访问层，包含管理系统相关数据访问代码
   4. nse-mgr-sys：管理系统核心业务，包含管理系统相关核心业务代码
   5. nse-mgr-license：管理系统License管理，包含管理系统License相关代码
   6. nse-mgr-course：管理系统课程管理，包含管理系统课程相关代码


# 开发准则

1.  所有计划的任务、文档、思考过程、记忆存储、日志、输出，都保存到augment文件夹下合适的位置
2. 所有生成的非代码文件，文件名按以`年月日时分_`开头，方便我后续查找；
3. 为每一次任务创建独立的文件夹，存储相关信息；
4. 每次开始涉及代码相关的任务时，务必先调用**Augment Context Engine**获取现有实现。
5. 禁止在nse-base、nse-cloud、nse-mgr三个项目工程代码文件中产生跟正式代码不相关的文件；
6. 禁止pgsl、redis、docker等服务，如有需要，务必需求用户确认；
7. 每一个任务都非常重要，需要非常高级的模型和深入的思考，必须选择最优模型，`Claude Sonnet 4`>`GPT-4.1`>`Gemini 2.5 Pro`，禁止使用其他模型来完成任务，因为它们的能力无法匹配要执行的任务。
8. 开发遵循《阿里规约》，并且遵循Java开发规范
9. Spring Boot版本和JDK版本严格按照当前项目依赖版本进行，不要调整版本号
10. 代码中出现的TODO注释，必须在注释中说明，不能简单的写TODO
11. 代码中出现的FIXME注释，必须在注释中说明，不能简单的写FIXME
12. ORM框架使用MyBatis-Plus，不要使用其他ORM框架，数据库查询使用Wrappers.lambda方式，禁止new QueryWrapper的写法，
13. SQL禁止使用XML方式编写，如果一定需要XML编写，一定要询问后生成
14. 代码中出现的异常，必须在catch块中进行处理，不能简单的写try-catch
15. 代码中出现的日志，必须使用SLF4J日志框架，不要使用System.out.println
16. 禁止使用swagger相关能力
17. 所有的json使用hutool 6.0.0版本的json序列化
---
