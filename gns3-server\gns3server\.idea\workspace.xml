<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a1ed12e9-a169-49b0-a28b-5a9dd4ef30b2" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GitLabMergeRequestFiltersHistory"><![CDATA[{
  "lastFilter": {
    "state": "OPENED",
    "assignee": {
      "type": "org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue",
      "username": "pengyaohuang",
      "fullname": "彭耀煌"
    }
  }
}]]></component>
  <component name="GitLabMergeRequestsSettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "first": "http://*************/nse/gns3-fork/gns3-server-2.2.54.git",
    "second": "9291e108-81b1-459b-b9c5-c9f0cc814dda"
  }
}]]></component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 8
}]]></component>
  <component name="ProjectId" id="32E1IpykZ9Cou5Aq3WJKXfkT9ke" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "feature/v1.0.0",
    "last_opened_file_path": "C:/__ruijie_work_space/nse/gns3-server/gns3server"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-9f8e2b94138c-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26094.141" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a1ed12e9-a169-49b0-a28b-5a9dd4ef30b2" name="Changes" comment="" />
      <created>1756970840601</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756970840601</updated>
    </task>
    <servers />
  </component>
</project>