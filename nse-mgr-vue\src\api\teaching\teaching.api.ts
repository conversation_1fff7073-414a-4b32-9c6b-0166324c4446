import request from '@/utils/request';
import {
  TeachingPageQuery,
  TeachingCourseForm,
  TeachingVO,
  PageResult,
  StudentPageQuery,
  StudentPageResult,
  StudentVO,
  CourseStartCheckResult
} from './types/teaching.types';

const COURSE_BASE_URL = '/api/course';

const TeachingAPI = {

  /**
   * 分页查询课程
   */
  getCoursePage(queryParams: TeachingPageQuery) {
    return request<any, PageResult<TeachingVO>>({
      url: `${COURSE_BASE_URL}/page`,
      method: 'get',
      params: queryParams
    });
  },

  /**
   * 创建课程
   */
  createCourse(data: TeachingCourseForm) {
    return request({
      url: COURSE_BASE_URL,
      method: 'post',
      data
    });
  },

  /**
   * 获取课程详情
   */
  getCourseDetail(id: string) {
    return request<any, CourseVO>({
      url: `${COURSE_BASE_URL}/${id}`,
      method: 'get'
    });
  },

  /**
   * 删除单个课程
   */
  deleteCourse(id: string) {
    return request({
      url: `${COURSE_BASE_URL}/${id}`,
      method: 'delete'
    });
  },

  /**
   * 批量删除课程
   */
  deleteCourseBatch(ids: string[]) {
    return request({
      url: `${COURSE_BASE_URL}/batch`,
      method: 'delete',
      data: ids
    });
  },

  /**
   * 分页查询课程学生
   */
  getCourseStudentPage(queryParams: StudentPageQuery) {
    return request<any, StudentPageResult<StudentVO>>({
      url: `${COURSE_BASE_URL}/student/page`,
      method: 'get',
      params: queryParams
    });
  },

  /**
   * 获取课程学生
   */
  getCourseStudent(courseId: string) {
    return request({
      url: `${COURSE_BASE_URL}/student/${courseId}`,
      method: 'get',
    });
  },

  /**
   * 新增课程学生
   */
  addCourseStudent(data: TeachingCourseForm) {
    return request({
      url: `${COURSE_BASE_URL}/student`,
      method: 'post',
      data
    });
  },

  /**
   * 删除学生
   */
  deleteCourseStudent(ids: string[]) {
    return request({
      url: `${COURSE_BASE_URL}/student`,
      method: 'delete',
      data: ids
    });
  },

  /**
   * 上课校验
   */
  startCourseCheck(id: String) {
    return request<any, CourseStartCheckResult>({
      url: `${COURSE_BASE_URL}/startCheck/${id}`,
      method: 'get'
    });
  },

  /**
   * 开始上课
   */
  startCourse(id: String) {
    return request({
      url: `${COURSE_BASE_URL}/start/${id}`,
      method: 'post'
    });
  },

  /**
   * 下课
   */
  endCourse(id: String) {
    return request({
      url: `${COURSE_BASE_URL}/end/${id}`,
      method: 'post'
    });
  },

  /**
   * 布置作业
   */
  assignHomework(courseId: String, repoId: string) {
    return request({
      url: `${COURSE_BASE_URL}/assignHomework`,
      method: 'post',
      data: { courseId, repoId }
    });
  },
}

export default TeachingAPI;
