#!/bin/bash

# PostgreSQL 17 一键卸载脚本
# 适用于 Ubuntu 24.04 离线安装的 PostgreSQL 17

set -e

# 脚本信息
SCRIPT_NAME="PostgreSQL 17 一键卸载脚本"
SCRIPT_VERSION="1.0.0"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

# 显示脚本信息
show_header() {
    echo -e "${BLUE}"
    echo "========================================"
    echo "  $SCRIPT_NAME"
    echo "  版本: $SCRIPT_VERSION"
    echo "  适用: Ubuntu 24.04"
    echo "========================================"
    echo -e "${NC}"
}

# 检查运行环境
check_environment() {
    log "🔍 检查运行环境..."
    
    # 检查是否为 root 或有 sudo 权限
    if [ "$EUID" -ne 0 ] && ! sudo -n true 2>/dev/null; then
        error "此脚本需要 root 权限或 sudo 权限"
        echo "请使用以下方式运行："
        echo "  sudo bash $0"
        exit 1
    fi
    
    # 检查操作系统
    if [ ! -f /etc/os-release ]; then
        error "无法确定操作系统版本"
        exit 1
    fi
    
    source /etc/os-release
    if [[ "$ID" != "ubuntu" ]] || [[ "$VERSION_ID" != "24.04" ]]; then
        warn "此脚本专为 Ubuntu 24.04 设计，当前系统: $PRETTY_NAME"
        read -p "是否继续？(y/N): " continue_anyway
        if [[ "$continue_anyway" != "y" && "$continue_anyway" != "Y" ]]; then
            log "用户取消操作"
            exit 0
        fi
    fi
    
    log "✅ 环境检查通过"
}

# 检查 PostgreSQL 安装状态
check_postgresql_status() {
    log "🔍 检查 PostgreSQL 17 安装状态..."
    
    local pg_installed=false
    local pg_running=false
    local pg_data_exists=false
    
    # 检查包是否安装
    if dpkg-query -W -f='${Status}' "postgresql-17" 2>/dev/null | grep -q "install ok installed"; then
        pg_installed=true
        log "📦 发现 PostgreSQL 17 软件包"
    fi
    
    # 检查服务是否运行
    if systemctl is-active --quiet "postgresql@17-main" 2>/dev/null; then
        pg_running=true
        log "🔄 PostgreSQL 17 服务正在运行"
    fi
    
    # 检查数据目录是否存在
    if [ -d "/var/lib/postgresql/17" ]; then
        pg_data_exists=true
        log "🗂️  发现 PostgreSQL 17 数据目录"
    fi
    
    # 检查用户是否存在
    if id "postgres" &>/dev/null; then
        log "👤 发现 postgres 用户"
    fi
    
    if [ "$pg_installed" = false ] && [ "$pg_running" = false ] && [ "$pg_data_exists" = false ]; then
        log "✅ 未发现 PostgreSQL 17 安装"
        echo ""
        echo "系统中似乎没有安装 PostgreSQL 17，无需卸载。"
        exit 0
    fi
    
    log "✅ PostgreSQL 17 状态检查完成"
}

# 显示卸载警告和确认
show_warning_and_confirm() {
    echo ""
    echo -e "${RED}⚠️  重要警告 ⚠️${NC}"
    echo -e "${RED}===========================================${NC}"
    echo "此操作将完全删除 PostgreSQL 17 及其所有数据！"
    echo ""
    echo "将要删除的内容包括："
    echo "  ✗ 所有数据库和数据"
    echo "  ✗ 配置文件和日志"
    echo "  ✗ postgres 用户和组"
    echo "  ✗ 相关软件包"
    echo "  ✗ 系统服务"
    echo ""
    echo -e "${YELLOW}此操作不可逆！请确保已备份重要数据！${NC}"
    echo ""
    
    # 第一次确认
    read -p "确定要继续卸载 PostgreSQL 17 吗？(输入 'yes' 确认): " first_confirm
    if [ "$first_confirm" != "yes" ]; then
        log "用户取消操作"
        exit 0
    fi
    
    # 第二次确认
    echo ""
    echo -e "${RED}最后确认：${NC}"
    read -p "请再次确认，输入 'DELETE ALL' 继续: " final_confirm
    if [ "$final_confirm" != "DELETE ALL" ]; then
        log "用户取消操作"
        exit 0
    fi
    
    log "✅ 用户确认卸载操作"
}

# 执行卸载
execute_uninstall() {
    log "🚀 开始执行 PostgreSQL 17 卸载..."
    
    local uninstall_script="$SCRIPT_DIR/scripts/unist/unist_postgresql.sh"
    
    # 检查卸载脚本是否存在
    if [ ! -f "$uninstall_script" ]; then
        error "未找到卸载脚本: $uninstall_script"
        exit 1
    fi
    
    # 确保脚本有执行权限
    chmod +x "$uninstall_script"
    
    # 执行卸载脚本（跳过交互确认）
    log "📜 执行详细卸载脚本..."
    
    # 设置环境变量跳过确认
    export SKIP_CONFIRMATION=true
    
    if bash "$uninstall_script"; then
        log "✅ PostgreSQL 17 卸载完成"
        return 0
    else
        error "PostgreSQL 17 卸载失败"
        return 1
    fi
}

# 显示卸载结果
show_result() {
    local exit_code=$1
    
    echo ""
    echo "========================================"
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}🎉 PostgreSQL 17 卸载成功！${NC}"
        echo ""
        echo "已完成以下操作："
        echo "  ✅ 停止并禁用所有 PostgreSQL 服务"
        echo "  ✅ 卸载所有相关软件包"
        echo "  ✅ 删除 postgres 用户和组"
        echo "  ✅ 清理所有数据和配置文件"
        echo "  ✅ 清理系统配置"
        echo ""
        echo "系统已恢复到安装前状态。"
    else
        echo -e "${RED}❌ PostgreSQL 17 卸载失败${NC}"
        echo ""
        echo "请检查日志文件了解详细信息："
        echo "  $SCRIPT_DIR/logs/nse-deploy.log"
        echo ""
        echo "您可以尝试："
        echo "  1. 重新运行此脚本"
        echo "  2. 手动执行卸载脚本"
        echo "  3. 联系系统管理员"
    fi
    echo "========================================"
}

# 主函数
main() {
    show_header
    check_environment
    check_postgresql_status
    show_warning_and_confirm
    
    if execute_uninstall; then
        show_result 0
        exit 0
    else
        show_result 1
        exit 1
    fi
}

# 执行主函数
main "$@"
