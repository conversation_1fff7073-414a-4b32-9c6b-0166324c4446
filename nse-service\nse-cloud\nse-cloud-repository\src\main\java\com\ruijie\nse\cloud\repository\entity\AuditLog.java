package com.ruijie.nse.cloud.repository.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 审计日志表
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("sys_audit_log")
public class AuditLog extends BaseEntity {

    /**
     * 请求体
     */
    private String requestBody;

    /**
     * 响应体
     */
    private String responseBody;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 耗时（毫秒）
     */
    private Integer elapsedMilliSeconds;

    /**
     * IP地址
     */
    private String ip;

    /**
     * URL
     */
    private String url;

    /**
     * MIME类型
     */
    private Integer mime;

    /**
     * 请求头
     */
    private String requestHeaders;

    /**
     * 审计标题
     */
    private String auditTitle;

    /**
     * 模块
     */
    private String module;


    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    protected String createdBy;

    /**
     * 创建日期
     */
    @TableField(fill = FieldFill.INSERT)
    protected Date createdDate;
}
