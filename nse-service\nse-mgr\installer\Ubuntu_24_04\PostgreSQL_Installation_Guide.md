# PostgreSQL 17 离线安装与卸载指南

## 概述

本指南提供了在 Ubuntu 24.04 系统上离线安装和卸载 PostgreSQL 17 的完整解决方案。

## 目录结构

```
nse-mgr/installer/Ubuntu_24_04/
├── scripts/
│   ├── 00__config.sh                    # 全局配置文件
│   ├── 12__postgresql_deb_pkg.sh        # deb包安装脚本
│   ├── 13__postgresql_installer.sh      # 主安装脚本（修复版）
│   └── unist/
│       ├── unist_postgresql.sh          # 详细卸载脚本
│       └── README.md                    # 卸载说明
├── softs/
│   └── postgresql17-offline-complete-pkgs/  # 离线deb包目录
├── uninstall_postgresql.sh             # 一键卸载脚本
├── test_postgresql_scripts.sh          # 脚本测试工具
└── PostgreSQL_Installation_Guide.md    # 本文档
```

## 功能特性

### 安装功能
- ✅ 完全离线安装 PostgreSQL 17
- ✅ 自动依赖解析和安装
- ✅ 数据库初始化和配置
- ✅ 用户和数据库创建
- ✅ 远程访问配置
- ✅ 服务自启动配置
- ✅ 安装状态检查和跳过重复安装

### 卸载功能
- ✅ 完全卸载所有相关包
- ✅ 清理数据目录和配置文件
- ✅ 删除用户和组
- ✅ 停止和禁用服务
- ✅ 清理系统配置
- ✅ 安全确认机制
- ✅ 卸载结果验证

## 使用方法

### 1. 安装前准备

#### 1.1 检查系统要求
- Ubuntu 24.04 LTS
- 至少 2GB 可用磁盘空间
- root 或 sudo 权限

#### 1.2 验证离线包
```bash
# 检查离线包目录
ls -la softs/postgresql17-offline-complete-pkgs/

# 应该包含约100+个deb包文件
```

#### 1.3 测试脚本（可选）
```bash
# 运行脚本测试工具
bash test_postgresql_scripts.sh
```

### 2. 安装 PostgreSQL 17

#### 2.1 一键安装（推荐）
```bash
# 进入安装目录
cd /path/to/nse-mgr/installer/Ubuntu_24_04

# 执行安装脚本
sudo bash scripts/13__postgresql_installer.sh
```

#### 2.2 分步安装
```bash
# 步骤1：安装deb包
sudo bash scripts/12__postgresql_deb_pkg.sh

# 步骤2：配置数据库
sudo bash scripts/13__postgresql_installer.sh
```

#### 2.3 安装过程说明
1. **包安装阶段**：
   - 检查已安装的包，跳过重复安装
   - 按依赖顺序安装deb包
   - 自动修复依赖问题

2. **服务配置阶段**：
   - 启动PostgreSQL服务
   - 初始化数据目录（如需要）
   - 创建数据库用户和数据库
   - 配置远程访问权限

3. **验证阶段**：
   - 检查服务状态
   - 验证数据库连接
   - 保存安装状态

### 3. 卸载 PostgreSQL 17

#### 3.1 一键卸载（推荐）
```bash
# 使用友好的卸载脚本
sudo bash uninstall_postgresql.sh
```

#### 3.2 直接卸载
```bash
# 直接使用详细卸载脚本
sudo bash scripts/unist/unist_postgresql.sh
```

#### 3.3 卸载过程说明
1. **安全确认**：
   - 显示将要删除的内容
   - 要求用户双重确认
   - 支持自动化模式

2. **服务停止**：
   - 停止所有PostgreSQL服务
   - 禁用服务自启动

3. **包卸载**：
   - 卸载所有相关软件包
   - 清除包配置文件

4. **数据清理**：
   - 删除数据目录
   - 删除配置文件
   - 删除日志文件

5. **用户清理**：
   - 删除postgres用户
   - 删除postgres组

6. **系统清理**：
   - 清理环境变量
   - 重载systemd配置
   - 清理包管理器缓存

7. **验证结果**：
   - 检查卸载完整性
   - 报告残留问题

## 配置说明

### 数据库配置
默认配置在 `scripts/00__config.sh` 中：

```bash
# 数据库配置
DEFAULT_DB_USER="sys_nse_rw"           # 数据库用户名
DEFAULT_DB_PASS="Ruijie@NSE20250902!!!" # 数据库密码
DB_NAME="nse"                          # 数据库名
SCHEMA_NAME="public"                   # 模式名
DB_PORT=5432                           # 端口号
```

### 网络配置
- 监听地址：`*`（所有接口）
- 端口：`5432`
- 允许远程连接：是
- 认证方式：MD5

## 故障排除

### 常见问题

#### 1. 安装失败
```bash
# 检查日志
tail -f logs/nse-deploy.log

# 检查包依赖
sudo apt --fix-broken install

# 重新运行安装
sudo bash scripts/13__postgresql_installer.sh
```

#### 2. 服务启动失败
```bash
# 检查服务状态
sudo systemctl status postgresql@17-main

# 检查日志
sudo journalctl -u postgresql@17-main -f

# 手动初始化数据目录
sudo -u postgres /usr/lib/postgresql/17/bin/initdb -D /var/lib/postgresql/17/main
```

#### 3. 连接失败
```bash
# 检查端口监听
sudo ss -tlnp | grep 5432

# 检查配置文件
sudo cat /var/lib/postgresql/17/main/postgresql.conf | grep listen_addresses
sudo cat /var/lib/postgresql/17/main/pg_hba.conf
```

#### 4. 卸载不完整
```bash
# 手动清理残留
sudo pkill -f postgres
sudo userdel -r postgres
sudo rm -rf /var/lib/postgresql
sudo rm -rf /etc/postgresql

# 重新运行卸载脚本
sudo bash uninstall_postgresql.sh
```

### 日志文件
- 安装日志：`logs/nse-deploy.log`
- PostgreSQL日志：`/var/log/postgresql/postgresql-17-main.log`
- 系统日志：`journalctl -u postgresql@17-main`

## 安全注意事项

1. **密码安全**：
   - 修改默认密码
   - 使用强密码策略
   - 定期更换密码

2. **网络安全**：
   - 限制远程访问IP
   - 使用防火墙规则
   - 启用SSL连接

3. **文件权限**：
   - 确保数据目录权限正确
   - 保护配置文件
   - 限制日志文件访问

4. **备份策略**：
   - 定期备份数据
   - 测试恢复流程
   - 异地存储备份

## 版本信息

- PostgreSQL版本：17.6
- 支持系统：Ubuntu 24.04 LTS
- 脚本版本：1.0.0
- 更新日期：2025-01-03

## 联系支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查日志文件
3. 运行测试脚本验证环境
4. 联系系统管理员
