package com.ruijie.nse.mgr.course.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.mgr.course.dto.input.CourseRepoInput;
import com.ruijie.nse.mgr.course.dto.output.CourseRepoOutput;
import com.ruijie.nse.mgr.course.service.CourseRepoService;
import com.ruijie.nse.mgr.repository.dto.input.CourseRepoQueryInput;
import com.ruijie.nse.mgr.repository.entity.CourseRepo;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.hutool.core.bean.BeanUtil;
import org.dromara.hutool.core.text.StrValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 课程库库控制器
 */
@RestController
@RequestMapping("/api/course-repo")
public class CourseRepoController {

    @Autowired
    private CourseRepoService courseRepoService;

    /**
     * 分页查询课程库
     * @param pageInput 分页参数
     * @param coursePkg 课程包名称
     * @param expName 实验名称
     * @param expType 实验类型
     * @return 分页结果
     */
    @GetMapping("/page")
    public R<PageOutput<CourseRepoOutput>> page(CourseRepoQueryInput queryInput) {
        return R.success(courseRepoService.findByPage(queryInput));
    }

    /**
     * 查询课程名称列表
     * @param coursePkg 分页参数
     * @return 课程名称列表
     */
    @GetMapping("/coursePkgList")
    public R<List<CourseRepoOutput>> findCoursePkgList(String coursePkg) {
        LambdaQueryWrapper<CourseRepo> wrapper = Wrappers.lambdaQuery(CourseRepo.class).like(StrValidator.isNotBlank(coursePkg), CourseRepo::getCoursePkg, coursePkg)
                .orderByAsc(CourseRepo::getCoursePkg);
        List<CourseRepo> courseRepoList = courseRepoService.list(wrapper);
        courseRepoList = courseRepoList.stream()
                .collect(Collectors.toMap(
                        repo -> repo.getCoursePkg() + "_" + repo.getExpType(),
                        Function.identity(),
                        (existing, replacement) -> existing
                ))
                .values()
                .stream()
                .toList();
        return R.success(BeanUtil.copyToList(courseRepoList, CourseRepoOutput.class));
    }

    /**
     * 查询课程实验列表
     * @param coursePkg
     * @param expType
     * @param expName
     * @return
     */
    @GetMapping("/courseExpList")
    public R<List<CourseRepoOutput>> courseExpList(@RequestParam String coursePkg,
                                                   @RequestParam String expType,
                                                   @RequestParam(required = false) String expName) {
        LambdaQueryWrapper<CourseRepo> wrapper = Wrappers.lambdaQuery(CourseRepo.class)
                .eq(CourseRepo::getCoursePkg, coursePkg)
                .eq(CourseRepo::getExpType, expType)
                .like(StrValidator.isNotBlank(expName), CourseRepo::getExpName, expName)
                .orderByAsc(CourseRepo::getExpName);
        List<CourseRepo> courseRepoList = courseRepoService.list(wrapper);
        return R.success(BeanUtil.copyToList(courseRepoList, CourseRepoOutput.class));
    }

    /**
     * 保存课程库
     * @param courseRepoInput 课程库信息
     * @return 操作结果
     */
    @PostMapping
    public R<CourseRepoOutput> save(CourseRepoInput courseRepoInput) {
        return R.success(courseRepoService.saveOrUpdate(courseRepoInput));
    }

    /**
     * 根据ID获取课程库详情
     * @param id 课程库ID
     * @return 课程库详情
     */
    @GetMapping("/{id}")
    public R<CourseRepoOutput> get(@PathVariable String id) {
        return R.success(courseRepoService.getById(id));
    }

    /**
     * 根据ID删除课程库
     * @param id 课程库ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public R<Void> delete(@PathVariable String id) {
        courseRepoService.deleteBatch(Collections.singletonList(id));
        return R.success();
    }

    /**
     * 批量删除课程库
     * @param ids 课程库ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    public R<Void> deleteBatch(@RequestBody List<String> ids) {
        courseRepoService.deleteBatch(ids);
        return R.success();
    }

    /**
     * 预览实验手册
     * @param filePath 实验手册文件路径
     * @return 文件流
     */
    @GetMapping("/preview")
    public void previewManual(String filePath, HttpServletResponse response) {
        courseRepoService.previewManual(filePath, response);
    }

}
