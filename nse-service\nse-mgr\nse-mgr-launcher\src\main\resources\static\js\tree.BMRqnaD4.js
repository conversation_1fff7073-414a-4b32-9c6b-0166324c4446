import{P as e,ba as t,ab as n,M as d,a7 as o,V as a,b6 as r,L as s,b7 as i,ac as l,ag as c,a2 as h,_ as u,d as p,b as f,A as g,l as y,a9 as k,y as N,r as v,a5 as C,a3 as x,ap as b,X as m,Z as E,g as K,f as D,C as w,m as S,e as A,h as L,w as B,D as T,n as O,j as $,k as _,Q as M,R as z,a_ as q,E as I,I as j,cO as F,B as P,o as R,by as H,u as U,K as W,F as Q,v as V,z as X,x as Y,c as Z,q as G}from"./index.Ckm1SagX.js";import{s as J}from"./token.DWNpOE8r.js";import{E as ee}from"./index.Cg5eTZHL.js";import{E as te}from"./checkbox.CyAsOZKA.js";import{E as ne}from"./index.CqmGTqol.js";import{f as de}from"./use-form-common-props.BSYTvb6G.js";const oe="$treeNodeId",ae=function(e,t){t&&!t[oe]&&Object.defineProperty(t,oe,{value:e.id,enumerable:!1,configurable:!1,writable:!1})},re=(e,t)=>null==t?void 0:t[e||oe],se=(e,t,n)=>{const d=e.value.currentNode;n();const o=e.value.currentNode;d!==o&&t("current-change",o?o.data:null,o)},ie=e=>{let t=!0,n=!0,d=!0;for(let o=0,a=e.length;o<a;o++){const a=e[o];(!0!==a.checked||a.indeterminate)&&(t=!1,a.disabled||(d=!1)),(!1!==a.checked||a.indeterminate)&&(n=!1)}return{all:t,none:n,allWithoutDisable:d,half:!t&&!n}},le=function(e){if(0===e.childNodes.length||e.loading)return;const{all:t,none:n,half:d}=ie(e.childNodes);t?(e.checked=!0,e.indeterminate=!1):d?(e.checked=!1,e.indeterminate=!0):n&&(e.checked=!1,e.indeterminate=!1);const o=e.parent;o&&0!==o.level&&(e.store.checkStrictly||le(o))},ce=function(e,t){const n=e.store.props,d=e.data||{},a=n[t];if(r(a))return a(d,e);if(s(a))return d[a];if(o(a)){const e=d[t];return o(e)?"":e}};let he=0;class ue{constructor(t){this.isLeafByUser=void 0,this.isLeaf=void 0,this.id=he++,this.text=null,this.checked=!1,this.indeterminate=!1,this.data=null,this.expanded=!1,this.parent=null,this.visible=!0,this.isCurrent=!1,this.canFocus=!1;for(const n in t)e(t,n)&&(this[n]=t[n]);this.level=0,this.loaded=!1,this.childNodes=[],this.loading=!1,this.parent&&(this.level=this.parent.level+1)}initialize(){var e;const o=this.store;if(!o)throw new Error("[Node]store is required!");o.registerNode(this);const a=o.props;if(a&&void 0!==a.isLeaf){const e=ce(this,"isLeaf");t(e)&&(this.isLeafByUser=e)}if(!0!==o.lazy&&this.data?(this.setData(this.data),o.defaultExpandAll&&(this.expanded=!0,this.canFocus=!0)):this.level>0&&o.lazy&&o.defaultExpandAll&&!this.isLeafByUser&&this.expand(),n(this.data)||ae(this,this.data),!this.data)return;const r=o.defaultExpandedKeys,s=o.key;s&&!d(this.key)&&r&&r.includes(this.key)&&this.expand(null,o.autoExpandParent),s&&void 0!==o.currentNodeKey&&this.key===o.currentNodeKey&&(o.currentNode=this,o.currentNode.isCurrent=!0),o.lazy&&o._initDefaultCheckedNode(this),this.updateLeafState(),1!==this.level&&!0!==(null==(e=this.parent)?void 0:e.expanded)||(this.canFocus=!0)}setData(e){let t;n(e)||ae(this,e),this.data=e,this.childNodes=[],t=0===this.level&&n(this.data)?this.data:ce(this,"children")||[];for(let n=0,d=t.length;n<d;n++)this.insertChild({data:t[n]})}get label(){return ce(this,"label")}get key(){const e=this.store.key;return this.data?this.data[e]:null}get disabled(){return ce(this,"disabled")}get nextSibling(){const e=this.parent;if(e){const t=e.childNodes.indexOf(this);if(t>-1)return e.childNodes[t+1]}return null}get previousSibling(){const e=this.parent;if(e){const t=e.childNodes.indexOf(this);if(t>-1)return t>0?e.childNodes[t-1]:null}return null}contains(e,t=!0){return(this.childNodes||[]).some(n=>n===e||t&&n.contains(e))}remove(){const e=this.parent;e&&e.removeChild(this)}insertChild(e,t,n){if(!e)throw new Error("InsertChild error: child is required.");if(!(e instanceof ue)){if(!n){const n=this.getChildren(!0);(null==n?void 0:n.includes(e.data))||(o(t)||t<0?null==n||n.push(e.data):null==n||n.splice(t,0,e.data))}Object.assign(e,{parent:this,store:this.store}),(e=a(new ue(e)))instanceof ue&&e.initialize()}e.level=this.level+1,o(t)||t<0?this.childNodes.push(e):this.childNodes.splice(t,0,e),this.updateLeafState()}insertBefore(e,t){let n;t&&(n=this.childNodes.indexOf(t)),this.insertChild(e,n)}insertAfter(e,t){let n;t&&(n=this.childNodes.indexOf(t),-1!==n&&(n+=1)),this.insertChild(e,n)}removeChild(e){const t=this.getChildren()||[],n=t.indexOf(e.data);n>-1&&t.splice(n,1);const d=this.childNodes.indexOf(e);d>-1&&(this.store&&this.store.deregisterNode(e),e.parent=null,this.childNodes.splice(d,1)),this.updateLeafState()}removeChildByData(e){let t=null;for(let n=0;n<this.childNodes.length;n++)if(this.childNodes[n].data===e){t=this.childNodes[n];break}t&&this.removeChild(t)}expand(e,t){const d=()=>{if(t){let e=this.parent;for(;e&&e.level>0;)e.expanded=!0,e=e.parent}this.expanded=!0,e&&e(),this.childNodes.forEach(e=>{e.canFocus=!0})};this.shouldLoadData()?this.loadData(e=>{n(e)&&(this.checked?this.setChecked(!0,!0):this.store.checkStrictly||le(this),d())}):d()}doCreateChildren(e,t={}){e.forEach(e=>{this.insertChild(Object.assign({data:e},t),void 0,!0)})}collapse(){this.expanded=!1,this.childNodes.forEach(e=>{e.canFocus=!1})}shouldLoadData(){return Boolean(!0===this.store.lazy&&this.store.load&&!this.loaded)}updateLeafState(){if(!0===this.store.lazy&&!0!==this.loaded&&void 0!==this.isLeafByUser)return void(this.isLeaf=this.isLeafByUser);const e=this.childNodes;!this.store.lazy||!0===this.store.lazy&&!0===this.loaded?this.isLeaf=!e||0===e.length:this.isLeaf=!1}setChecked(e,t,n,d){if(this.indeterminate="half"===e,this.checked=!0===e,this.store.checkStrictly)return;if(!this.shouldLoadData()||this.store.checkDescendants){const{all:n,allWithoutDisable:o}=ie(this.childNodes);this.isLeaf||n||!o||(this.checked=!1,e=!1);const a=()=>{if(t){const n=this.childNodes;for(let r=0,s=n.length;r<s;r++){const o=n[r];d=d||!1!==e;const a=o.disabled?o.checked:d;o.setChecked(a,t,!0,d)}const{half:o,all:a}=ie(n);a||(this.checked=a,this.indeterminate=o)}};if(this.shouldLoadData())return void this.loadData(()=>{a(),le(this)},{checked:!1!==e});a()}const o=this.parent;o&&0!==o.level&&(n||le(o))}getChildren(e=!1){if(0===this.level)return this.data;const t=this.data;if(!t)return null;const n=this.store.props;let d="children";return n&&(d=n.children||"children"),o(t[d])&&(t[d]=null),e&&!t[d]&&(t[d]=[]),t[d]}updateChildren(){const e=this.getChildren()||[],t=this.childNodes.map(e=>e.data),n={},d=[];e.forEach((e,o)=>{const a=e[oe];!!a&&t.findIndex(e=>(null==e?void 0:e[oe])===a)>=0?n[a]={index:o,data:e}:d.push({index:o,data:e})}),this.store.lazy||t.forEach(e=>{n[null==e?void 0:e[oe]]||this.removeChildByData(e)}),d.forEach(({index:e,data:t})=>{this.insertChild({data:t},e)}),this.updateLeafState()}loadData(e,t={}){if(!0!==this.store.lazy||!this.store.load||this.loaded||this.loading&&!Object.keys(t).length)e&&e.call(this);else{this.loading=!0;const n=n=>{this.childNodes=[],this.doCreateChildren(n,t),this.loaded=!0,this.loading=!1,this.updateLeafState(),e&&e.call(this,n)},d=()=>{this.loading=!1};this.store.load(this,n,d)}}eachNode(e){const t=[this];for(;t.length;){const n=t.shift();t.unshift(...n.childNodes),e(n)}}reInitChecked(){this.store.checkStrictly||le(this)}}class pe{constructor(t){this.lazy=!1,this.checkStrictly=!1,this.autoExpandParent=!1,this.defaultExpandAll=!1,this.checkDescendants=!1,this.currentNode=null,this.currentNodeKey=null;for(const n in t)e(t,n)&&(this[n]=t[n]);this.nodesMap={}}initialize(){if(this.root=new ue({data:this.data,store:this}),this.root.initialize(),this.lazy&&this.load){(0,this.load)(this.root,e=>{this.root.doCreateChildren(e),this._initDefaultCheckedNodes()},i)}else this._initDefaultCheckedNodes()}filter(e){const t=this.filterNodeMethod,n=this.lazy,d=async function(o){const a=o.root?o.root.childNodes:o.childNodes;for(const[n,r]of a.entries())r.visible=!!(null==t?void 0:t.call(r,e,r.data,r)),n%80==0&&n>0&&await h(),await d(r);if(!o.visible&&a.length){let e=!0;e=!a.some(e=>e.visible),o.root?o.root.visible=!1===e:o.visible=!1===e}e&&o.visible&&!o.isLeaf&&(n&&!o.loaded||o.expand())};d(this)}setData(e){e!==this.root.data?(this.nodesMap={},this.root.setData(e),this._initDefaultCheckedNodes(),this.setCurrentNodeKey(this.currentNodeKey)):this.root.updateChildren()}getNode(e){if(e instanceof ue)return e;const t=l(e)?re(this.key,e):e;return this.nodesMap[t]||null}insertBefore(e,t){var n;const d=this.getNode(t);null==(n=d.parent)||n.insertBefore({data:e},d)}insertAfter(e,t){var n;const d=this.getNode(t);null==(n=d.parent)||n.insertAfter({data:e},d)}remove(e){const t=this.getNode(e);t&&t.parent&&(t===this.currentNode&&(this.currentNode=null),t.parent.removeChild(t))}append(e,t){const n=c(t)?this.root:this.getNode(t);n&&n.insertChild({data:e})}_initDefaultCheckedNodes(){const e=this.defaultCheckedKeys||[],t=this.nodesMap;e.forEach(e=>{const n=t[e];n&&n.setChecked(!0,!this.checkStrictly)})}_initDefaultCheckedNode(e){const t=this.defaultCheckedKeys||[];!d(e.key)&&t.includes(e.key)&&e.setChecked(!0,!this.checkStrictly)}setDefaultCheckedKey(e){e!==this.defaultCheckedKeys&&(this.defaultCheckedKeys=e,this._initDefaultCheckedNodes())}registerNode(e){const t=this.key;if(e&&e.data)if(t){const t=e.key;d(t)||(this.nodesMap[t]=e)}else this.nodesMap[e.id]=e}deregisterNode(e){this.key&&e&&e.data&&(e.childNodes.forEach(e=>{this.deregisterNode(e)}),delete this.nodesMap[e.key])}getCheckedNodes(e=!1,t=!1){const n=[],d=function(o){(o.root?o.root.childNodes:o.childNodes).forEach(o=>{(o.checked||t&&o.indeterminate)&&(!e||e&&o.isLeaf)&&n.push(o.data),d(o)})};return d(this),n}getCheckedKeys(e=!1){return this.getCheckedNodes(e).map(e=>(e||{})[this.key])}getHalfCheckedNodes(){const e=[],t=function(n){(n.root?n.root.childNodes:n.childNodes).forEach(n=>{n.indeterminate&&e.push(n.data),t(n)})};return t(this),e}getHalfCheckedKeys(){return this.getHalfCheckedNodes().map(e=>(e||{})[this.key])}_getAllNodes(){const t=[],n=this.nodesMap;for(const d in n)e(n,d)&&t.push(n[d]);return t}updateChildren(e,t){const n=this.nodesMap[e];if(!n)return;const d=n.childNodes;for(let o=d.length-1;o>=0;o--){const e=d[o];this.remove(e.data)}for(let o=0,a=t.length;o<a;o++){const e=t[o];this.append(e,n.data)}}_setCheckedKeys(e,t=!1,n){const d=this._getAllNodes().sort((e,t)=>e.level-t.level),o=Object.create(null),a=Object.keys(n);d.forEach(e=>e.setChecked(!1,!1));const r=t=>{t.childNodes.forEach(t=>{var n;o[t.data[e]]=!0,(null==(n=t.childNodes)?void 0:n.length)&&r(t)})};for(let s=0,i=d.length;s<i;s++){const n=d[s],i=n.data[e].toString();if(a.includes(i)){if(n.childNodes.length&&r(n),n.isLeaf||this.checkStrictly)n.setChecked(!0,!1);else if(n.setChecked(!0,!0),t){n.setChecked(!1,!1);const e=function(t){t.childNodes.forEach(t=>{t.isLeaf||t.setChecked(!1,!1),e(t)})};e(n)}}else n.checked&&!o[i]&&n.setChecked(!1,!1)}}setCheckedNodes(e,t=!1){const n=this.key,d={};e.forEach(e=>{d[(e||{})[n]]=!0}),this._setCheckedKeys(n,t,d)}setCheckedKeys(e,t=!1){this.defaultCheckedKeys=e;const n=this.key,d={};e.forEach(e=>{d[e]=!0}),this._setCheckedKeys(n,t,d)}setDefaultExpandedKeys(e){e=e||[],this.defaultExpandedKeys=e,e.forEach(e=>{const t=this.getNode(e);t&&t.expand(null,this.autoExpandParent)})}setChecked(e,t,n){const d=this.getNode(e);d&&d.setChecked(!!t,n)}getCurrentNode(){return this.currentNode}setCurrentNode(e){const t=this.currentNode;t&&(t.isCurrent=!1),this.currentNode=e,this.currentNode.isCurrent=!0}setUserCurrentNode(e,t=!0){var n;const d=e[this.key],o=this.nodesMap[d];this.setCurrentNode(o),t&&this.currentNode&&this.currentNode.level>1&&(null==(n=this.currentNode.parent)||n.expand(null,!0))}setCurrentNodeKey(e,t=!0){var n;if(this.currentNodeKey=e,c(e))return this.currentNode&&(this.currentNode.isCurrent=!1),void(this.currentNode=null);const d=this.getNode(e);d&&(this.setCurrentNode(d),t&&this.currentNode&&this.currentNode.level>1&&(null==(n=this.currentNode.parent)||n.expand(null,!0)))}}const fe="RootTree",ge="NodeInstance",ye="TreeNodeMap";var ke=u(p({name:"ElTreeNodeContent",props:{node:{type:Object,required:!0},renderContent:Function},setup(e){const t=f("tree"),n=g(ge),d=g(fe);return()=>{const o=e.node,{data:a,store:r}=o;return e.renderContent?e.renderContent(k,{_self:n,node:o,data:a,store:r}):y(d.ctx.slots,"default",{node:o,data:a},()=>[k(ne,{tag:"span",truncated:!0,class:t.be("node","label")},()=>[o.label])])}}}),[["__file","tree-node-content.vue"]]);function Ne(e){const t=g(ye,null),n={treeNodeExpand:t=>{var n;e.node!==t&&(null==(n=e.node)||n.collapse())},children:[]};return t&&t.children.push(n),N(ye,n),{broadcastExpanded:t=>{if(e.accordion)for(const e of n.children)e.treeNodeExpand(t)}}}const ve=Symbol("dragEvents");const Ce=p({name:"ElTreeNode",components:{ElCollapseTransition:ee,ElCheckbox:te,NodeContent:ke,ElIcon:I,Loading:q},props:{node:{type:ue,default:()=>({})},props:{type:Object,default:()=>({})},accordion:Boolean,renderContent:Function,renderAfterExpand:Boolean,showCheckbox:Boolean},emits:["node-expand"],setup(e,t){const n=f("tree"),{broadcastExpanded:d}=Ne(e),o=g(fe),a=v(!1),i=v(!1),l=v(),c=v(),u=v(),p=g(ve),y=P();N(ge,y),e.node.expanded&&(a.value=!0,i.value=!0);const k=o.props.props.children||"children";j(()=>{var t;const n=null==(t=e.node.data)?void 0:t[k];return n&&[...n]},()=>{e.node.updateChildren()}),j(()=>e.node.indeterminate,t=>{x(e.node.checked,t)}),j(()=>e.node.checked,t=>{x(t,e.node.indeterminate)}),j(()=>e.node.childNodes.length,()=>e.node.reInitChecked()),j(()=>e.node.expanded,e=>{h(()=>a.value=e),e&&(i.value=!0)});const C=e=>re(o.props.nodeKey,e.data),x=(t,n)=>{l.value===t&&c.value===n||o.ctx.emit("check-change",e.node.data,t,n),l.value=t,c.value=n},b=()=>{e.node.isLeaf||(a.value?(o.ctx.emit("node-collapse",e.node.data,e.node,y),e.node.collapse()):e.node.expand(()=>{t.emit("node-expand",e.node.data,e.node,y)}))},m=t=>{e.node.setChecked(t,!(null==o?void 0:o.props.checkStrictly)),h(()=>{const t=o.store.value;o.ctx.emit("check",e.node.data,{checkedNodes:t.getCheckedNodes(),checkedKeys:t.getCheckedKeys(),halfCheckedNodes:t.getHalfCheckedNodes(),halfCheckedKeys:t.getHalfCheckedKeys()})})};return{ns:n,node$:u,tree:o,expanded:a,childNodeRendered:i,oldChecked:l,oldIndeterminate:c,getNodeKey:C,getNodeClass:t=>{const n=e.props.class;if(!n)return{};let d;if(r(n)){const{data:e}=t;d=n(e,t)}else d=n;return s(d)?{[d]:!0}:d},handleSelectChange:x,handleClick:t=>{se(o.store,o.ctx.emit,()=>{var t;if(null==(t=null==o?void 0:o.props)?void 0:t.nodeKey){const t=C(e.node);o.store.value.setCurrentNodeKey(t)}else o.store.value.setCurrentNode(e.node)}),o.currentNode.value=e.node,o.props.expandOnClickNode&&b(),(o.props.checkOnClickNode||e.node.isLeaf&&o.props.checkOnClickLeaf&&e.showCheckbox)&&!e.node.disabled&&m(!e.node.checked),o.ctx.emit("node-click",e.node.data,e.node,y,t)},handleContextMenu:t=>{var n;(null==(n=o.instance.vnode.props)?void 0:n.onNodeContextmenu)&&(t.stopPropagation(),t.preventDefault()),o.ctx.emit("node-contextmenu",t,e.node.data,e.node,y)},handleExpandIconClick:b,handleCheckChange:m,handleChildNodeExpand:(e,t,n)=>{d(t),o.ctx.emit("node-expand",e,t,n)},handleDragStart:t=>{o.props.draggable&&p.treeNodeDragStart({event:t,treeNode:e})},handleDragOver:t=>{t.preventDefault(),o.props.draggable&&p.treeNodeDragOver({event:t,treeNode:{$el:u.value,node:e.node}})},handleDrop:e=>{e.preventDefault()},handleDragEnd:e=>{o.props.draggable&&p.treeNodeDragEnd(e)},CaretRight:F}}});const xe=G(u(p({name:"ElTree",components:{ElTreeNode:u(Ce,[["render",function(e,t,n,d,o,a){const r=b("el-icon"),s=b("el-checkbox"),i=b("loading"),l=b("node-content"),c=b("el-tree-node"),h=b("el-collapse-transition");return m((D(),K("div",{ref:"node$",class:O([e.ns.b("node"),e.ns.is("expanded",e.expanded),e.ns.is("current",e.node.isCurrent),e.ns.is("hidden",!e.node.visible),e.ns.is("focusable",!e.node.disabled),e.ns.is("checked",!e.node.disabled&&e.node.checked),e.getNodeClass(e.node)]),role:"treeitem",tabindex:"-1","aria-expanded":e.expanded,"aria-disabled":e.node.disabled,"aria-checked":e.node.checked,draggable:e.tree.props.draggable,"data-key":e.getNodeKey(e.node),onClick:$(e.handleClick,["stop"]),onContextmenu:e.handleContextMenu,onDragstart:$(e.handleDragStart,["stop"]),onDragover:$(e.handleDragOver,["stop"]),onDragend:$(e.handleDragEnd,["stop"]),onDrop:$(e.handleDrop,["stop"])},[w("div",{class:O(e.ns.be("node","content")),style:_({paddingLeft:(e.node.level-1)*e.tree.props.indent+"px"})},[e.tree.props.icon||e.CaretRight?(D(),A(r,{key:0,class:O([e.ns.be("node","expand-icon"),e.ns.is("leaf",e.node.isLeaf),{expanded:!e.node.isLeaf&&e.expanded}]),onClick:$(e.handleExpandIconClick,["stop"])},{default:B(()=>[(D(),A(T(e.tree.props.icon||e.CaretRight)))]),_:1},8,["class","onClick"])):L("v-if",!0),e.showCheckbox?(D(),A(s,{key:1,"model-value":e.node.checked,indeterminate:e.node.indeterminate,disabled:!!e.node.disabled,onClick:$(()=>{},["stop"]),onChange:e.handleCheckChange},null,8,["model-value","indeterminate","disabled","onClick","onChange"])):L("v-if",!0),e.node.loading?(D(),A(r,{key:2,class:O([e.ns.be("node","loading-icon"),e.ns.is("loading")])},{default:B(()=>[S(i)]),_:1},8,["class"])):L("v-if",!0),S(l,{node:e.node,"render-content":e.renderContent},null,8,["node","render-content"])],6),S(h,null,{default:B(()=>[!e.renderAfterExpand||e.childNodeRendered?m((D(),K("div",{key:0,class:O(e.ns.be("node","children")),role:"group","aria-expanded":e.expanded,onClick:$(()=>{},["stop"])},[(D(!0),K(M,null,z(e.node.childNodes,t=>(D(),A(c,{key:e.getNodeKey(t),"render-content":e.renderContent,"render-after-expand":e.renderAfterExpand,"show-checkbox":e.showCheckbox,node:t,accordion:e.accordion,props:e.props,onNodeExpand:e.handleChildNodeExpand},null,8,["render-content","render-after-expand","show-checkbox","node","accordion","props","onNodeExpand"]))),128))],10,["aria-expanded","onClick"])),[[E,e.expanded]]):L("v-if",!0)]),_:1})],42,["aria-expanded","aria-disabled","aria-checked","draggable","data-key","onClick","onContextmenu","onDragstart","onDragover","onDragend","onDrop"])),[[E,e.node.visible]])}],["__file","tree-node.vue"]])},props:{data:{type:X(Array),default:()=>[]},emptyText:{type:String},renderAfterExpand:{type:Boolean,default:!0},nodeKey:String,checkStrictly:Boolean,defaultExpandAll:Boolean,expandOnClickNode:{type:Boolean,default:!0},checkOnClickNode:Boolean,checkOnClickLeaf:{type:Boolean,default:!0},checkDescendants:Boolean,autoExpandParent:{type:Boolean,default:!0},defaultCheckedKeys:Array,defaultExpandedKeys:Array,currentNodeKey:[String,Number],renderContent:{type:X(Function)},showCheckbox:Boolean,draggable:Boolean,allowDrag:{type:X(Function)},allowDrop:{type:X(Function)},props:{type:Object,default:()=>({children:"children",label:"label",disabled:"disabled"})},lazy:Boolean,highlightCurrent:Boolean,load:Function,filterNodeMethod:Function,accordion:Boolean,indent:{type:Number,default:18},icon:{type:V}},emits:["check-change","current-change","node-click","node-contextmenu","node-collapse","node-expand","check","node-drag-start","node-drag-end","node-drop","node-drag-leave","node-drag-enter","node-drag-over"],setup(e,t){const{t:n}=Y(),d=f("tree"),o=g(J,null),a=v(new pe({key:e.nodeKey,data:e.data,lazy:e.lazy,props:e.props,load:e.load,currentNodeKey:e.currentNodeKey,checkStrictly:e.checkStrictly,checkDescendants:e.checkDescendants,defaultCheckedKeys:e.defaultCheckedKeys,defaultExpandedKeys:e.defaultExpandedKeys,autoExpandParent:e.autoExpandParent,defaultExpandAll:e.defaultExpandAll,filterNodeMethod:e.filterNodeMethod}));a.value.initialize();const s=v(a.value.root),i=v(null),l=v(null),c=v(null),{broadcastExpanded:h}=Ne(e),{dragState:u}=function({props:e,ctx:t,el$:n,dropIndicator$:d,store:o}){const a=f("tree"),s=v({showDropIndicator:!1,draggingNode:null,dropNode:null,allowDrop:!0,dropType:null});return N(ve,{treeNodeDragStart:({event:n,treeNode:d})=>{if(n.dataTransfer){if(r(e.allowDrag)&&!e.allowDrag(d.node))return n.preventDefault(),!1;n.dataTransfer.effectAllowed="move";try{n.dataTransfer.setData("text/plain","")}catch(o){}s.value.draggingNode=d,t.emit("node-drag-start",d.node,n)}},treeNodeDragOver:({event:o,treeNode:i})=>{if(!o.dataTransfer)return;const l=i,c=s.value.dropNode;c&&c.node.id!==l.node.id&&C(c.$el,a.is("drop-inner"));const h=s.value.draggingNode;if(!h||!l)return;let u=!0,p=!0,f=!0,g=!0;r(e.allowDrop)&&(u=e.allowDrop(h.node,l.node,"prev"),g=p=e.allowDrop(h.node,l.node,"inner"),f=e.allowDrop(h.node,l.node,"next")),o.dataTransfer.dropEffect=p||u||f?"move":"none",(u||p||f)&&(null==c?void 0:c.node.id)!==l.node.id&&(c&&t.emit("node-drag-leave",h.node,c.node,o),t.emit("node-drag-enter",h.node,l.node,o)),s.value.dropNode=u||p||f?l:null,l.node.nextSibling===h.node&&(f=!1),l.node.previousSibling===h.node&&(u=!1),l.node.contains(h.node,!1)&&(p=!1),(h.node===l.node||h.node.contains(l.node))&&(u=!1,p=!1,f=!1);const y=l.$el,k=y.querySelector(`.${a.be("node","content")}`).getBoundingClientRect(),N=n.value.getBoundingClientRect();let v;const b=u?p?.25:f?.45:1:-1,m=f?p?.75:u?.55:0:1;let E=-9999;const K=o.clientY-k.top;v=K<k.height*b?"before":K>k.height*m?"after":p?"inner":"none";const D=y.querySelector(`.${a.be("node","expand-icon")}`).getBoundingClientRect(),w=d.value;"before"===v?E=D.top-N.top:"after"===v&&(E=D.bottom-N.top),w.style.top=`${E}px`,w.style.left=D.right-N.left+"px","inner"===v?x(y,a.is("drop-inner")):C(y,a.is("drop-inner")),s.value.showDropIndicator="before"===v||"after"===v,s.value.allowDrop=s.value.showDropIndicator||g,s.value.dropType=v,t.emit("node-drag-over",h.node,l.node,o)},treeNodeDragEnd:e=>{var n,d;const{draggingNode:r,dropType:i,dropNode:l}=s.value;if(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="move"),(null==r?void 0:r.node.data)&&l){const s={data:r.node.data};"none"!==i&&r.node.remove(),"before"===i?null==(n=l.node.parent)||n.insertBefore(s,l.node):"after"===i?null==(d=l.node.parent)||d.insertAfter(s,l.node):"inner"===i&&l.node.insertChild(s),"none"!==i&&(o.value.registerNode(s),o.value.key&&r.node.eachNode(e=>{var t;null==(t=o.value.nodesMap[e.data[o.value.key]])||t.setChecked(e.checked,!o.value.checkStrictly)})),C(l.$el,a.is("drop-inner")),t.emit("node-drag-end",r.node,l.node,i,e),"none"!==i&&t.emit("node-drop",r.node,l.node,i,e)}r&&!l&&t.emit("node-drag-end",r.node,null,i,e),s.value.showDropIndicator=!1,s.value.draggingNode=null,s.value.dropNode=null,s.value.allowDrop=!0}}),{dragState:s}}({props:e,ctx:t,el$:l,dropIndicator$:c,store:a});!function({el$:e},t){const n=f("tree");R(()=>{d()}),H(()=>{Array.from(e.value.querySelectorAll("input[type=checkbox]")).forEach(e=>{e.setAttribute("tabindex","-1")})}),U(e,"keydown",d=>{const o=d.target;if(!o.className.includes(n.b("node")))return;const a=d.code,r=Array.from(e.value.querySelectorAll(`.${n.is("focusable")}[role=treeitem]`)),s=r.indexOf(o);let i;if([W.up,W.down].includes(a)){if(d.preventDefault(),a===W.up){i=-1===s?0:0!==s?s-1:r.length-1;const e=i;for(;!t.value.getNode(r[i].dataset.key).canFocus;){if(i--,i===e){i=-1;break}i<0&&(i=r.length-1)}}else{i=-1===s?0:s<r.length-1?s+1:0;const e=i;for(;!t.value.getNode(r[i].dataset.key).canFocus;){if(i++,i===e){i=-1;break}i>=r.length&&(i=0)}}-1!==i&&r[i].focus()}[W.left,W.right].includes(a)&&(d.preventDefault(),o.click());const l=o.querySelector('[type="checkbox"]');[W.enter,W.numpadEnter,W.space].includes(a)&&l&&(d.preventDefault(),l.click())});const d=()=>{var t;if(!e.value)return;const d=Array.from(e.value.querySelectorAll(`.${n.is("focusable")}[role=treeitem]`));Array.from(e.value.querySelectorAll("input[type=checkbox]")).forEach(e=>{e.setAttribute("tabindex","-1")});const o=e.value.querySelectorAll(`.${n.is("checked")}[role=treeitem]`);o.length?o[0].setAttribute("tabindex","0"):null==(t=d[0])||t.setAttribute("tabindex","0")}}({el$:l},a);const p=Z(()=>{const{childNodes:e}=s.value,t=!!o&&0!==o.hasFilteredOptions;return(!e||0===e.length||e.every(({visible:e})=>!e))&&!t});j(()=>e.currentNodeKey,e=>{a.value.setCurrentNodeKey(null!=e?e:null)}),j(()=>e.defaultCheckedKeys,e=>{a.value.setDefaultCheckedKey(null!=e?e:[])}),j(()=>e.defaultExpandedKeys,e=>{a.value.setDefaultExpandedKeys(null!=e?e:[])}),j(()=>e.data,e=>{a.value.setData(e)},{deep:!0}),j(()=>e.checkStrictly,e=>{a.value.checkStrictly=e});const y=()=>{const e=a.value.getCurrentNode();return e?e.data:null};return N(fe,{ctx:t,props:e,store:a,root:s,currentNode:i,instance:P()}),N(de,void 0),{ns:d,store:a,root:s,currentNode:i,dragState:u,el$:l,dropIndicator$:c,isEmpty:p,filter:t=>{if(!e.filterNodeMethod)throw new Error("[Tree] filterNodeMethod is required when filter");a.value.filter(t)},getNodeKey:t=>re(e.nodeKey,t.data),getNodePath:t=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in getNodePath");const n=a.value.getNode(t);if(!n)return[];const d=[n.data];let o=n.parent;for(;o&&o!==s.value;)d.push(o.data),o=o.parent;return d.reverse()},getCheckedNodes:(e,t)=>a.value.getCheckedNodes(e,t),getCheckedKeys:e=>a.value.getCheckedKeys(e),getCurrentNode:y,getCurrentKey:()=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in getCurrentKey");const t=y();return t?t[e.nodeKey]:null},setCheckedNodes:(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedNodes");a.value.setCheckedNodes(t,n)},setCheckedKeys:(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedKeys");a.value.setCheckedKeys(t,n)},setChecked:(e,t,n)=>{a.value.setChecked(e,t,n)},getHalfCheckedNodes:()=>a.value.getHalfCheckedNodes(),getHalfCheckedKeys:()=>a.value.getHalfCheckedKeys(),setCurrentNode:(n,d=!0)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentNode");se(a,t.emit,()=>{h(n),a.value.setUserCurrentNode(n,d)})},setCurrentKey:(n,d=!0)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentKey");se(a,t.emit,()=>{h(),a.value.setCurrentNodeKey(null!=n?n:null,d)})},t:n,getNode:e=>a.value.getNode(e),remove:e=>{a.value.remove(e)},append:(e,t)=>{a.value.append(e,t)},insertBefore:(e,t)=>{a.value.insertBefore(e,t)},insertAfter:(e,t)=>{a.value.insertAfter(e,t)},handleNodeExpand:(e,n,d)=>{h(n),t.emit("node-expand",e,n,d)},updateKeyChildren:(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in updateKeyChild");a.value.updateChildren(t,n)}}}}),[["render",function(e,t,n,d,o,a){const r=b("el-tree-node");return D(),K("div",{ref:"el$",class:O([e.ns.b(),e.ns.is("dragging",!!e.dragState.draggingNode),e.ns.is("drop-not-allow",!e.dragState.allowDrop),e.ns.is("drop-inner","inner"===e.dragState.dropType),{[e.ns.m("highlight-current")]:e.highlightCurrent}]),role:"tree"},[(D(!0),K(M,null,z(e.root.childNodes,t=>(D(),A(r,{key:e.getNodeKey(t),node:t,props:e.props,accordion:e.accordion,"render-after-expand":e.renderAfterExpand,"show-checkbox":e.showCheckbox,"render-content":e.renderContent,onNodeExpand:e.handleNodeExpand},null,8,["node","props","accordion","render-after-expand","show-checkbox","render-content","onNodeExpand"]))),128)),e.isEmpty?(D(),K("div",{key:0,class:O(e.ns.e("empty-block"))},[y(e.$slots,"empty",{},()=>{var t;return[w("span",{class:O(e.ns.e("empty-text"))},Q(null!=(t=e.emptyText)?t:e.t("el.tree.emptyText")),3)]})],2)):L("v-if",!0),m(w("div",{ref:"dropIndicator$",class:O(e.ns.e("drop-indicator"))},null,2),[[E,e.dragState.showDropIndicator]])],2)}],["__file","tree.vue"]]));export{xe as E};
