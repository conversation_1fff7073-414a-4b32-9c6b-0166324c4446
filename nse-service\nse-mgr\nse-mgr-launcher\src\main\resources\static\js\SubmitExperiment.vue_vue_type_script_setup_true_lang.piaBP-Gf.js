import{d as e,ax as l,V as o,r as a,az as s,g as i,f as r,m as t,w as d,C as n,i as u,a1 as f,aP as m}from"./index.Ckm1SagX.js";import{E as p}from"./dialog.TtqHlFhB.js";import"./overlay.CXfNA60T.js";import{E as c,a as _}from"./form-item.CUMILu98.js";import{E as v}from"./upload.CnbKCtkP.js";import"./progress.ChnKapv7.js";/* empty css               */import{E as g}from"./index.CbYeWxT8.js";const j={slot:"footer",class:"dialog-footer"},x=e({__name:"SubmitExperiment",props:l(["id"],{modelValue:{type:Boolean,required:!0,default:!1},modelModifiers:{}}),emits:l(["confirm","cancel"],["update:modelValue"]),setup(e,{expose:l,emit:x}){const y=e,V=o({files:[]}),E=a(),b=o({files:[{required:!0,message:"请上传文件",trigger:"blur"}]}),k=s(e,"modelValue"),w=x;function C(e,l){if(l.length>1)return l.splice(-1,1),void m.warning("只允许上传一个文件！");if(!(e.size/1024/1024<10))return l.splice(-1,1),void m.warning("文件大小不能超过10M");V.files=l}function h(e,l){return!0}function z(e,l){V.files=l}const M=()=>{E.value.resetFields(),w("cancel")};return l({cancel:M}),(e,l)=>{const o=g,a=v,s=_,m=c,x=p;return r(),i("div",null,[t(x,{title:"提交实验作业",modelValue:k.value,"onUpdate:modelValue":l[2]||(l[2]=e=>k.value=e),width:"30%","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:M},{default:d(()=>[n("div",null,[t(m,{ref_key:"formRef",ref:E,model:u(V),rules:u(b)},{default:d(()=>[t(s,{prop:"files"},{default:d(()=>[t(a,{class:"documentation-upload","on-remove":z,"before-remove":h,"on-change":C,"file-list":u(V).files,"auto-upload":!1,accept:".pdf,.docx"},{tip:d(()=>l[4]||(l[4]=[n("div",{class:"el-upload__tip"},"请上传pdf、docx格式的实验结果，不超过10MB",-1)])),default:d(()=>[t(o,{size:"small",type:"primary"},{default:d(()=>l[3]||(l[3]=[f("点击上传")])),_:1,__:[3]})]),_:1},8,["file-list"])]),_:1})]),_:1},8,["model","rules"])]),n("div",null,[n("span",j,[t(o,{onClick:l[0]||(l[0]=e=>M())},{default:d(()=>l[5]||(l[5]=[f("取消")])),_:1,__:[5]}),t(o,{type:"primary",onClick:l[1]||(l[1]=e=>{E.value.validate(e=>{e&&w("confirm",y.id,V.files)})})},{default:d(()=>l[6]||(l[6]=[f("确定")])),_:1,__:[6]})])])]),_:1},8,["modelValue"])])}}});export{x as _};
