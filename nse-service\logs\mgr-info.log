15:38:36.630 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
15:38:36.723 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 55052 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
15:38:36.724 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
15:38:39.145 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
15:38:40.381 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
15:38:40.385 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
15:38:40.386 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
15:38:40.483 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
15:38:40.660 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
15:38:40.946 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
15:38:42.385 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
15:38:42.796 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3d9c8c3c
15:38:42.799 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
15:38:42.864 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
15:38:43.215 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.149s)
15:38:43.547 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
15:38:43.579 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
15:38:45.759 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
15:38:45.780 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
15:38:45.950 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
15:38:46.279 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
15:38:46.394 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
15:38:46.575 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
15:38:46.673 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2204769ms : this is harmless.
15:38:46.711 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
15:38:46.714 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCacheNoPersistence' created in EhcacheManager.
15:38:46.716 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
15:38:46.759 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
15:38:46.763 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCacheNoPersistence
15:38:46.763 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
15:38:46.764 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,72] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
15:38:48.299 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
15:38:48.341 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
15:38:48.829 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
15:38:49.454 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c65121
15:38:49.904 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
15:38:49.953 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 14.09 seconds (process running for 15.524)
15:38:49.957 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
15:38:49.957 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
15:38:49.957 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
15:38:49.958 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
15:38:49.958 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
15:38:49.958 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, jwtTokenCacheNoPersistence, serviceCache]
15:38:49.958 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
15:38:50.669 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
15:38:50.669 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
15:38:50.669 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
15:38:50.674 [main] INFO  c.r.n.m.c.u.NetworkConfigManager - [initializeNetworkConfig,44] - [System|系统] - 初始化网络配置...
15:38:50.677 [main] INFO  c.r.n.m.c.u.NetworkConfigManager - [initializeNetworkConfig,56] - [System|系统] - 网络配置初始化完成:
15:38:50.677 [main] INFO  c.r.n.m.c.u.NetworkConfigManager - [initializeNetworkConfig,57] - [System|系统] -   - 检测到的本机IP: *************
15:38:50.677 [main] INFO  c.r.n.m.c.u.NetworkConfigManager - [initializeNetworkConfig,58] - [System|系统] -   - 配置的服务器IP: *************
15:38:50.677 [main] INFO  c.r.n.m.c.u.NetworkConfigManager - [initializeNetworkConfig,59] - [System|系统] -   - 配置的域名: null
15:38:50.678 [main] INFO  c.r.n.m.c.u.NetworkConfigManager - [initializeNetworkConfig,60] - [System|系统] -   - 基础URL: http://*************:8080
15:38:50.679 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

15:45:31.453 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
15:45:31.563 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 46072 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
15:45:31.564 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
15:45:33.798 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
15:45:34.929 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
15:45:34.935 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
15:45:34.935 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
15:45:35.056 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
15:45:35.228 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
15:45:35.420 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
15:45:36.773 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
15:45:37.139 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2fca282c
15:45:37.142 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
15:45:37.200 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
15:45:37.532 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.135s)
15:45:37.845 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
15:45:37.871 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
15:45:39.234 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
15:45:39.256 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
15:45:39.423 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
15:45:39.536 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
15:45:39.557 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
15:45:39.718 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
15:45:39.769 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2204769ms : this is harmless.
15:45:39.796 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
15:45:39.799 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCacheNoPersistence' created in EhcacheManager.
15:45:39.802 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
15:45:39.848 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
15:45:39.851 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCacheNoPersistence
15:45:39.853 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
15:45:39.854 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,72] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
15:45:41.327 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
15:45:41.383 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
15:45:41.792 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
15:45:42.413 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c65121
15:45:42.834 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
15:45:42.869 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 12.042 seconds (process running for 13.134)
15:45:42.935 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
15:45:42.936 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
15:45:42.936 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
15:45:42.936 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
15:45:42.936 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
15:45:42.936 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, jwtTokenCacheNoPersistence, serviceCache]
15:45:42.936 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
15:45:43.463 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
15:45:43.463 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
15:45:43.463 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
15:45:43.470 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

