<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryForm" :inline="true" label-width="130px">
        <el-form-item label="实验名称" prop="expName">
          <el-input v-model="queryForm.expName" placeholder="请输入实验名称" clearable />
        </el-form-item>
        <el-form-item label="实验总人数" prop="totals">
          <el-input-number
            v-model="queryForm.totals[0]"
            placeholder="最小值"
            :controls="false"
            :precision="0"
            :min="0"
            :max="1000"
          ></el-input-number>
          <span class="line">&nbsp;-&nbsp;</span>
          <el-input-number
            v-model="queryForm.totals[1]"
            placeholder="最大值"
            :controls="false"
            :precision="0"
            :min="0"
            :max="1000"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="实验提交人数" prop="submits">
          <el-input-number
            v-model="queryForm.submits[0]"
            placeholder="最小值"
            :controls="false"
            :precision="0"
            :min="0"
            :max="1000"
          ></el-input-number>
          <span class="line">&nbsp;-&nbsp;</span>
          <el-input-number
            v-model="queryForm.submits[1]"
            placeholder="最大值"
            :controls="false"
            :precision="0"
            :min="0"
            :max="1000"
          ></el-input-number>
        </el-form-item>

        <template v-if="isExpand">
          <el-form-item label="提交实验人数占比" prop="percents">
            <el-input-number
              v-model="queryForm.percents[0]"
              placeholder="最小值"
              :controls="false"
              :precision="0"
              :min="0"
              :max="1000"
            ></el-input-number>
            <span class="line">&nbsp;-&nbsp;</span>
            <el-input-number
              v-model="queryForm.percents[1]"
              placeholder="最大值"
              :controls="false"
              :precision="0"
              :min="0"
              :max="1000"
            ></el-input-number>
          </el-form-item>
        </template>
      </el-form>
      <div class="search-buttons">
        <el-button type="primary" icon="search" @click="pageQuery()">查询</el-button>
        <el-button icon="refresh" @click="resetQuery()">重置</el-button>
        <el-link class="ml-3" type="primary" underline="never" @click="isExpand = !isExpand">
          {{ isExpand ? "收起" : "展开" }}
          <component :is="isExpand ? ArrowUp : ArrowDown" class="w-4 h-4 ml-2" />
        </el-link>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card shadow="never" class="data-table">
      <div class="data-table__tips">
        <span><b>课程：</b>{{ courseName }}</span>
        <span><b>任课老师：</b>{{ createdBy }}</span>
        <span><b>总实验个数：</b>{{ myTotal }}</span>
      </div>
      <div class="data-table__toolbar">
        <div class="data-table__toolbar--actions">
          <!-- v-hasPerm="['expData:assignHomework']" -->
          <el-button type="primary" icon="CirclePlus" @click="handleAssignHomework">布置实验作业</el-button>
          <!-- v-hasPerm="['expData:batchDelete']" -->
          <el-button
type="danger" plain icon="delete" 
              :disabled="lessonBatchNos.length === 0"
              @click="handleBatchDeleteExperimentData">批量删除</el-button>
        </div>
      </div>

      <el-table
ref="tableRef" v-loading="myLoading" :data="myPageData" 
          border stripe highlight-current-row
          class="data-table__content" 
          @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column type="index" label="序号" width="70" />
        <el-table-column label="实验名称" prop="expName" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <el-link type="primary" @click="viewExpDataDetails(row)">{{ row.expName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="实验总人数"
          prop="totals"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="实验提交人数"
          prop="submits"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="提交实验人数占比"
          prop="submitRatePercent"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ row.submitRatePercent }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="220">
          <template #default="scope">
            <!-- v-hasPerm="['expData:export']" -->
            <el-button type="primary" link size="small" :icon="MessageBox" @click="handleExportExcel(scope.row)">导出数据</el-button>
            <!-- v-hasPerm="['expData:delete']" -->
            <el-button type="danger" link size="small" :icon="Delete" @click="handleDeleteExperimentData(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

        <pagination
          v-if="myTotal > 0"
          v-model:total="myTotal"
          v-model:page="queryForm.pageNumber"
          v-model:limit="queryForm.pageSize"
          @pagination="fetchTableData"
        />
    </el-card>

    <!-- 布置作业对话框 -->
    <CoursePkgSelector
      v-model:visible="assignDialogVisible"
      title="布置实验作业"
      @confirm="handleAssignConfirm"
      @cancel="handleAssignCancel"
    />

  </div>
</template>

<script setup lang="ts">
import ExperimentDataAPI from "@/api/teaching/experiment-data.api";
import {
  ExperimentDataExport,
  ExperimentDataPageQuery,
  ExperimentDataVO,
} from "@/api/teaching/types/experiment-data.types";
import { ArrowUp, ArrowDown, MessageBox, Delete } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import Base64 from "@/utils/base64";
import CoursePkgSelector from "@/components/AssignHomeworkDialog/CoursePkgSelector.vue";
import TeachingAPI from "@/api/teaching/teaching.api";

const queryFormRef = ref();
const tableRef = ref();
const isExpand = ref(false);
const router = useRouter();
const queryForm = reactive<ExperimentDataPageQuery>({
  pageNumber: 1,
  pageSize: 10,
  courseId: "",
  expName: "",
  totals: [],
  submits: [],
  percents: [],
});

const route = useRoute()
const myLoading = ref(false);
const myPageData = ref<ExperimentDataVO[]>([]);
const myTotal = ref(0);
const queryString = computed(() => Base64.decode(route.query.info) || {});
const courseName = computed(() => queryString.value.courseName || '')
const createdBy = computed(() => queryString.value.createdBy || '')

// 设置courseId
queryForm.courseId = queryString.value.courseId || '';

// 拉取数据
const fetchTableData = async () => {
  myLoading.value = true;
  try {
    const data = await ExperimentDataAPI.listPageQuery(queryForm);
    myPageData.value = data.rows;
    myTotal.value = data.total;
  } finally {
    myLoading.value = false;
  }
}

// 查询
function pageQuery() {
  queryForm.pageNumber = 1;
  fetchTableData();
}

// 重置查询
function resetQuery() {
  queryFormRef.value?.resetFields();
  pageQuery();
}

const viewExpDataDetails = (row: ExperimentDataVO) => {
  router.push({
    path: "/teaching/experiment-data-detail",
    query: {
      info: Base64.encode(
        JSON.stringify({
          lessonBatchNo: row.lessonBatchNo,
          courseId: queryString.value.courseId,
          expName: row.expName,
          expTotalCount: row.totals,
          expSubmitCount: row.submits,
        })
      ),
    },
  });
};

// 导出Excel
const handleExportExcel = async (row: ExperimentDataVO) => {
    const dataForm : ExperimentDataExport = {
      lessonBatchNo: row.lessonBatchNo,
    }
    await ExperimentDataAPI.exportExcel(dataForm);
    ElMessage.success('导出成功');
};

const lessonBatchNos = ref<string[]>([])

// 删除课程实验
const handleDeleteExperimentData = async (row: ExperimentDataVO) => {
  
    await ElMessageBox.confirm("此操作将永久删除该实验数据, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
  const nos = [row.lessonBatchNo]
  await ExperimentDataAPI.deleteLessonsBatchNo(nos)
  ElMessage.success('删除成功');
  fetchTableData()
}

// 删除课程实验 批量
const handleBatchDeleteExperimentData = async () => {
  
    await ElMessageBox.confirm("此操作将永久删除该实验数据, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
  if (!lessonBatchNos.value) { 
    ElMessage.warning('请选择要删除的实验');
    return;
  }
  await ExperimentDataAPI.deleteLessonsBatchNo(lessonBatchNos.value)
  ElMessage.success('删除成功');
  fetchTableData()
}

const handleSelectionChange = (selection: ExperimentDataVO[]) => {
  lessonBatchNos.value = selection.map((item: ExperimentDataVO) => item.lessonBatchNo);
};

// 布置作业相关
const assignDialogVisible = ref(false);

// 处理布置作业
const handleAssignHomework = () => {
  assignDialogVisible.value = true;
};

// 处理布置作业确认
const handleAssignConfirm = async (repo: any) => {
  await TeachingAPI.assignHomework(queryString.value.courseId, repo.repoId);
  ElMessage.success('布置作业成功');
  fetchTableData();
  assignDialogVisible.value = false;
};

// 处理布置作业取消
const handleAssignCancel = () => {
  assignDialogVisible.value = false;
};

onMounted(() => { 
  fetchTableData();
})
</script>

<style scoped>
.search-container {
  display: flex;
  justify-content: space-between;
  padding: 18px 16px 0;
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;

  .search-buttons {
    min-width: 250px;
  }
  .line {
    color: #606266;
  }
  .el-input {
    width: 200px;
  }

  .el-input-number {
    width: 100px;
  }
}

.data-table__tips {
  margin-bottom: 10px;
}

.data-table__tips > span {
  margin-right: 20px;
}
</style>
