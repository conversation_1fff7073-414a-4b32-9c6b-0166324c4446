<template>
  <div class="app-container">
    <!-- 系统资源监控 -->
    <div class="resource-monitor mb-4">
      <el-card shadow="hover" class="resource-card">
        <div class="resource-header">
          <h3 class="resource-title">
            <el-icon><Monitor /></el-icon>
            个人资源概览
          </h3>
        </div>
        <div class="resource-content">
          <div class="resource-item">
            <div class="resource-label">CPU</div>
            <div class="resource-info">
              <div class="resource-text">
                <span>已用：{{ systemResources.cpu.available }}</span>
              </div>
              <el-progress
                :percentage="systemResources.cpu.usageRate"
                :color="getProgressColor(systemResources.cpu.usageRate)"
                :stroke-width="8"
              />
            </div>
          </div>

          <div class="resource-item">
            <div class="resource-label">内存</div>
            <div class="resource-info">
              <div class="resource-text">
                <span>可用：{{ systemResources.memory.available }}</span>
                <span>容量：{{ systemResources.memory.total }}</span>
              </div>
              <el-progress
                :percentage="systemResources.memory.usageRate"
                :color="getProgressColor(systemResources.memory.usageRate)"
                :stroke-width="8"
              />
            </div>
          </div>

          <div class="resource-item">
            <div class="resource-label">存储</div>
            <div class="resource-info">
              <div class="resource-text">
                <span>可用：{{ systemResources.storage.available }}</span>
                <span>容量：{{ systemResources.storage.total }}</span>
              </div>
              <el-progress
                :percentage="systemResources.storage.usageRate"
                :color="getProgressColor(systemResources.storage.usageRate)"
                :stroke-width="8"
              />
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 老师布置的实验 -->
    <div class="experiment-section mb-4">
      <el-card shadow="hover">
        <template #header>
          <div class="section-header">
            <h3 class="section-title">老师布置的实验</h3>
          </div>
        </template>

        <!-- 搜索区域 -->
        <div class="search-container mb-4">
          <el-form ref="assignedQueryFormRef" :model="assignedQueryParams" :inline="true">
            <el-form-item label="课程名称" prop="courseName">
              <el-input
                v-model="assignedQueryParams.courseName"
                placeholder="请输入课程名称"
                clearable
                @keyup.enter="handleAssignedQuery"
              />
            </el-form-item>
            <el-form-item label="实验名称" prop="experimentName">
              <el-input
                v-model="assignedQueryParams.experimentName"
                placeholder="请输入实验名称"
                clearable
                @keyup.enter="handleAssignedQuery"
              />
            </el-form-item>
            <el-form-item label="老师姓名" prop="teacherName">
              <el-input
                v-model="assignedQueryParams.teacherName"
                placeholder="请输入老师姓名"
                clearable
                @keyup.enter="handleAssignedQuery"
              />
            </el-form-item>
            <el-form-item label="提交状态" prop="submitStatus">
              <el-select
                v-model="assignedQueryParams.submitStatus"
                placeholder="请选择提交状态"
                clearable
                style="width: 200px"
              >
                <el-option label="已提交" value="已提交"></el-option>
                <el-option label="未提交" value="未提交"></el-option>
              </el-select>
            </el-form-item>
            <template v-if="assignedIsExpand">
              <el-form-item label="布置时间" prop="createdDate">
                <el-date-picker
                  v-model="assignedQueryParams.createdDate"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="提交时间" prop="submitDate">
                <el-date-picker
                  v-model="assignedQueryParams.submitDate"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD HH:mm:ss"
                ></el-date-picker>
              </el-form-item>

              <el-form-item label="分数" prop="score">
                <el-input-number
                  v-model="assignedQueryParams.score[0]"
                  placeholder="最小值"
                  :controls="false"
                  :precision="0"
                  :min="0"
                  :max="100"
                ></el-input-number>
                <span class="line">&nbsp;-&nbsp;</span>
                <el-input-number
                  v-model="assignedQueryParams.score[1]"
                  placeholder="最大值"
                  :controls="false"
                  :precision="0"
                  :min="0"
                  :max="100"
                ></el-input-number>
              </el-form-item>
            </template>
          </el-form>
          <div class="search-buttons">
            <el-button type="primary" icon="search" @click="handleAssignedQuery()">查询</el-button>
            <el-button icon="refresh" @click="handleAssignedResetQuery()">重置</el-button>
            <el-link
              class="ml-3"
              type="primary"
              underline="never"
              @click="assignedIsExpand = !assignedIsExpand"
            >
              {{ assignedIsExpand ? "收起" : "展开" }}
              <component :is="assignedIsExpand ? ArrowUp : ArrowDown" class="w-4 h-4 ml-2" />
            </el-link>
          </div>
        </div>

        <el-table
          v-loading="assignedLoading"
          :data="assignedPageData"
          border
          stripe
          highlight-current-row
          class="data-table__content"
        >
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column label="课程名称" prop="courseName" min-width="150" />
          <el-table-column label="实验名称" prop="experimentName" min-width="150" >
            <template #default="scope">
              <el-button 
                v-if="scope.row.experimentManualPath !== null && scope.row.experimentManualPath !== ''" 
                type="primary"
                link
                @click="handlePreviewManual(scope.row.experimentManualPath)"
                >
                {{ scope.row.experimentName || "-" }}
              </el-button>
              <span v-else>{{ scope.row.experimentName || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="老师姓名" prop="teacherName" width="150" align="center" />
          <el-table-column label="布置时间" prop="createdDate" width="220" align="center" />
          <el-table-column label="提交时间" prop="submitDate" width="220" align="center">
            <template #default="scope">
              {{ scope.row.submitDate || "-" }}
            </template>
          </el-table-column>
          <el-table-column label="提交状态" prop="submitStatus" width="100" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.submitStatus === '未提交'" type="info">未提交</el-tag>
              <el-tag v-else-if="scope.row.submitStatus === '已提交'" type="success">已提交</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="分数" prop="score" width="80" align="center">
            <template #default="scope">
              {{ scope.row.score || "待评分" }}
            </template>
          </el-table-column>
          <el-table-column label="实验报告" prop="expResultFilename" min-width="420" align="center">
            <template #default="{ row }">
              <el-link type="primary" @click="previewExpResult(row)">
                {{ row.expResultFilename }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="220" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                link :icon="Notebook"
                @click="handleAssignedExperiment(scope.row)"
              >
                开始实验
              </el-button>
              <el-button
type="primary" size="small" link
                :icon="Upload"
                @click="showSubmitDialog(scope.row.id)">
                提交实验作业
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-if="assignedTotal > 0"
          v-model:total="assignedTotal"
          v-model:page="assignedQueryParams.pageNumber"
          v-model:limit="assignedQueryParams.pageSize"
          @pagination="fetchAssignedData"
        />
      </el-card>
    </div>

    <!-- 我创建的实验 -->
    <div class="experiment-section">
      <el-card shadow="hover">
        <template #header>
          <div class="section-header">
            <h3 class="section-title">我创建的实验</h3>
          </div>
        </template>

        <!-- 搜索区域 -->
        <div class="search-container mb-4">
          <el-form ref="myQueryFormRef" :model="myQueryParams" :inline="true">
            <el-form-item label="实验名称" prop="experimentName">
              <el-input
                v-model="myQueryParams.experimentName"
                placeholder="请输入实验名称"
                clearable
                @keyup.enter="handleMyQuery"
              />
            </el-form-item>
            <el-form-item label="实验类型" prop="experimentType">
              <el-select
                v-model="myQueryParams.experimentType"
                placeholder="全部"
                clearable
                style="width: 220px"
              >
                <el-option label="自建" value="自建" />
                <el-option label="课程库实验" value="课程库实验" />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" prop="createdDate">
              <el-date-picker
                v-model="myQueryParams.createdDate"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <template v-if="myIsExpand">
              <el-form-item label="修改时间" prop="modifiedDate">
                <el-date-picker
                  v-model="myQueryParams.modifiedDate"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
            </template>
          </el-form>
          <div class="search-buttons">
            <el-button type="primary" icon="search" @click="handleMyQuery()">查询</el-button>
            <el-button icon="refresh" @click="handleMyResetQuery()">重置</el-button>
            <el-link
              class="ml-3"
              type="primary"
              underline="never"
              @click="myIsExpand = !myIsExpand"
            >
              {{ myIsExpand ? "收起" : "展开" }}
              <component :is="myIsExpand ? ArrowUp : ArrowDown" class="w-4 h-4 ml-2" />
            </el-link>
          </div>
        </div>
        <div>
          <el-button
            v-hasPerm="['course:experiment:create']"
            type="primary"
            icon="CirclePlus"
            @click="handleCreateExperiment"
          >
            新建实验
          </el-button>

          <el-tooltip
            content="删除后，将会一并删除实验数据，且无法恢复，请确认后操作！"
            placement="top"
          >
            <el-button
              v-hasPerm="['course:experiment:delete']"
              type="danger"
              plain
              :disabled="myIds.length === 0"
              icon="delete"
              @click="handleBatchDeleteExperiment"
            >
              批量删除实验
            </el-button>
          </el-tooltip>
        </div>
        <el-table
          v-loading="myLoading"
          :data="myPageData"
          border
          stripe
          highlight-current-row
          class="data-table__content"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column label="实验名称" prop="experimentName" min-width="100" />
          <el-table-column label="实验类型" prop="experimentType" width="120" />
          <el-table-column label="创建时间" prop="createdDate" width="220" align="center" />
          <el-table-column label="修改时间" prop="modifiedDate" width="220" align="center" />
          <el-table-column label="操作" fixed="right" width="360" align="center">
            <template #default="scope">
              <el-button  
                v-hasPerm="['course:experiment:open']"
                v-loading.fullscreen.lock="screenLoading"
                type="primary"
                size="small" 
                link :icon="Notebook"
                @click="handleOpenMyExperiment(scope.row.id)"
              >
                开始实验
              </el-button>
              <el-button
                v-hasPerm="['course:experiment:duplicate']"
                v-loading.fullscreen.lock="screenLoading"
                type="primary"
                size="small"
                link :icon="CopyDocument"
                @click="handleDuplicateExperiment(scope.row.id)"
              >
                复制
              </el-button>
              <el-button
                v-hasPerm="['course:experiment:export']"
                v-loading.fullscreen.lock="screenLoading"
                type="primary"
                size="small"
                link :icon="MessageBox"
                @click="handleExportExperiment(scope.row.id)"
              >
                导出
              </el-button>
              <el-button
                v-hasPerm="['course:experiment:rename']"
                type="primary"
                size="small"
                link :icon="EditPen"
                @click="handleRenameExperiment(scope.row.id, scope.row.experimentName)"
              >
                重命名
              </el-button>
              <el-button
                v-hasPerm="['course:experiment:delete']"
                v-loading.fullscreen.lock="screenLoading"
                type="danger"
                size="small"
                link :icon="Delete"
                @click="handleDeleteExperiment(scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-if="myTotal > 0"
          v-model:total="myTotal"
          v-model:page="myQueryParams.pageNumber"
          v-model:limit="myQueryParams.pageSize"
          @pagination="fetchMyData"
        />
      </el-card>
    </div>

    <!-- 实验表单弹窗 -->
    <ExperimentAdd
      v-model:visible="dialog.visible"
      :title="dialog.title"
      :initial-data="formData"
      @submit="handleSubmitForm"
      @close="handleCloseDialog"
    />

    <!-- 提交实验结果（作业）弹窗 -->
    <SubmitExperiment
      :id="submitDialog.id"
      ref="submitComponentRef"
      v-model="submitDialog.visible"
      @confirm="submitExpResult"
      @cancel="cancelSubmit"
    />

    <!-- 预览实验结果（作业）弹窗 -->
    <PreviewExpResult
      v-if="previewDialog.visible"
      v-model="previewDialog.visible"
      :exp-file="previewDialog.blob"
      @stop-preview="stopPreview"
    />

    <!-- 实验重命名 -->
    <el-dialog v-model="renameDialog.visible" :title="renameDialog.title" width="400px">
      <el-form
        ref="renameFormRef"
        :model="renameFormData"
        :rules="renameRules"
        label-width="100px"
        label-position="top"
      >
        <el-form-item label="新实验名称" prop="experimentName">
          <el-input v-model="renameFormData.experimentName" placeholder="请输入新实验名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="handleRenameSubmit">确定</el-button>
        <el-button @click="renameDialog.visible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { Monitor } from "@element-plus/icons-vue";
import { useRouter } from 'vue-router';
import ExperimentAPI from "@/api/experiment/experiment.api";
import type {
  AssignedExperimentQuery,
  MyExperimentQuery,
  AssignedExperimentVO,
  MyExperimentVO,
  ExperimentForm,
} from "@/api/experiment/types/experiment.types";
import ExperimentAdd from "./components/ExperimentAdd.vue";
import SubmitExperiment from "./components/SubmitExperiment.vue";
import PreviewExpResult from "./components/PreviewExpResult.vue";
import {
  getEnvironmentResourcesApi,
  formatCpuUsage,
  formatBytes,
} from "@/api/envirment/envirment.api";
import { type FormattedEnvironmentResourceVO } from "@/api/envirment/types/envirment.types";
import usePreview from "@/hooks/experiment/usePreview";
import useSubmit from "@/hooks/experiment/useSubmit";
import { ArrowUp, ArrowDown, Notebook, Upload, MessageBox, Delete, CopyDocument, EditPen } from "@element-plus/icons-vue";
import { useExperimentStore } from "@/store/modules/experiment.store";
import { useUserStore } from "@/store/modules/user.store";

defineOptions({
  name: "MyExperiment",
  inheritAttrs: false,
});

const assignedIsExpand = ref(false);
const myIsExpand = ref(false);

// 初始化路由和store
const screenLoading = ref(false);
const router = useRouter();
const userStore = useUserStore();

// 系统资源数据
const systemResources = ref<FormattedEnvironmentResourceVO>({
  cpu: { used: "0GHz", available: "0GHz", usageRate: 0 },
  memory: { used: "0GB", available: "0GB", total: "0GB", usageRate: 0 },
  storage: { used: "0GB", available: "0GB", total: "0GB", usageRate: 0 },
});

// 老师布置的实验相关
const assignedLoading = ref(false);
const assignedPageData = ref<AssignedExperimentVO[]>([]);
const assignedTotal = ref(0);
const assignedQueryFormRef = ref();
const assignedQueryParams = reactive<AssignedExperimentQuery>({
  pageNumber: 1,
  pageSize: 10,
  experimentName: "",
  courseName: "",
  teacherName: "",
  createdDate: [],
  submitDate: [],
  submitStatus: "",
  score: [],
});

// 我创建的实验相关
const myLoading = ref(false);
const myPageData = ref<MyExperimentVO[]>([]);
const myTotal = ref(0);
const myQueryFormRef = ref();
const myQueryParams = reactive<MyExperimentQuery>({
  pageNumber: 1,
  pageSize: 10,
  experimentName: "",
  experimentType: "",
  createdDate: [],
  modifiedDate: [],
});
// 选择我的实验id
const myIds = ref<string[]>([]);
// 行复选框选中
const handleSelectionChange = (selection: MyExperimentVO[]) => {
  myIds.value = selection.map((item: MyExperimentVO) => item.id);
};

// 弹窗相关
const dialog = reactive({
  visible: false,
  title: "",
});

const renameFormRef = ref();
const renameDialog = reactive({
  visible: false,
  title: "重命名",
});
const renameFormData = reactive<ExperimentForm>({
  id: "",
  experimentName: "",
  experimentType: "",
});

// 表单验证规则
const renameRules = {
  experimentName: [
    {
      required: true,
      message: "请输入实验名称",
      trigger: "blur",
      // 只有当实验类型为空白实验时才验证
      validator: (rule: any, value: string, callback: (error?: Error) => void) => {
        if (!value) {
          callback(new Error("请输入实验名称"));
        } else {
          callback();
        }
      },
    },
  ],
};

const formData = reactive<ExperimentForm>({
  id: "",
  experimentName: "",
  experimentType: "",
});

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 50) return "#67c23a";
  if (percentage < 80) return "#e6a23c";
  return "#f56c6c";
};

// 获取系统资源信息
const fetchSystemResources = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: "正在加载中......",
    background: "rgba(0, 0, 0, 0.7)",
  });
  try {
    const data = await getEnvironmentResourcesApi();
    // 计算内存和磁盘的可用量
    const memoryAvailable = data.memoryTotal - data.memoryUsage;
    const diskAvailable = data.diskTotal - data.diskUsage;

    // 计算使用率百分比
    const memoryUsageRate = Math.round((data.memoryUsage / data.memoryTotal) * 100);
    const diskUsageRate = Math.round((data.diskUsage / data.diskTotal) * 100);
    const cpuUsageRate = Math.round(data.cpuUsage * 100);

    systemResources.value = {
      cpu: {
        used: formatCpuUsage(data.cpuUsage),
        available: formatCpuUsage(data.cpuUsage),
        usageRate: cpuUsageRate,
      },
      memory: {
        used: formatBytes(data.memoryUsage),
        available: formatBytes(memoryAvailable),
        total: formatBytes(data.memoryTotal),
        usageRate: memoryUsageRate,
      },
      storage: {
        used: formatBytes(data.diskUsage),
        available: formatBytes(diskAvailable),
        total: formatBytes(data.diskTotal),
        usageRate: diskUsageRate,
      },
    };
  } catch (error) {
    console.error("获取系统资源信息失败:", error);
  } finally {
    loading.close();
  }
};

// 获取老师布置的实验列表
const fetchAssignedData = async () => {
  assignedLoading.value = true;
  try {
    const data = await ExperimentAPI.getAssignedExperimentsApi(assignedQueryParams);
    assignedPageData.value = data.rows;
    assignedTotal.value = data.total;
  } catch (error) {
    console.error("获取实验列表失败:", error);
  } finally {
    assignedLoading.value = false;
  }
};

// 获取我创建的实验列表
const fetchMyData = async () => {
  myLoading.value = true;
  try {
    const data = await ExperimentAPI.getMyExperimentsApi(myQueryParams);
    myPageData.value = data.rows;
    myTotal.value = data.total;
  } catch (error) {
    console.error("获取实验列表失败:", error);
  } finally {
    myLoading.value = false;
  }
};

// 老师布置的实验搜索
const handleAssignedQuery = () => {
  assignedQueryParams.pageNumber = 1;
  fetchAssignedData();
};

const handleAssignedResetQuery = () => {
  assignedQueryFormRef.value?.resetFields();
  assignedQueryParams.pageNumber = 1;
  fetchAssignedData();
};

// 我创建的实验搜索
const handleMyQuery = () => {
  myQueryParams.pageNumber = 1;
  fetchMyData();
};

const handleMyResetQuery = () => {
  myQueryFormRef.value?.resetFields();
  myQueryParams.pageNumber = 1;
  fetchMyData();
};

// 查看实验详情
const handleViewExperiment = (id: string) => {
  // 跳转到实验详情页面或打开详情弹窗
  console.log("查看实验详情:", id);
};

const handleAssignedExperiment = async (data: AssignedExperimentVO) => {
  try {
    screenLoading.value = true;

    const requestData: ExperimentForm = {
      courseRepoId: '',
      lessonId: data.id,
      experimentType: "布置作业",
      experimentName: data.experimentName
    }
    const expId = await ExperimentAPI.createExperimentApi(requestData);
    // 换取一个可以打开的实验地址url和主机信息
    const experimentUrlInfo = await ExperimentAPI.getProjectUrlApi(expId);

    // 创建实验会话并获取会话ID
    const experimentStore = useExperimentStore();
    const sessionId = experimentStore.createSession({
      signUrl: experimentUrlInfo.signUrl,
      title: "实验环境",
      hostId: experimentUrlInfo.hostId,
      serverIp: experimentUrlInfo.serverIp,
      serverPort: experimentUrlInfo.serverPort?.toString(),
      userId: experimentUrlInfo.userId,
    });
    
    // 使用会话ID导航到实验容器页面
    const routeData = router.resolve({
      path: '/expinfo',
      query: {
        sessionId,
      }
    });
    window.open(routeData.href, '_blank');
  } finally {
    screenLoading.value = false;
  }

}

/**
 * 开始实验
 * @param id 实验id
 */
const handleOpenMyExperiment = async (id: string) => {
  try {
    screenLoading.value = true;
    // 换取一个可以打开的实验地址url和主机信息
    const experimentUrlInfo = await ExperimentAPI.getProjectUrlApi(id);

    // 创建实验会话并获取会话ID
    const experimentStore = useExperimentStore();
    const sessionId = experimentStore.createSession({
      signUrl: experimentUrlInfo.signUrl,
      title: "实验环境",
      hostId: experimentUrlInfo.hostId,
      serverIp: experimentUrlInfo.serverIp,
      serverPort: experimentUrlInfo.serverPort?.toString(),
      userId: experimentUrlInfo.userId,
    });
    
    // 使用会话ID导航到实验容器页面
    const routeData = router.resolve({
      path: '/expinfo',
      query: {
        sessionId,
      }
    });
    window.open(routeData.href, '_blank');
  } finally {
    screenLoading.value = false;
  }
};

// 提交实验结果（作业）相关
const submitComponentRef = ref();
const { submitDialog, showSubmitDialog, submitExpResult, cancelSubmit } = useSubmit(
  submitComponentRef,
  fetchAssignedData
);

// 预览实验结果（作业）相关
const { previewDialog, previewExpResult, stopPreview } = usePreview();

// 新建实验
const handleCreateExperiment = () => {
  dialog.title = "新建实验";
  dialog.visible = true;
  resetForm();
};

// 复制实验
const handleDuplicateExperiment = async (id: string) => {
  try {
    await ElMessageBox.confirm("确定要复制这个实验吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    screenLoading.value = true;
    await ExperimentAPI.duplicateExperimentApi(id);
    ElMessage.success("复制实验成功");
    fetchMyData();
  } finally {
    screenLoading.value = false;
  }
};

// 重命名
const handleRenameExperiment = async (id: string, name: string) => {
  renameDialog.visible = true;
  renameFormData.id = id;
  renameFormData.experimentName = name;
};

/**
 * 重命名
 */
const handleRenameSubmit = async () => {
  try {
    screenLoading.value = true;
    await renameFormRef.value?.validate();
    await ExperimentAPI.renameExperimentApi(renameFormData);
    renameDialog.visible = false;
    ElMessage.success("重命名成功");
    fetchMyData();
  } finally {
    screenLoading.value = false;
  }
};

/**
 * 导出实验
 */
const handleExportExperiment = async (id: string) => {
  try {
    screenLoading.value = true;
    ExperimentAPI.exportExperimentApi(id).then((response: any) => {
      const fileData = response.data;
      const fileName = decodeURI(
        response.headers["content-disposition"].split(";")[1].split("=")[1]
      );
      const fileType = "application/octet-stream;charset=utf-8";

      const blob = new Blob([fileData], { type: fileType });
      const downloadUrl = window.URL.createObjectURL(blob);

      const downloadLink = document.createElement("a");
      downloadLink.href = downloadUrl;
      downloadLink.download = fileName;

      document.body.appendChild(downloadLink);
      downloadLink.click();

      document.body.removeChild(downloadLink);
      window.URL.revokeObjectURL(downloadUrl);
    });
  } finally {
    screenLoading.value = false;
  }
};

// 预览实验手册PDF
const handlePreviewManual = async (experimentManualPath: string) => {
  try {
    // 调用通用文件预览接口，传递type参数
    const response = await ExperimentAPI.previewFile(experimentManualPath, 'manual');
    const blob = new Blob([response.data], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    window.open(url, '_blank');
    // 清理URL对象
    setTimeout(() => {
      window.URL.revokeObjectURL(url);
    }, 1000);
  } catch (error) {
    console.error('预览实验手册失败:', error);
    ElMessage.error('预览实验手册失败');
  }
};

// 删除实验
const handleDeleteExperiment = async (id: string) => {
  try {
    await ElMessageBox.confirm("此操作将永久删除该实验数据, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    screenLoading.value = true;
    const ids = [id];
    await ExperimentAPI.deleteExperiment(ids);
    ElMessage.success("删除成功");
    fetchMyData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除实验失败:", error);
    }
  } finally {
    screenLoading.value = false;
  }
};

// 批量删除实验
const handleBatchDeleteExperiment = async () => {
  try {
    await ElMessageBox.confirm("此操作将永久删除该实验数据, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    if (!myIds.value) {
      ElMessage.error("请选择要删除的实验");
      return;
    }
    screenLoading.value = true;
    await ExperimentAPI.deleteExperiment(myIds.value);
    ElMessage.success("删除成功");
    fetchMyData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除实验失败:", error);
    }
  } finally {
    screenLoading.value = false;
  }
};

// 提交表单
const handleSubmitForm = async (data: ExperimentForm) => {
  try {
    // 更新本地表单数据
    Object.assign(formData, data);

    await ExperimentAPI.createExperimentApi(formData);
    ElMessage.success("创建成功");

    dialog.visible = false;
    fetchMyData();
  } catch (error) {
    console.error("提交失败:", error);
  }
};

// 关闭弹窗
const handleCloseDialog = () => {
  dialog.visible = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    experimentName: "",
    experimentType: "",
  });
};

// 初始化
onMounted(() => {
  fetchSystemResources();
  fetchAssignedData();
  fetchMyData();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.resource-monitor {
  .resource-card {
    .resource-header {
      margin-bottom: 16px;

      .resource-title {
        display: flex;
        gap: 8px;
        align-items: center;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .resource-content {
      display: flex;
      gap: 24px;

      .resource-item {
        flex: 1;

        .resource-label {
          margin-bottom: 8px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .resource-info {
          .resource-text {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 12px;
            color: var(--el-text-color-regular);
          }
        }
      }
    }
  }
}

.experiment-section {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.search-container {
  display: flex;
  justify-content: space-between;
  padding: 18px 16px 0;
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;

  .search-buttons {
    min-width: 250px;
  }
  .line {
    color: #606266;
  }
  .el-input {
    width: 200px;
  }

  .el-input-number {
    width: 100px;
  }
}

.data-table__content {
  margin-bottom: 16px;
}

.dialog-footer {
  text-align: right;
}
</style>
