import logging
import os
import threading
from functools import wraps
from typing import Optional, Dict, Any

import jwt
from aiohttp.web import json_response as aiohttp_json_response
from jwt.exceptions import InvalidTokenError, ImmatureSignatureError


# 配置管理
class JWTConfig:
    SECRET_KEY = os.getenv('JWT_SECRET_KEY', '7x9v2Kj5QpXWnYrLtSzM1uBcDfGhJkLmNoPqRsTuVwXy')
    ALGORITHMS = ['HS256']
    USER_ID_FIELD = 'user_id'  # JWT payload中用户ID的字段名
    USER_TYPE_FIELD = 'user_type'  # JWT payload中用户类型的字段名
    TOKEN_HEADER = 'X-Auth-Nse-Token'  # 请求头中的token字段
    TOKEN_PREFIX = 'Bearer'  # token前缀
    ADMIN_TYPES = {'admin', 'TEACHER', 'administrator', 'root'}  # 管理员用户类型
    LEEWAY_SECONDS = int(os.getenv('JWT_LEEWAY_SECONDS', '7200'))  # 允许的时钟偏差（秒），默认2小时


# 创建线程局部存储
_local = threading.local()
log = logging.getLogger()


def get_token_from_request(request):
    """从aiohttp请求中获取token"""
    if not request:
        return None
        
    # 从请求头获取token
    token = request.headers.get(JWTConfig.TOKEN_HEADER)

    # 如果上面方式获取不到，尝试其他格式
    if not token:
        # aiohttp通常会规范化header名称
        normalized_header = JWTConfig.TOKEN_HEADER.lower().replace('-', '_')
        token = request.headers.get(normalized_header)

    # 如果还获取不到，遍历查找
    if not token:
        target_header = JWTConfig.TOKEN_HEADER.lower()
        for key, value in request.headers.items():
            if key.lower() == target_header:
                token = value
                break
                
    # 移除Bearer前缀（如果存在）
    if token and token.startswith(JWTConfig.TOKEN_PREFIX + ' '):
        token = token[len(JWTConfig.TOKEN_PREFIX) + 1:]
        
    return token


def parse_token(token: str, secret_key: str = None, algorithms: list = None) -> Dict[str, Any]:
    """
    解析JWT token并返回payload

    Args:
        token: JWT token字符串
        secret_key: 用于验证token的密钥
        algorithms: 允许的加密算法列表

    Returns:
        JWT payload字典

    Raises:
        InvalidTokenError: 如果token无效或过期
    """
    secret_key = secret_key or JWTConfig.SECRET_KEY
    algorithms = algorithms or JWTConfig.ALGORITHMS

    # 检查token是否为空
    if not token:
        raise InvalidTokenError("Token is empty")
    
    # 检查token是否为有效的JWT格式（至少包含两个点）
    if token.count('.') < 2:
        raise InvalidTokenError("Invalid token format")

    try:
        # 添加options和leeway参数以处理时钟偏差
        payload = jwt.decode(token, secret_key, algorithms=algorithms, 
                           options={"verify_signature": True, "verify_exp": True, "verify_nbf": True, "verify_iat": True},
                           leeway=JWTConfig.LEEWAY_SECONDS)
        return payload
    except jwt.ExpiredSignatureError:
        raise InvalidTokenError("Token has expired")
    except ImmatureSignatureError:
        raise InvalidTokenError("Token is not yet valid (iat) - possible clock skew")
    except jwt.InvalidTokenError as e:
        raise InvalidTokenError(f"Invalid token: {str(e)}")


class UserContext:
    """用户信息 上下文管理器"""

    @staticmethod
    def set_current_user(user_id: str, user_type: str = None):
        """设置当前用户信息
        
        Args:
            user_id: 用户ID
            user_type: 用户类型，可选参数
        """
        _local.user_id = user_id
        _local.jwt_payload = getattr(_local, 'jwt_payload', {})
        _local.jwt_payload[JWTConfig.USER_ID_FIELD] = user_id
        
        if user_type is not None:
            _local.user_type = user_type
            _local.jwt_payload[JWTConfig.USER_TYPE_FIELD] = user_type
        else:
            # 清除之前的user_type值，确保向后兼容
            if hasattr(_local, 'user_type'):
                del _local.user_type
            if JWTConfig.USER_TYPE_FIELD in _local.jwt_payload:
                del _local.jwt_payload[JWTConfig.USER_TYPE_FIELD]

    @staticmethod
    def get_current_user() -> Optional[str]:
        return getattr(_local, 'user_id', None)

    @staticmethod
    def get_jwt_payload() -> Optional[Dict[str, Any]]:
        return getattr(_local, 'jwt_payload', None)

    @staticmethod
    def set_user_type(user_type: str):
        """设置当前用户类型"""
        _local.user_type = user_type
        _local.jwt_payload = getattr(_local, 'jwt_payload', {})
        _local.jwt_payload[JWTConfig.USER_TYPE_FIELD] = user_type

    @staticmethod
    def get_user_type() -> Optional[str]:
        """获取当前用户类型"""
        return getattr(_local, 'user_type', None)

    @staticmethod
    def is_admin() -> bool:
        """检查当前用户是否为管理员
        
        Returns:
            bool: 如果用户类型在ADMIN_TYPES中则返回True，否则返回False
        """
        user_type = UserContext.get_user_type()
        return user_type is not None and user_type in JWTConfig.ADMIN_TYPES

    @staticmethod
    def clear_context():
        if hasattr(_local, 'user_id'):
            del _local.user_id
        if hasattr(_local, 'user_type'):
            del _local.user_type
        if hasattr(_local, 'jwt_payload'):
            del _local.jwt_payload


def token_required(f):
    """
    JWT token验证装饰器
    """

    @wraps(f)
    def decorated(*args, **kwargs):
        try:
            # 在aiohttp中，request是作为参数传入的
            # 假设第一个参数是request对象
            request = args[0] if args else None
            if not request:
                return aiohttp_json_response({'error': 'No request object found'}, status=401)

            # aiohttp中获取header的方式
            log.info("---------------------request=%s", request)
            log.info("---------------------all headers: %s", dict(request.headers))

            # 从请求头获取token
            token = get_token_from_request(request)
            log.info("---------------------token=%s", token)

            if not token:
                return aiohttp_json_response({'error': 'Token is missing'}, status=401)

            # 解析token
            payload = parse_token(token)
            if not payload:
                return aiohttp_json_response({'error': 'Invalid token'}, status=401)

            # 设置用户上下文
            user_id = payload.get(JWTConfig.USER_ID_FIELD)
            user_type = payload.get(JWTConfig.USER_TYPE_FIELD)  # 可能为None（向后兼容）

            if not user_id:
                return aiohttp_json_response({'error': 'Invalid token payload'}, status=401)

            UserContext.set_current_user(user_id, user_type)

            # 调用原函数
            return f(*args, **kwargs)

        except Exception as e:
            log.error("Token validation failed: %s", str(e), exc_info=True)
            return aiohttp_json_response({'error': f'Token validation failed: {str(e)}'}, status=401)
        finally:
            # 清理用户上下文
            UserContext.clear_context()

    return decorated


def admin_required(f):
    """
    管理员权限验证装饰器
    
    先进行token验证，然后检查用户是否为管理员
    
    使用方法:
    @admin_required
    def admin_only_route():
        user_id = UserContext.get_user_id()
        user_type = UserContext.get_user_type()
        return f"Admin {user_id} with type {user_type}"
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        try:
            # 在aiohttp中，request是作为参数传入的
            # 假设第一个参数是request对象
            request = args[0] if args else None
            if not request:
                return aiohttp_json_response({'error': 'No request object found'}, status=401)

            # 从请求头获取token
            token = get_token_from_request(request)
            
            if not token:
                return aiohttp_json_response({'error': 'Token is missing'}, status=401)
            
            # 解析token
            payload = parse_token(token)
            if not payload:
                return aiohttp_json_response({'error': 'Invalid token'}, status=401)
            
            # 设置用户上下文
            user_id = payload.get(JWTConfig.USER_ID_FIELD)
            user_type = payload.get(JWTConfig.USER_TYPE_FIELD)  # 可能为None（向后兼容）
            
            if not user_id:
                return aiohttp_json_response({'error': 'Invalid token payload'}, status=401)
            
            UserContext.set_current_user(user_id, user_type)
            
            # 检查管理员权限
            if not UserContext.is_admin():
                return aiohttp_json_response({'error': 'Admin access required'}, status=403)
            
            # 调用原函数
            return f(*args, **kwargs)
            
        except Exception as e:
            log.error("Admin validation failed: %s", str(e), exc_info=True)
            return aiohttp_json_response({'error': f'Admin validation failed: {str(e)}'}, status=401)
        finally:
            # 清理用户上下文
            UserContext.clear_context()
    
    return decorated


def json_response(data, status=200):
    return aiohttp_json_response(data, status=status)

# 测试用例
if __name__ == '__main__':
    from datetime import datetime, timedelta
    
    # 创建测试token
    test_payload = {
        JWTConfig.USER_ID_FIELD: 'test_user_123',
        JWTConfig.USER_TYPE_FIELD: 'TEACHER',
        'exp': datetime.utcnow() + timedelta(hours=1)
    }
    
    test_token = jwt.encode(test_payload, JWTConfig.SECRET_KEY, algorithm='HS256')
    print(f"Test token: {test_token}")
    
    # 测试token解析
    parsed = parse_token(test_token)
    print(f"Parsed payload: {parsed}")
    
    # 测试用户上下文
    UserContext.set_current_user('test_user_123', 'TEACHER')
    print(f"Current user ID: {UserContext.get_current_user()}")
    print(f"Current user type: {UserContext.get_user_type()}")
    print(f"Is admin: {UserContext.is_admin()}")
    
    # 测试向后兼容性（没有user_type的token）
    legacy_payload = {
        JWTConfig.USER_ID_FIELD: 'legacy_user_456',
        'exp': datetime.utcnow() + timedelta(hours=1)
    }
    
    legacy_token = jwt.encode(legacy_payload, JWTConfig.SECRET_KEY, algorithm='HS256')
    print(f"\nLegacy token: {legacy_token}")
    
    parsed_legacy = parse_token(legacy_token)
    print(f"Parsed legacy payload: {parsed_legacy}")
    
    # 测试legacy token的用户上下文
    UserContext.set_current_user('legacy_user_456', None)
    print(f"Legacy user ID: {UserContext.get_current_user()}")
    print(f"Legacy user type: {UserContext.get_user_type()}")
    print(f"Legacy is admin: {UserContext.is_admin()}")
    
    # 测试无效token
    print("\nTesting invalid token:")
    try:
        invalid_result = parse_token('invalid_token')
        print(f"Invalid token result: {invalid_result}")
    except InvalidTokenError as e:
        print(f"Expected error: {e}")
    
    # 测试管理员权限验证
    print("\n=== 管理员权限测试 ===")
    
    # 测试不同类型的管理员
    admin_types = ['admin', 'TEACHER', 'administrator', 'root']
    for admin_type in admin_types:
        admin_payload = {
            JWTConfig.USER_ID_FIELD: f'admin_{admin_type}',
            JWTConfig.USER_TYPE_FIELD: admin_type,
            'exp': datetime.utcnow() + timedelta(hours=1)
        }
        
        admin_token = jwt.encode(admin_payload, JWTConfig.SECRET_KEY, algorithm='HS256')
        parsed_admin = parse_token(admin_token)
        
        UserContext.set_current_user(f'admin_{admin_type}', admin_type)
        print(f"User type '{admin_type}' - Is admin: {UserContext.is_admin()}")
        UserContext.clear_context()
    
    # 测试非管理员用户
    print("\n测试非管理员用户:")
    non_admin_types = ['student', 'user', 'guest', 'viewer']
    for user_type in non_admin_types:
        user_payload = {
            JWTConfig.USER_ID_FIELD: f'user_{user_type}',
            JWTConfig.USER_TYPE_FIELD: user_type,
            'exp': datetime.utcnow() + timedelta(hours=1)
        }
        
        user_token = jwt.encode(user_payload, JWTConfig.SECRET_KEY, algorithm='HS256')
        parsed_user = parse_token(user_token)
        
        UserContext.set_current_user(f'user_{user_type}', user_type)
        print(f"User type '{user_type}' - Is admin: {UserContext.is_admin()}")
        UserContext.clear_context()
    
    # 测试没有用户类型的情况
    print("\n测试没有用户类型的情况:")
    UserContext.set_current_user('no_type_user', None)
    print(f"No user type - Is admin: {UserContext.is_admin()}")
    UserContext.clear_context()
    
    print("\n=== 管理员权限测试完成 ===")