import{ap as e,g as t,f as r,m as s,w as a,a1 as i}from"./index.Ckm1SagX.js";import{E as o}from"./alert.DMAbw9T1.js";/* empty css             */import{_ as l}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{E as m}from"./index.CMOQuMWt.js";import"./index.Cn1QDWeG.js";import"./index.BRUQ9gWw.js";const p={style:{padding:"30px"}};const n=l({},[["render",function(l,n){const d=m,u=e("router-view"),f=o;return r(),t("div",p,[s(d,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/multi-level/level1.vue",type:"primary",target:"_blank",class:"mb-10"},{default:a(()=>n[0]||(n[0]=[i(" 示例源码 请点击>>>> ")])),_:1,__:[0]}),s(f,{closable:!1,title:"菜单一级"},{default:a(()=>[s(u)]),_:1})])}]]);export{n as default};
