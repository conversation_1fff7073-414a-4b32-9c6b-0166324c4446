10:19:06.442 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:19:06.520 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 20264 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:19:06.521 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:19:08.320 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:19:09.358 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:19:09.361 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:19:09.361 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:19:09.447 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:19:09.590 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:19:09.745 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:19:12.865 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:19:12.888 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:19:13.059 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:19:13.160 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:19:13.196 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:19:13.372 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:19:13.458 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:19:13.462 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:19:13.510 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:19:13.513 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:19:13.513 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:19:13.770 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:19:13.807 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:19:14.138 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:19:15.265 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@54336c81
10:19:15.607 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:19:15.642 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 9.775 seconds (process running for 10.884)
10:19:16.061 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:19:16.394 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@221961f2
10:19:16.396 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:19:16.513 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,56] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
10:19:17.336 [main] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_LIC_VERIFY_FAILED, evtDetails=FileNotFoundException: proc\uptime (系统找不到指定的路径。), remark=null, createdDate=2025-08-28T10:19:17.156080, modifiedDate=2025-08-28T10:19:17.156080, evtLevel=ERROR, evtMessage=License验证异常)
10:19:17.365 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,56] - [System|系统] - 事件消息发布成功: type=EVT_LIC_VERIFY_FAILED, message=License验证异常
10:19:17.365 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=false, status=500 INTERNAL_SERVER_ERROR, validationTime=Thu Aug 28 10:19:17 CST 2025, errorMessage=验证过程中发生异常: proc\uptime (系统找不到指定的路径。), details=[], extensions=null)
10:19:17.366 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:19:17.366 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:19:17.366 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:19:17.366 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:19:17.366 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:19:17.366 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:19:17.366 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:19:17.815 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |--- 当前python版本为: Python 3.11.2
10:20:00.004 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
10:20:00.099 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
10:25:00.315 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
10:25:00.363 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
10:30:00.469 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
10:30:00.528 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
10:31:05.987 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:31:06.061 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:31:06.081 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:31:06.133 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:31:06.277 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
10:31:06.278 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
10:31:06.344 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
10:31:06.345 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
10:31:06.347 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
10:31:06.350 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
10:31:10.805 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:31:10.875 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 46640 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:31:10.876 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:31:12.634 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:31:13.618 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:31:13.621 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:31:13.622 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:31:13.719 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:31:13.853 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:31:14.016 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:31:15.880 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:31:15.899 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:31:16.050 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:31:16.140 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:31:16.160 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:31:16.334 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:31:16.385 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 712816ms : this is harmless.
10:31:16.409 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:31:16.411 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:31:16.449 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:31:16.452 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:31:16.452 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:31:16.688 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:31:16.731 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:31:17.078 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:31:18.214 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b5f8707
10:31:18.568 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:31:18.604 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 8.328 seconds (process running for 9.271)
10:31:18.608 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:31:18.608 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:31:18.609 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:31:18.609 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:31:18.609 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:31:18.609 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:31:18.609 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:31:18.926 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:31:19.274 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5d7f4cbb
10:31:19.276 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:31:19.824 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] -     |===========================================
    |--- 当前python版本为: Python 3.11.2
    |===========================================

10:31:19.833 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,48] - [System|系统] - 

----------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
----------------------------------------------------------
| 并发数（管理员） | 并发数（普通用户） | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 0.5      | 2      | 10.0     |

10:32:06.560 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:32:06.573 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:32:06.663 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:32:06.664 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:32:06.690 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
10:32:06.690 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
10:32:06.714 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
10:32:06.715 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
10:32:06.717 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
10:32:06.719 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
10:32:10.892 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:32:10.975 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 22872 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:32:10.977 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:32:12.860 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:32:13.874 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:32:13.878 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:32:13.878 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:32:13.973 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:32:14.126 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:32:14.287 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:32:16.171 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:32:16.188 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:32:16.341 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:32:16.431 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:32:16.452 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:32:16.618 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:32:16.671 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 773229ms : this is harmless.
10:32:16.695 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:32:16.698 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:32:16.738 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:32:16.740 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:32:16.740 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:32:16.988 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:32:17.029 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:32:17.376 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:32:18.584 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b5f8707
10:32:18.921 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:32:18.960 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 8.694 seconds (process running for 9.676)
10:32:18.965 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:32:18.965 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:32:18.965 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:32:18.966 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:32:18.966 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:32:18.966 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:32:18.966 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:32:19.295 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:32:19.618 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@36299b83
10:32:19.621 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:32:20.151 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] -     |===========================================
    |--- 当前python版本为: Python 3.11.2
    |===========================================

10:32:20.158 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 0.5      | 2      | 10.0     |
|------------------------------------------------------------------|

10:33:09.588 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:33:09.674 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 19044 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:33:09.675 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:33:11.577 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:33:12.730 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:33:12.733 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:33:12.734 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:33:12.834 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:33:12.981 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:33:13.153 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:33:15.125 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:33:15.148 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:33:15.329 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:33:15.427 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:33:15.462 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:33:15.614 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:33:15.669 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 773229ms : this is harmless.
10:33:15.695 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:33:15.698 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:33:15.737 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:33:15.740 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:33:15.740 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:33:15.977 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:33:16.013 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:33:16.356 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:33:17.443 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b5f8707
10:33:17.774 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:33:17.810 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 8.775 seconds (process running for 9.638)
10:33:18.116 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:33:18.435 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3380313d
10:33:18.438 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:33:18.959 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] -     |===========================================
    |--- 当前python版本为: Python 3.11.2
    |===========================================

10:33:18.959 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:33:18.959 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:33:18.960 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:33:18.960 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:33:18.960 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:33:18.960 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:33:18.960 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:33:18.967 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 0.5      | 2      | 10.0     |
|------------------------------------------------------------------|

10:33:44.479 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:33:44.563 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 57324 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:33:44.564 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:33:46.482 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:33:47.502 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:33:47.505 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:33:47.506 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:33:47.605 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:33:47.779 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:33:47.946 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:33:49.841 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:33:49.859 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:33:50.013 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:33:50.112 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:33:50.145 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:33:50.294 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:33:50.348 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 773229ms : this is harmless.
10:33:50.379 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:33:50.382 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:33:50.433 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:33:50.436 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:33:50.437 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:33:50.701 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:33:50.748 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:33:51.156 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:33:52.354 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b5f8707
10:33:52.688 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:33:52.726 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 8.762 seconds (process running for 9.708)
10:33:52.731 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:33:52.731 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:33:52.731 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:33:52.731 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:33:52.731 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:33:52.732 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:33:52.732 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:33:53.172 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] -     |===========================================
    |--- 当前python版本为: Python 3.11.2
    |===========================================

10:33:53.478 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:33:53.812 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@653c0c9c
10:33:53.814 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:34:57.229 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 0.5      | 2      | 10.0     |
|------------------------------------------------------------------|

10:37:30.080 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:37:30.157 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 59636 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:37:30.159 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:37:31.878 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:37:32.849 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:37:32.852 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:37:32.852 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:37:32.939 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:37:33.069 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:37:33.227 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:37:35.032 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:37:35.052 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:37:35.220 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:37:35.322 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:37:35.342 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:37:35.499 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:37:35.559 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 773229ms : this is harmless.
10:37:35.586 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:37:35.588 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:37:35.628 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:37:35.631 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:37:35.631 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:37:35.870 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:37:35.907 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:37:36.261 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:37:37.358 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b5f8707
10:37:37.698 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:37:37.736 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 8.237 seconds (process running for 9.167)
10:37:37.741 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:37:37.741 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:37:37.741 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:37:37.741 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:37:37.741 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:37:37.742 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:37:37.742 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:37:38.067 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:37:38.392 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5e7fc012
10:37:38.394 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:37:38.919 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] -     |===========================================
    |--- 当前python版本为: Python 3.11.2
    |===========================================

10:37:49.272 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 0.5      | 2      | 20.0     |
|------------------------------------------------------------------|

10:39:05.027 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:39:05.030 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:39:05.111 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:39:05.117 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:39:05.148 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
10:39:05.148 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
10:39:05.177 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
10:39:05.178 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
10:39:05.180 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
10:39:05.182 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
10:39:08.704 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:39:08.779 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 20020 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:39:08.781 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:39:10.606 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:39:11.598 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:39:11.601 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:39:11.602 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:39:11.691 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:39:11.821 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:39:11.985 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:39:13.812 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:39:13.829 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:39:13.983 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:39:14.071 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:39:14.093 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:39:14.251 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:39:14.301 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1191686ms : this is harmless.
10:39:14.328 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:39:14.331 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:39:14.370 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:39:14.372 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:39:14.373 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:39:14.614 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:39:14.652 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:39:14.984 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:39:16.072 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b5f8707
10:39:16.400 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:39:16.436 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 8.23 seconds (process running for 9.098)
10:39:16.763 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:39:17.090 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@51c0346d
10:39:17.093 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:39:17.177 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:39:17.177 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:39:17.178 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:39:17.179 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:39:17.179 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:39:17.179 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:39:17.179 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:39:17.608 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] -      
     |===========================================
     |--- 当前python版本为: Python 3.11.2
     |===========================================
 
10:39:17.615 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

10:40:00.001 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
10:40:00.126 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
10:40:24.008 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:40:24.074 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:40:24.101 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:40:24.149 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:40:24.238 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
10:40:24.239 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
10:40:24.267 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
10:40:24.268 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
10:40:24.269 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
10:40:24.272 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
10:40:27.785 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:40:27.856 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 35292 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:40:27.858 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:40:29.572 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:40:30.549 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:40:30.552 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:40:30.553 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:40:30.647 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:40:30.797 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:40:30.947 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:40:32.768 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:40:32.787 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:40:32.948 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:40:33.039 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:40:33.062 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:40:33.216 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:40:33.270 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1270777ms : this is harmless.
10:40:33.297 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:40:33.300 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:40:33.337 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:40:33.340 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:40:33.341 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:40:33.563 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:40:33.606 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:40:33.920 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:40:35.004 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b5f8707
10:40:35.325 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:40:35.358 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 8.063 seconds (process running for 8.935)
10:40:35.362 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:40:35.362 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:40:35.362 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:40:35.363 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:40:35.363 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:40:35.363 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:40:35.363 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:40:35.676 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:40:35.977 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6cf34b88
10:40:35.979 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:40:36.484 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] -  |===========================================
 |--- 当前python版本为: Python 3.11.2
 |===========================================

10:40:36.491 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

10:41:08.824 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:41:08.852 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:41:08.906 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:41:08.930 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:41:08.955 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
10:41:08.956 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
10:41:08.979 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
10:41:08.980 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
10:41:08.982 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
10:41:08.984 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
10:41:12.446 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:41:12.514 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 28744 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:41:12.516 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:41:14.274 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:41:15.271 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:41:15.275 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:41:15.275 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:41:15.366 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:41:15.498 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:41:15.653 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:41:17.504 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:41:17.522 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:41:17.679 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:41:17.769 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:41:17.791 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:41:17.956 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:41:18.014 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1315494ms : this is harmless.
10:41:18.038 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:41:18.040 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:41:18.078 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:41:18.080 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:41:18.081 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:41:18.312 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:41:18.351 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:41:18.684 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:41:19.764 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b5f8707
10:41:20.108 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:41:20.145 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 8.202 seconds (process running for 9.067)
10:41:20.474 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:41:20.795 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1f50fe84
10:41:20.798 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:41:21.320 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - 
 |===========================================
 |--- 当前python版本为: Python 3.11.2
 |===========================================

10:41:21.321 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:41:21.321 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:41:21.321 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:41:21.321 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:41:21.321 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:41:21.321 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:41:21.321 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:41:21.327 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

10:42:12.095 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:42:12.106 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:42:12.180 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:42:12.184 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:42:12.206 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
10:42:12.206 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
10:42:12.228 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
10:42:12.229 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
10:42:12.231 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
10:42:12.233 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
10:42:15.731 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:42:15.805 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 56436 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:42:15.807 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:42:17.541 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:42:18.549 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:42:18.552 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:42:18.553 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:42:18.651 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:42:18.803 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:42:18.954 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:42:20.850 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:42:20.868 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:42:21.015 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:42:21.104 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:42:21.126 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:42:21.284 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:42:21.342 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1378745ms : this is harmless.
10:42:21.365 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:42:21.367 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:42:21.408 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:42:21.410 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:42:21.410 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:42:21.642 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:42:21.679 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:42:22.020 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:42:23.116 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b5f8707
10:42:23.435 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:42:23.472 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 8.232 seconds (process running for 9.106)
10:42:23.921 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
10:42:23.921 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
10:42:23.921 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
10:42:23.922 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:42:23.922 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:42:23.922 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:42:23.922 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:42:23.922 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:42:23.922 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:42:23.922 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:42:24.223 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:42:24.574 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@661d49d1
10:42:24.576 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:42:24.698 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

10:45:00.013 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
10:45:00.131 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
10:47:54.614 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:47:54.674 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:47:54.714 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:47:54.747 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:47:54.774 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
10:47:54.774 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
10:47:54.798 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
10:47:54.799 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
10:47:54.801 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
10:47:54.804 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
10:47:57.570 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:47:57.649 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 19928 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:47:57.651 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:47:59.643 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:48:00.667 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:48:00.671 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:48:00.672 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:48:00.765 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:48:00.944 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:48:01.231 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:48:03.152 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:48:03.171 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:48:03.324 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:48:03.419 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:48:03.458 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:48:03.603 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:48:03.653 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1721312ms : this is harmless.
10:48:03.684 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:48:03.687 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:48:03.728 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:48:03.731 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:48:03.731 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:48:03.987 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:48:04.028 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:48:04.418 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:48:05.595 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3add81c4
10:48:05.980 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:48:06.024 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 9.207 seconds (process running for 10.392)
10:48:06.460 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
10:48:06.460 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
10:48:06.460 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
10:48:06.780 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:48:07.101 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7271320c
10:48:07.103 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:48:07.187 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:48:07.187 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:48:07.188 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:48:07.188 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:48:07.188 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:48:07.188 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:48:07.188 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:48:07.196 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

10:50:00.000 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
10:50:00.125 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
10:54:19.375 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:54:19.454 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
10:54:19.466 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:54:19.535 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
10:54:19.563 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
10:54:19.564 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
10:54:19.587 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
10:54:19.588 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
10:54:19.590 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
10:54:19.592 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
10:54:22.084 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
10:54:22.170 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 32972 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
10:54:22.172 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
10:54:24.037 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:54:25.089 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
10:54:25.093 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
10:54:25.094 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
10:54:25.202 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
10:54:25.374 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
10:54:25.597 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
10:54:27.586 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
10:54:27.612 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
10:54:27.791 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
10:54:27.906 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
10:54:27.932 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
10:54:28.121 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
10:54:28.176 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2106102ms : this is harmless.
10:54:28.200 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
10:54:28.202 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
10:54:28.240 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
10:54:28.242 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
10:54:28.242 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
10:54:28.477 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
10:54:28.518 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
10:54:28.971 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
10:54:30.165 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3add81c4
10:54:30.567 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
10:54:30.611 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 9.012 seconds (process running for 10.042)
10:54:31.040 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
10:54:31.374 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@33f059ad
10:54:31.376 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
10:54:31.469 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
10:54:31.470 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
10:54:31.470 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
10:54:31.470 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
10:54:31.470 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
10:54:31.470 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
10:54:31.470 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
10:54:31.973 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
10:54:31.973 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
10:54:31.973 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
10:54:31.984 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

10:55:00.010 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
10:55:00.109 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
10:55:02.345 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：0
11:25:47.253 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
11:25:47.331 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 56548 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
11:25:47.333 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
11:25:49.365 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
11:25:50.414 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
11:25:50.418 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
11:25:50.419 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
11:25:50.507 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
11:25:50.687 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
11:25:50.869 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
11:25:52.678 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
11:25:53.040 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@771cbd13
11:25:53.043 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
11:25:53.108 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
11:25:53.474 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.151s)
11:25:53.802 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 0
11:25:53.889 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Migrating schema "mgr" to version "1 - Initial schema"
11:25:54.350 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
11:25:54.358 [main] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
11:25:54.518 [main] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
11:28:14.498 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
11:28:14.591 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 58732 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
11:28:14.593 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
11:28:22.355 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
11:28:22.438 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 38364 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
11:28:22.440 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
11:28:24.437 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
11:28:25.487 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
11:28:25.492 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
11:28:25.492 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
11:28:25.585 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
11:28:25.726 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
11:28:25.903 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
11:28:27.209 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
11:28:27.564 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3e489ac1
11:28:27.566 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
11:28:27.626 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
11:28:27.991 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.148s)
11:28:28.329 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 0
11:28:28.416 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Migrating schema "mgr" to version "1 - Initial schema"
11:28:34.115 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
11:28:34.121 [main] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
11:28:34.123 [main] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
11:41:07.532 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
11:41:07.616 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 54888 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
11:41:07.618 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
11:41:09.469 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
11:41:10.577 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
11:41:10.580 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
11:41:10.581 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
11:41:10.670 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
11:41:10.821 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
11:41:11.008 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
11:41:12.216 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
11:41:12.542 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@642c5bb3
11:41:12.545 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
11:41:12.602 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
11:41:12.972 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.169s)
11:41:13.295 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 0
11:41:13.391 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Migrating schema "mgr" to version "1 - Initial schema"
11:41:19.434 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
11:41:19.440 [main] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
11:41:19.442 [main] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
11:48:10.655 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
11:48:10.732 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 51892 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
11:48:10.735 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
11:48:12.494 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
11:48:13.500 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
11:48:13.503 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
11:48:13.504 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
11:48:13.590 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
11:48:13.726 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
11:48:13.888 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
11:48:15.002 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
11:48:15.318 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@70bc3a9c
11:48:15.321 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
11:48:15.379 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
11:48:15.701 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.130s)
11:48:16.007 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 0
11:48:16.089 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Migrating schema "mgr" to version "1 - Initial schema"
11:48:21.625 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
11:48:21.632 [main] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
11:48:21.634 [main] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
11:48:54.374 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
11:48:54.446 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 35256 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
11:48:54.447 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
11:48:56.422 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
11:48:57.488 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
11:48:57.492 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
11:48:57.492 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
11:48:57.583 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
11:48:57.726 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
11:48:57.876 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
11:48:59.066 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
11:48:59.409 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@771cbd13
11:48:59.411 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
11:48:59.471 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
11:48:59.822 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.139s)
11:49:00.158 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 0
11:49:00.238 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Migrating schema "mgr" to version "1 - Initial schema"
11:49:05.946 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
11:49:05.952 [main] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
11:49:05.954 [main] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
13:35:57.783 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
13:35:57.858 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 54924 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
13:35:57.860 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
13:35:59.741 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
13:36:00.811 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
13:36:00.815 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
13:36:00.816 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
13:36:00.912 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
13:36:01.053 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
13:36:01.210 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
13:36:02.348 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
13:36:02.673 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1425e531
13:36:02.676 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
13:36:02.734 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
13:36:03.064 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.131s)
13:36:03.375 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 0
13:36:03.455 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Migrating schema "mgr" to version "1 - Initial schema"
13:36:08.710 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
13:36:08.716 [main] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
13:36:08.718 [main] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
13:38:40.035 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
13:38:40.114 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 41036 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
13:38:40.116 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
13:38:42.133 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
13:38:43.301 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
13:38:43.307 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
13:38:43.308 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
13:38:43.413 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
13:38:43.587 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
13:38:43.774 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
13:38:45.155 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
13:38:45.553 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3e489ac1
13:38:45.556 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
13:38:45.632 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
13:38:46.015 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.151s)
13:38:46.353 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 0
13:38:46.444 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Migrating schema "mgr" to version "1 - Initial schema"
13:38:52.733 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Successfully applied 1 migration to schema "mgr", now at version v1 (execution time 00:05.833s)
13:38:54.048 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
13:38:54.070 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
13:38:54.244 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
13:38:54.350 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
13:38:54.371 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
13:38:54.543 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
13:38:54.677 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2106102ms : this is harmless.
13:38:54.702 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
13:38:54.705 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
13:38:54.749 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
13:38:54.752 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
13:38:54.753 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
13:38:55.027 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
13:38:55.066 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
13:38:55.452 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
13:38:56.718 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3add81c4
13:38:57.084 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
13:38:57.117 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 17.669 seconds (process running for 18.828)
13:38:57.825 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
13:38:57.825 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
13:38:57.825 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
13:38:58.242 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
13:38:58.242 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
13:38:58.242 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
13:38:58.242 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
13:38:58.243 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
13:38:58.243 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
13:38:58.243 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
13:38:58.251 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| N/A            | N/A              | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

13:40:00.584 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
13:40:00.697 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
13:45:00.889 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
13:45:00.944 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
13:50:00.009 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
13:50:00.065 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
13:50:13.100 [http-nio-8090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:50:13.433 [http-nio-8090-exec-1] INFO  c.r.n.m.s.s.LoginService - [loginByAccount,46] - [System|System] - 用户登录: admin
13:50:14.067 [http-nio-8090-exec-1] INFO  c.r.n.m.s.s.LoginService - [loginByAccount,89] - [System|System] - 用户登录成功: LoginOutput(accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jR6YAgnAKlGQDDSj9GhZVusnOI1Z5yOE4Ts-W_Bagko, refreshToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************.yGvhA6B06CnAJFf6qdrJf6P2A05AONevwU4OdYdZwiI, tokenType=Bearer, expiresIn=86400, userInfo=LoginOutput.UserInfo(userId=1945376569312702465, username=admin))
13:50:21.736 [http-nio-8090-exec-7] INFO  c.r.n.m.s.s.OnlineUserService - [getOnlineUsers,64] - [1945376569312702465|admin] - 当前在线用户数量: 1
13:50:53.021 [http-nio-8090-exec-1] INFO  c.r.n.m.l.c.LicenseV1Controller - [update,68] - [1945376569312702465|admin] - 更新License文件: LIC-NSE-License-01011947483058352230400_20250730_173219.lic
13:50:53.178 [http-nio-8090-exec-1] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,56] - [1945376569312702465|admin] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
13:50:53.492 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_THREAT_DETECTED, evtDetails=RuntimeDetectedService.SecurityStatus(debuggingDetected=true, jvmTampered=false, fileIntegrityViolated=false, memoryAnomalyDetected=false, networkAnomalyDetected=false, securityLevel=MEDIUM_RISK, lastCheckTime=1756360253052), remark=null, createdDate=2025-08-28T13:50:53.179744300, modifiedDate=2025-08-28T13:50:53.179744300, evtLevel=WARNING, evtMessage=安全事件检查)
13:51:41.270 [http-nio-8090-exec-10] INFO  c.r.n.m.s.s.OnlineUserService - [getOnlineUsers,64] - [1945376569312702465|admin] - 当前在线用户数量: 1
13:55:00.246 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
13:55:00.300 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
14:00:00.652 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
14:00:00.709 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
14:00:08.272 [http-nio-8090-exec-6] INFO  c.r.n.m.s.s.OnlineUserService - [getOnlineUsers,64] - [1945376569312702465|admin] - 当前在线用户数量: 1
14:05:00.001 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
14:05:00.085 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,54] - [System|系统] - ser_hosts表中没有记录，任务结束
14:05:25.324 [http-nio-8090-exec-1] INFO  c.r.n.m.l.c.LicenseV1Controller - [update,68] - [1945376569312702465|admin] - 更新License文件: 授权文件 (1).lic
14:05:25.356 [http-nio-8090-exec-1] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,56] - [1945376569312702465|admin] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
14:05:25.634 [http-nio-8090-exec-1] INFO  c.r.n.m.l.s.LicenseV1Service - [importLicense,177] - [1945376569312702465|admin] - License导入成功: LIC-NSE-License-05506EPUUuybApjLNUDlvotvy
14:05:26.372 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_THREAT_DETECTED, evtDetails=RuntimeDetectedService.SecurityStatus(debuggingDetected=true, jvmTampered=false, fileIntegrityViolated=false, memoryAnomalyDetected=false, networkAnomalyDetected=false, securityLevel=MEDIUM_RISK, lastCheckTime=1756361125356), remark=null, createdDate=2025-08-28T14:05:26.282399300, modifiedDate=2025-08-28T14:05:26.282399300, evtLevel=WARNING, evtMessage=安全事件检查)
14:06:36.086 [http-nio-8090-exec-10] INFO  c.r.n.m.s.c.EnvironmentController - [get,144] - [1945376569312702465|admin] - 当前NSE Server未启动，等待启动......
14:06:36.114 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [beforeLauncher,73] - [1945376569312702465|admin] - |--------- [NSE SERVER] 1945376569312702465 启动前检查通过 ---------|
14:06:36.131 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [launcher0,82] - [1945376569312702465|admin] - |--------- [NSE SERVER] 1945376569312702465 服务启动 ---------|
14:06:36.147 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [startPythonProcess,166] - [1945376569312702465|admin] - |--------- [NSE SERVER] 1945376569312702465 获取到可用端口：20000 ---------|
14:06:36.282 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,247] - [1945376569312702465|admin] - |--------- [NSE SERVER] 启动成功 ---------|
14:06:36.283 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,248] - [1945376569312702465|admin] - | 用户: 1945376569312702465
14:06:36.283 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,249] - [1945376569312702465|admin] - | PID: 10568
14:06:36.283 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,250] - [1945376569312702465|admin] - | 端口: 20000
14:06:36.283 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,251] - [1945376569312702465|admin] - | 最大内存: 55.0 GB
14:06:36.283 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,252] - [1945376569312702465|admin] - | CPU核心: 6
14:06:36.283 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,253] - [1945376569312702465|admin] - | 最大磁盘: 10.0 GB
14:06:36.284 [http-nio-8090-exec-10] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,254] - [1945376569312702465|admin] - |----------------------------------------|
14:06:36.441 [http-nio-8090-exec-10] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=4krvgDQIr5hbPPEB, secret=coypg0nmHDRWJQqYKlSSoHHcBZ3pCuZIOZlm8OSCCVtZK5bddOwzttf1Ye2CXCXC), pid=10568, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:06:37.454 [http-nio-8090-exec-10] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.12748344370860928, memoryTotal=**********, memoryUsage=3399680, diskTotal=**********, diskUsage=127866402)
14:06:50.070 [http-nio-8090-exec-1] INFO  c.r.n.m.p.a.ServerReachableAspect - [validateServerReachability,56] - [1945376569312702465|admin] - 用户 1945376569312702465 的 NSE 服务可达，服务地址: 127.0.0.1:20000
14:06:50.071 [http-nio-8090-exec-1] INFO  c.r.n.m.c.c.ExperimentController - [createExperiment,62] - [1945376569312702465|admin] - 创建实验，输入参数：ExperimentCreateInput(experimentType=布置作业, experimentName=2323, courseRepoId=, lessonId=1954782347642093569)
14:06:55.211 [http-nio-8090-exec-2] INFO  c.r.n.m.p.a.ServerReachableAspect - [validateServerReachability,56] - [1945376569312702465|admin] - 用户 1945376569312702465 的 NSE 服务可达，服务地址: 127.0.0.1:20000
14:06:55.212 [http-nio-8090-exec-2] INFO  c.r.n.m.c.c.ExperimentController - [createExperiment,62] - [1945376569312702465|admin] - 创建实验，输入参数：ExperimentCreateInput(experimentType=布置作业, experimentName=null, courseRepoId=, lessonId=1950815587817553921)
14:07:19.125 [http-nio-8090-exec-10] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=4krvgDQIr5hbPPEB, secret=coypg0nmHDRWJQqYKlSSoHHcBZ3pCuZIOZlm8OSCCVtZK5bddOwzttf1Ye2CXCXC), pid=10568, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:07:19.173 [http-nio-8090-exec-10] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.037067200297840656, memoryTotal=**********, memoryUsage=52965376, diskTotal=**********, diskUsage=127866402)
14:07:36.756 [http-nio-8090-exec-7] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=4krvgDQIr5hbPPEB, secret=coypg0nmHDRWJQqYKlSSoHHcBZ3pCuZIOZlm8OSCCVtZK5bddOwzttf1Ye2CXCXC), pid=10568, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:07:36.797 [http-nio-8090-exec-7] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.028610556536373687, memoryTotal=**********, memoryUsage=52912128, diskTotal=**********, diskUsage=127866402)
14:07:41.601 [http-nio-8090-exec-10] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=4krvgDQIr5hbPPEB, secret=coypg0nmHDRWJQqYKlSSoHHcBZ3pCuZIOZlm8OSCCVtZK5bddOwzttf1Ye2CXCXC), pid=10568, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:07:41.637 [http-nio-8090-exec-10] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.02672228079020947, memoryTotal=**********, memoryUsage=52912128, diskTotal=**********, diskUsage=127866402)
14:08:57.238 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_LIC_VERIFY_FAILED, evtDetails=FileNotFoundException: proc\uptime (系统找不到指定的路径。), remark=null, createdDate=2025-08-28T14:08:57.185649700, modifiedDate=2025-08-28T14:08:57.185649700, evtLevel=ERROR, evtMessage=License验证异常)
14:08:57.269 [scheduling-1] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,56] - [System|系统] - 事件消息发布成功: type=EVT_LIC_VERIFY_FAILED, message=License验证异常
14:08:57.269 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=false, status=500 INTERNAL_SERVER_ERROR, validationTime=Thu Aug 28 14:08:57 CST 2025, errorMessage=验证过程中发生异常: proc\uptime (系统找不到指定的路径。), details=[], extensions=null)
14:09:34.867 [http-nio-8090-exec-4] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=4krvgDQIr5hbPPEB, secret=coypg0nmHDRWJQqYKlSSoHHcBZ3pCuZIOZlm8OSCCVtZK5bddOwzttf1Ye2CXCXC), pid=10568, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:09:34.939 [http-nio-8090-exec-4] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.015381086131844275, memoryTotal=**********, memoryUsage=52932608, diskTotal=**********, diskUsage=127866402)
14:10:00.001 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
14:10:00.054 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
14:10:00.055 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
14:11:17.309 [http-nio-8090-exec-7] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=4krvgDQIr5hbPPEB, secret=coypg0nmHDRWJQqYKlSSoHHcBZ3pCuZIOZlm8OSCCVtZK5bddOwzttf1Ye2CXCXC), pid=10568, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:11:17.376 [http-nio-8090-exec-7] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.012391124199324961, memoryTotal=**********, memoryUsage=52932608, diskTotal=**********, diskUsage=127866402)
14:11:32.405 [http-nio-8090-exec-8] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=4krvgDQIr5hbPPEB, secret=coypg0nmHDRWJQqYKlSSoHHcBZ3pCuZIOZlm8OSCCVtZK5bddOwzttf1Ye2CXCXC), pid=10568, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:11:32.470 [http-nio-8090-exec-8] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.012023385180384533, memoryTotal=**********, memoryUsage=52932608, diskTotal=**********, diskUsage=127866402)
14:11:40.808 [http-nio-8090-exec-2] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=4krvgDQIr5hbPPEB, secret=coypg0nmHDRWJQqYKlSSoHHcBZ3pCuZIOZlm8OSCCVtZK5bddOwzttf1Ye2CXCXC), pid=10568, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:11:40.898 [http-nio-8090-exec-2] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.012102055084537138, memoryTotal=**********, memoryUsage=52916224, diskTotal=**********, diskUsage=127866402)
14:13:38.742 [http-nio-8090-exec-5] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=4krvgDQIr5hbPPEB, secret=coypg0nmHDRWJQqYKlSSoHHcBZ3pCuZIOZlm8OSCCVtZK5bddOwzttf1Ye2CXCXC), pid=10568, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:13:38.810 [http-nio-8090-exec-5] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.010830752047478307, memoryTotal=**********, memoryUsage=52985856, diskTotal=**********, diskUsage=127866402)
14:15:00.002 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
14:15:00.054 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
14:15:00.054 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
14:18:37.054 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：1
14:18:37.071 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：1
14:18:37.140 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
14:18:37.191 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,84] - [System|系统] - NSE Server进程缓存清理成功
14:18:37.395 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
14:18:37.396 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
14:18:37.429 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
14:18:37.430 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
14:18:37.431 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
14:18:37.433 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
14:23:16.851 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.2.Final
14:23:16.936 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarting,53] - [System|系统] - Starting NseMgrApplication using Java 21.0.6 with PID 16156 (C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-launcher\target\classes started by Administrator in C:\__ruijie_work_space\nse\nse-service)
14:23:16.938 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStartupProfileInfo,658] - [System|系统] - The following 1 profile is active: "dev"
14:23:19.073 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:23:20.271 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8090"]
14:23:20.275 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
14:23:20.275 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.34]
14:23:20.376 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
14:23:20.538 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
14:23:20.723 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
14:23:22.017 [main] INFO  c.z.h.HikariDataSource - [getConnection,109] - [System|系统] - HikariPool-1 - Starting...
14:23:22.346 [main] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - [System|系统] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@26888c31
14:23:22.349 [main] INFO  c.z.h.HikariDataSource - [getConnection,122] - [System|系统] - HikariPool-1 - Start completed.
14:23:22.405 [main] INFO  o.f.c.FlywayExecutor - [info,41] - [System|系统] - Database: **************************************************************************************************************** (PostgreSQL 13.12)
14:23:22.733 [main] INFO  o.f.c.i.c.DbValidate - [info,41] - [System|系统] - Successfully validated 2 migrations (execution time 00:00.134s)
14:23:23.043 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Current version of schema "mgr": 1
14:23:23.071 [main] INFO  o.f.c.i.c.DbMigrate - [info,41] - [System|系统] - Schema "mgr" is up to date. No migration necessary.
14:23:24.424 [main] INFO  c.r.n.c.c.c.EhcacheProperties - [init,49] - [System|系统] - EhcacheProperties 初始化完成，初始化参数: EhcacheProperties(storagePath=E:\GNS3_DATA\ehcache-storage-mgr, heap=50, offheap=100, disk=1024, jwtExpire=1440, serviceExpire=30)
14:23:24.450 [main] INFO  o.e.i.c.SizedResourcePoolImpl - [<init>,60] - [System|系统] - Byte based heap resources are deprecated and will be removed in a future version.
14:23:24.633 [main] INFO  o.e.s.f.AnnotationSizeOfFilter - [<clinit>,48] - [System|系统] - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
14:23:24.739 [main] INFO  o.e.s.i.JvmInformation - [<clinit>,356] - [System|系统] - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
14:23:24.764 [main] INFO  o.e.s.i.AgentLoader - [loadAgent,144] - [System|系统] - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
14:23:24.939 [main] INFO  o.e.s.o.t.o.p.UpfrontAllocatingPageSource - [<init>,149] - [System|系统] - Allocating 100.0MB in chunks
14:23:24.984 [main] INFO  o.e.i.i.s.d.OffHeapDiskStore - [info,186] - [System|系统] - {cache-alias=jwtTokenCache}The index for data file ehcache-disk-store.data is more recent than the data file itself by 173ms : this is harmless.
14:23:25.016 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'jwtTokenCache' created in EhcacheManager.
14:23:25.020 [main] INFO  o.e.c.EhcacheManager - [createCache,305] - [System|系统] - Cache 'serviceCache' created in EhcacheManager.
14:23:25.063 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=jwtTokenCache
14:23:25.067 [main] INFO  o.e.j.Eh107CacheManager - [registerObject,393] - [System|系统] - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=serviceCache
14:23:25.067 [main] INFO  c.r.n.c.c.c.EhcacheConfig - [jCacheCacheManager,65] - [System|系统] - Ehcache 缓存创建成功 'jwtTokenCache' 和 'serviceCache'
14:23:25.324 [main] INFO  o.a.c.c.StandardContext - [log,173] - [System|系统] - Suspicious URL pattern: [/**] in context [], see sections 12.1 and 12.2 of the Servlet specification
14:23:25.366 [main] INFO  c.r.n.c.w.f.LogMDCFilter - [init,19] - [System|系统] - LogMDCFilter 初始化完成
14:23:25.735 [main] INFO  c.r.n.m.p.l.m.GlobalShutdownHookManager - [registerShutdownHook,42] - [System|系统] - [Python3Server] - hook 注册成功！
14:23:27.073 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - [setApplicationContext,40] - [System|系统] - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3add81c4
14:23:27.869 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8090"]
14:23:27.922 [main] INFO  c.r.n.m.l.NseMgrApplication - [logStarted,59] - [System|系统] - Started NseMgrApplication in 11.688 seconds (process running for 12.797)
14:23:28.609 [main] INFO  c.r.n.m.c.s.EventPublishService - [publishEvent,56] - [System|系统] - 事件消息发布成功: type=EVT_THREAT_DETECTED, message=安全事件检查
14:23:28.771 [scheduling-1] INFO  c.r.n.m.l.s.EventVerifyRecordsService - [saveEventMessage,62] - [System|系统] - 事件消息已保存到数据库: EventVerifyRecords(evt=EVT_THREAT_DETECTED, evtDetails=RuntimeDetectedService.SecurityStatus(debuggingDetected=true, jvmTampered=false, fileIntegrityViolated=false, memoryAnomalyDetected=false, networkAnomalyDetected=false, securityLevel=MEDIUM_RISK, lastCheckTime=1756362208587), remark=null, createdDate=2025-08-28T14:23:28.609871900, modifiedDate=2025-08-28T14:23:28.609871900, evtLevel=WARNING, evtMessage=安全事件检查)
14:23:29.509 [main] INFO  c.r.n.m.l.c.LicenseVerifyRunner - [run,41] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Thu Aug 28 14:23:29 CST 2025, errorMessage=null, details=[], extensions=null)
14:23:29.510 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,28] - [System|系统] - | ================ 缓存配置验证开始 ================ |
14:23:29.510 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,31] - [System|系统] - | ✓ EhcacheProperties 已加载
14:23:29.510 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,32] - [System|系统] - |   - 存储路径: E:\GNS3_DATA\ehcache-storage-mgr
14:23:29.510 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,38] - [System|系统] - | ✓ CacheManager 已创建
14:23:29.510 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,39] - [System|系统] - |   - CacheManager 类型: org.springframework.cache.jcache.JCacheCacheManager
14:23:29.510 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,40] - [System|系统] - |   - 可用缓存名称: [jwtTokenCache, serviceCache]
14:23:29.511 [main] INFO  c.r.n.c.c.c.EhcacheConfigValidator - [run,45] - [System|系统] - | ================ 缓存配置验证结束 ================ |
14:23:33.249 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Thu Aug 28 14:23:33 CST 2025, errorMessage=null, details=[], extensions=null)
14:23:33.425 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,36] - [System|系统] - |===========================================
14:23:33.426 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,37] - [System|系统] - |--- 当前python版本为: Python 3.11.2
14:23:33.426 [main] INFO  c.r.n.m.p.c.Python3EnvironmentValidator - [run,38] - [System|系统] - |===========================================
14:23:33.437 [main] INFO  c.r.n.m.l.NseMgrApplication - [main,49] - [System|系统] - 

---------------------------------------------------------------------
应用 'nse-mgr             ' 运行成功! 当前环境 '[dev]     ' !!! 端口 '[8090]' !!!
---------------------------------------------------------------------
|  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
|----------------|------------------|----------|--------|----------|
| 50             | 5                | 55.0     | 6      | 10.0     |
|------------------------------------------------------------------|

14:23:38.293 [http-nio-8090-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:23:42.319 [http-nio-8090-exec-5] INFO  c.r.n.m.s.c.EnvironmentController - [get,144] - [1945376569312702465|admin] - 当前NSE Server未启动，等待启动......
14:23:42.347 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [beforeLauncher,73] - [1945376569312702465|admin] - |--------- [NSE SERVER] 1945376569312702465 启动前检查通过 ---------|
14:23:42.350 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [launcher0,82] - [1945376569312702465|admin] - |--------- [NSE SERVER] 1945376569312702465 服务启动 ---------|
14:23:42.372 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [startPythonProcess,166] - [1945376569312702465|admin] - |--------- [NSE SERVER] 1945376569312702465 获取到可用端口：20000 ---------|
14:23:42.580 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,247] - [1945376569312702465|admin] - |--------- [NSE SERVER] 启动成功 ---------|
14:23:42.581 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,248] - [1945376569312702465|admin] - | 用户: 1945376569312702465
14:23:42.582 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,249] - [1945376569312702465|admin] - | PID: 57660
14:23:42.582 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,250] - [1945376569312702465|admin] - | 端口: 20000
14:23:42.582 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,251] - [1945376569312702465|admin] - | 最大内存: 55.0 GB
14:23:42.582 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,252] - [1945376569312702465|admin] - | CPU核心: 6
14:23:42.583 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,253] - [1945376569312702465|admin] - | 最大磁盘: 10.0 GB
14:23:42.583 [http-nio-8090-exec-5] INFO  c.r.n.m.p.l.Python3ServerLauncher - [logServerStartSuccess,254] - [1945376569312702465|admin] - |----------------------------------------|
14:23:42.750 [http-nio-8090-exec-5] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=3xwdFlcWVZ7kKmLK, secret=GTNWJDDBqpIY0Amb6sEHYRyOXe7DdMEEle4az5QvoLVP9HuL5fFLvGJEh6UN8r46), pid=57660, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:23:44.525 [http-nio-8090-exec-5] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.6206395348837209, memoryTotal=**********, memoryUsage=51179520, diskTotal=**********, diskUsage=127866402)
14:25:00.971 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
14:25:01.029 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
14:25:01.030 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
14:28:28.713 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Thu Aug 28 14:28:28 CST 2025, errorMessage=null, details=[], extensions=null)
14:30:00.014 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
14:30:00.063 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
14:30:00.064 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
14:30:13.585 [http-nio-8090-exec-9] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=3xwdFlcWVZ7kKmLK, secret=GTNWJDDBqpIY0Amb6sEHYRyOXe7DdMEEle4az5QvoLVP9HuL5fFLvGJEh6UN8r46), pid=57660, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:30:13.627 [http-nio-8090-exec-9] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.011461903995051238, memoryTotal=**********, memoryUsage=52797440, diskTotal=**********, diskUsage=127866402)
14:31:09.054 [http-nio-8090-exec-6] INFO  c.r.n.m.s.s.OnlineUserService - [getOnlineUsers,64] - [1945376569312702465|admin] - 当前在线用户数量: 1
14:32:33.555 [http-nio-8090-exec-8] INFO  c.r.n.m.s.c.EnvironmentController - [get,147] - [1945376569312702465|admin] - 当前NSE Server信息: SerHosts(serverIp=127.0.0.1, serverPort=20000, basicAuth=BasicAuthBo(key=3xwdFlcWVZ7kKmLK, secret=GTNWJDDBqpIY0Amb6sEHYRyOXe7DdMEEle4az5QvoLVP9HuL5fFLvGJEh6UN8r46), pid=57660, maxMemory=55.0, maxDisk=10.0, cpuCore=6, userId=1945376569312702465, storagePath=F:\GNS3_DATA, currentMemory=null)
14:32:33.607 [http-nio-8090-exec-8] INFO  c.r.n.m.s.c.EnvironmentController - [get,151] - [1945376569312702465|admin] - 当前NSE Server资源情况: ProcessDto(cpuUsage=0.01002863441514066, memoryTotal=**********, memoryUsage=52797440, diskTotal=**********, diskUsage=127866402)
14:33:28.892 [scheduling-1] INFO  c.r.n.m.l.c.LicenseSchedulerConfig - [scheduledLicenseValidation,46] - [System|系统] - license auth result: LicenseValidationResult(valid=true, status=200 OK, validationTime=Thu Aug 28 14:33:28 CST 2025, errorMessage=null, details=[], extensions=null)
14:34:32.691 [http-nio-8090-exec-5] INFO  c.r.n.m.s.s.OnlineUserService - [getOnlineUsers,64] - [1945376569312702465|admin] - 当前在线用户数量: 1
14:35:00.007 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,47] - [System|系统] - 开始执行PID校验任务...
14:35:00.056 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,58] - [System|系统] - 找到 1 条ser_hosts记录，开始校验PID
14:35:00.056 [scheduling-1] INFO  c.r.n.m.j.j.PidValidationJob - [validateAndCleanupPids,87] - [System|系统] - PID校验任务完成，共清理 0 条无效记录
14:35:17.198 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：1
14:35:17.219 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,52] - [System|系统] - 释放所有服务主机资源，数量：1
14:35:17.408 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,78] - [System|系统] - 终止 NSE Server进程: PID=57660
14:35:17.408 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,78] - [System|系统] - 终止 NSE Server进程: PID=57660
14:35:17.408 [Python3Server-Shutdown-Hook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,88] - [System|系统] - NSE Server进程缓存清理成功
14:35:17.408 [SpringApplicationShutdownHook] INFO  c.r.n.m.p.l.c.Python3ServerContext - [releaseAllHosts,88] - [System|系统] - NSE Server进程缓存清理成功
14:35:17.642 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'jwtTokenCache' removed from EhcacheManager.
14:35:17.642 [SpringApplicationShutdownHook] INFO  o.e.c.EhcacheManager - [removeCache,216] - [System|系统] - Cache 'serviceCache' removed from EhcacheManager.
14:35:17.666 [SpringApplicationShutdownHook] INFO  c.r.n.c.c.c.EhcacheConfig - [destroy,73] - [System|系统] - Ehcache 缓存管理器已关闭，数据已持久化
14:35:17.667 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - [System|系统] - HikariPool-1 - Shutdown initiated...
14:35:17.669 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - [System|系统] - HikariPool-1 - Shutdown completed.
14:35:17.671 [SpringApplicationShutdownHook] INFO  c.r.n.c.w.f.LogMDCFilter - [destroy,51] - [System|系统] - LogMDCFilter 销毁完成
